<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:foreground="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical"
    android:paddingHorizontal="16dp"
    android:paddingVertical="16dp"
    android:layout_marginBottom="1dp">

    <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
    <TextView
        android:id="@+id/txtCabecalho"
        style="@style/TextAppearance.Material3.TitleMedium"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="?android:attr/textColorPrimary"
        tools:text="CAMPO DOS ALEMAES - CP 788" />

    <!-- Subtítulo -->
    <TextView
        android:id="@+id/txtCabecalho2"
        style="@style/TextAppearance.Material3.BodyMedium"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="?android:attr/textColorSecondary"
        tools:text="ET 648563 - ENGELMIG - SJC" />

    <!-- Lista de Serviços -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvOrdens"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:nestedScrollingEnabled="false" />

</LinearLayout>