[versions]
agp = "8.12.0"
ksp = "1.9.0-1.0.13"

glide = "4.15.1"
gradle = "8.5"
kotlin = "1.9.0"
coreKtx = "1.15.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
kotlinGradlePlugin = "1.9.0"
lifecycleRuntimeKtx = "2.8.7"
activityCompose = "1.9.3"
composeBom = "2024.04.01"
recyclerview = "1.3.2"
baselibrary = "3.2.0-alpha11"
appcompat = "1.7.0"
material = "1.12.0"
compiler = "4.15.1"
retrofit = "2.9.0"
gson = "2.9.0"
gms = "20.0.0"
shimmer = "0.5.0"
loadingbutton = "2.3.0"
rygelouvLoadingButton = "1.2.0"
room = "2.6.1"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }

androidx-shimmer = { group = "com.facebook.shimmer", name = "shimmer", version.ref = "shimmer" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
gradle = { module = "com.android.tools.build:gradle", version.ref = "gradle" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
androidx-recyclerview = { group = "androidx.recyclerview", name = "recyclerview", version.ref = "recyclerview" }
androidx-baselibrary = { group = "androidx.databinding", name = "baseLibrary", version.ref = "baselibrary" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlinGradlePlugin" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
lottie = { group = "com.airbnb.android", name = "lottie", version = "6.0.0" }
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
retrofit-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "gson" }
photoView = { module = "com.github.chrisbanes:PhotoView", version = "2.3.0" }
compiler = { module = "com.github.bumptech.glide:compiler", version = "compiler" }
play-services-location = { group = "com.google.android.gms", name = "play-services-location", version.ref = "gms" }
loading-button = { module = "com.github.leandroborgesferreira:loading-button-android", version.ref = "loadingbutton" }
rygelouv-loading-button = { module = "com.github.rygelouv:Android-LoadingButton", version.ref = "rygelouvLoadingButton" }
room-runtime = { module = "androidx.room:room-runtime", version.ref = "room" }
room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
room-ktx = { module = "androidx.room:room-ktx", version.ref = "room" }



[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }

