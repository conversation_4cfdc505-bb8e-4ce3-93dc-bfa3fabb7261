package com.baramaia.hercules;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.models.OrdemServico;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class DescricaoNsAdapter extends RecyclerView.Adapter<DescricaoNsAdapter.ViewHolder> {

    private List<OrdemServico> ordens;
    private List<Boolean> selectedStates;
    private int selectedPosition = -1; // Posição do item selecionado

    public DescricaoNsAdapter(List<OrdemServico> ordens) {
        this.ordens = ordens;
        this.selectedStates = new ArrayList<>(Collections.nCopies(ordens.size(), false));
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.row_ns, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        OrdemServico ordem = ordens.get(position);
        String texto = "";
        if (ordem.getOrd_pos3()!=null){
            texto = ("CS: " + ordem.getOrd_cs() + " - POS: " + ordem.getOrd_pos1() + ", "+ ordem.getOrd_pos2() + ", "+ ordem.getOrd_pos3());
        }
        else{
            texto = ("CS: " + ordem.getOrd_cs() + " - POS: " + ordem.getOrd_pos1() + ", "+ ordem.getOrd_pos2());
        }
        holder.radioOrdem.setText(texto);
        holder.radioOrdem.setChecked(selectedStates.get(position));
        // Desmarcar ao clicar novamente no mesmo botão
        holder.radioOrdem.setOnClickListener(v -> {
            boolean isSelected = !selectedStates.get(position); // Alterna o estado
            selectedStates.set(position, isSelected);          // Atualiza o estado na lista
            notifyDataSetChanged();                            // Atualiza a lista
        });
    }

    @Override
    public int getItemCount() {
        return ordens.size();
    }

    // Método para recuperar os itens selecionados
    public List<Integer> getSelectedIds() {
        List<Integer> selectedIds = new ArrayList<>();
        for (int i = 0; i < selectedStates.size(); i++) {
            if (selectedStates.get(i)) {
                selectedIds.add(ordens.get(i).getId()); // Adiciona o atributo ID
            }
        }
        return selectedIds;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public RadioButton radioOrdem;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            radioOrdem = itemView.findViewById(R.id.radioOrdem);
        }
    }
}
