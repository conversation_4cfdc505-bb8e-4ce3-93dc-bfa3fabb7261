package com.baramaia.hercules.adapters;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.baramaia.hercules.fragments.TabConfirmacaoFragment;
import com.baramaia.hercules.fragments.TabInfoBasicasFragment;
import com.baramaia.hercules.fragments.TabInfoTecnicasFragment;
// Removido TabLocalizacao (fluxo simplificado)

public class TabPagerAdapter extends FragmentStateAdapter {

    private static final int NUM_TABS = 2;

    public TabPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return new TabInfoBasicasFragment(); // Informações Completas (Motivo + Técnicas + Empreiteiras)
            case 1:
                return new TabConfirmacaoFragment(); // Confirmar
            default:
                return new TabInfoBasicasFragment();
        }
    }

    @Override
    public int getItemCount() {
        return NUM_TABS;
    }
}
