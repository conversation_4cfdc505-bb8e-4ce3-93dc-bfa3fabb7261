package com.baramaia.hercules;

import android.app.Dialog;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.button.MaterialButton;

public class NotaServicoDetalheActivity extends AppCompatActivity {
    private Button btnRegistrar;
    private LinearLayout linearColumnarrowleft;
    private TextView txtNsTitle;
    private TextView txtIdTitle;
    private TextView txtNome;
    private TextView txtLogradouro;
    private TextView txtComplemento;
    private TextView txtUc;
    private TextView txtEt;
    private TextView txtCs;
    private TextView txtPosicoes;
    private TextView txtModulo;
    private TextView txtDisplay;
    private TextView txtServico;
    private TextView txtMotivo;
    private TextView txtObs;
    private TextView txtGpsHint;
    private MaterialButton btnGps;
    private int ordId;
    private int et;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_notas_de_servi_o_detalhes);

        Intent intent = getIntent();
        ordId = intent.getIntExtra("ORDEM_ID", -1);
        String ns = intent.getStringExtra("ORDEM_NS");
        int cliId = intent.getIntExtra("ORDEM_CLID", -1);
        String nome = intent.getStringExtra("ORDEM_NOME");
        String logradouro = intent.getStringExtra("ORDEM_LOG");
        String numero = intent.getStringExtra("ORDEM_NUM");
        String complemento = intent.getStringExtra("ORDEM_COMP");
        int uc = intent.getIntExtra("ORDEM_UC", -1);
        et = intent.getIntExtra("ORDEM_ET", -1);
        int cs = intent.getIntExtra("ORDEM_CS", -1);
        String pos1 = intent.getStringExtra("ORDEM_POS1");
        String pos2 = intent.getStringExtra("ORDEM_POS2");
        String pos3 = intent.getStringExtra("ORDEM_POS3");
        int medidor = intent.getIntExtra("ORDEM_MED", -1);
        String display = intent.getStringExtra("ORDEM_DISP");
        String servico = intent.getStringExtra("ORDEM_SERV");
        String motivo = intent.getStringExtra("ORDEM_MOT");
        String observacao = intent.getStringExtra("ORDEM_OBS");
        String gps = intent.getStringExtra("ORDEM_GPS");

        // Referência para o botão
        //Button btnCopyText = findViewById(R.id.btnCopyText);
        btnRegistrar = findViewById(R.id.btnRegistrar);
        btnGps = findViewById(R.id.btnGps);
        txtGpsHint = findViewById(R.id.txtGpsHint);
        txtNsTitle = findViewById(R.id.txtNsTitle);
        txtIdTitle = findViewById(R.id.txtIdTitle);
        txtNome = findViewById(R.id.txtNome);
        txtLogradouro = findViewById(R.id.txtLogradouro);
        txtComplemento = findViewById(R.id.txtComplemento);
        txtUc = findViewById(R.id.txtUc);
        txtEt = findViewById(R.id.txtEt);
        txtCs = findViewById(R.id.txtCs);
        txtPosicoes = findViewById(R.id.txtPosicoes);
        txtModulo = findViewById(R.id.txtModulo);
        txtDisplay = findViewById(R.id.txtDisplay);
        txtServico = findViewById(R.id.txtServico);
        txtMotivo = findViewById(R.id.txtMotivo);
        txtObs = findViewById(R.id.txtObs);

        txtNsTitle.setText("NS " + ns);
        txtIdTitle.setText("ID " + String.valueOf(cliId));
        txtNome.setText(nome);
        txtLogradouro.setText(logradouro + ", " + numero);
        txtComplemento.setText(complemento);
        if (uc != 0) {
            txtUc.setText("UC " + String.valueOf(uc));
        }
        if (uc != 0) {
            txtUc.setText("");
        }
        txtEt.setText("ET " + String.valueOf(et));
        txtCs.setText("CS " + String.valueOf(cs));
        if (pos1 != null && pos2 != null && pos3 != null) {
            txtPosicoes.setText(pos1 + " - " + pos2 + " - " + pos3);
        }
        if (pos1 != null && pos2 != null && pos3 == null) {
            txtPosicoes.setText(pos1 + " - " + pos2);
        }
        if (pos1 != null && pos2 == null && pos3 == null) {
            txtPosicoes.setText(pos1);
        }
        txtModulo.setText("Medidor: " + String.valueOf(medidor));
        txtDisplay.setText("Display: " + display);
        txtServico.setText(servico);
        txtMotivo.setText(motivo);

        // Verifica se tem observação e controla a visibilidade
        if (observacao != null && !observacao.trim().isEmpty()) {
            txtObs.setVisibility(View.VISIBLE);
            txtObs.setText(observacao);
        } else {
            txtObs.setVisibility(View.GONE);
        }

        // Configurar GPS e hint
        if (gps != null) {
            btnGps.setVisibility(View.VISIBLE);
            txtGpsHint.setVisibility(View.VISIBLE);
            btnGps.setOnClickListener(view -> {
                copiarParaAreaDeTransferencia(gps);
                // Feedback visual opcional
                txtGpsHint.setText("GPS Copiado!");
                txtGpsHint.postDelayed(() -> txtGpsHint.setText("Copiar GPS"), 2000);
            });
        } else {
            btnGps.setVisibility(View.GONE);
            txtGpsHint.setVisibility(View.GONE);
        }

        linearColumnarrowleft = findViewById(R.id.linearColumnarrowleft);

        btnRegistrar.setOnClickListener(view -> showConfirmDialog());
        linearColumnarrowleft.bringToFront();
        linearColumnarrowleft.setOnClickListener(view -> finish());
        //linearColumnarrowleft.setOnClickListener(view -> finish());
    }

    private void copiarParaAreaDeTransferencia(String texto) {
        ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("Texto Copiado", texto);
        clipboard.setPrimaryClip(clip);

        // Exibir uma mensagem para o usuário
        //Toast.makeText(this, "Texto copiado para a área de transferência", Toast.LENGTH_SHORT).show();
    }

    private void showConfirmDialog() {
        Dialog dialog = new Dialog(this, R.style.DialogTheme);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_confirmar_registro);

        Window window = dialog.getWindow();
        if (window != null) {
            window.setLayout(WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT);
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }

        Button btnSim = dialog.findViewById(R.id.btnSim);
        Button btnNo = dialog.findViewById(R.id.btnNo);

        btnSim.setOnClickListener(v -> {
            dialog.dismiss();
            Intent intentConfirma = new Intent(NotaServicoDetalheActivity.this, DescricaoActivity.class);
            intentConfirma.putExtra("ordemId", ordId);
            intentConfirma.putExtra("et", et);

            startActivity(intentConfirma);
            finish();
        });

        btnNo.setOnClickListener(v -> {
            dialog.dismiss();
            Intent intentVisita = new Intent(NotaServicoDetalheActivity.this, RegistroNaoActivity.class);
            intentVisita.putExtra("ordemId", ordId);
            intentVisita.putExtra("et", et);
            startActivity(intentVisita);
            finish();
        });

        dialog.show();
    }
}