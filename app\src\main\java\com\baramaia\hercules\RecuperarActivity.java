package com.baramaia.hercules;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.widget.Button;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import com.github.leandroborgesferreira.loadingbutton.customViews.CircularProgressButton;
import com.google.android.material.textfield.TextInputEditText;

public class RecuperarActivity extends AppCompatActivity {
    private CircularProgressButton btnEnviar;
    private TextInputEditText etEnteruserOne;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_recuperar);

        btnEnviar = findViewById(R.id.btnEnviar);
        etEnteruserOne = findViewById(R.id.etEnteruserOne);

        btnEnviar.setOnClickListener(view -> {
            String email = etEnteruserOne.getText().toString().trim();
            if (email.isEmpty()) {
                Toast.makeText(this, "Por favor, digite seu e-mail", Toast.LENGTH_SHORT).show();
                return;
            }

            // Inicia animação
            btnEnviar.startAnimation();

            // Simula envio (adicionar chamada real da API depois)
            new Handler().postDelayed(() -> {
                Intent intent = new Intent(getApplicationContext(), LoginActivity.class);
                intent.putExtra("msg", "Verifique sua caixa de entrada!");
                startActivity(intent);
                // Não chamamos o revertAnimation() aqui para manter o loading até a troca de tela
                finish();
            }, 1500);
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Reverte o botão ao estado inicial quando voltar para a tela
        if (btnEnviar != null) {
            btnEnviar.revertAnimation();
        }
    }
}

/* txtRecuperar - Titulo REcuperar */

/* etEnteruserOne - EditText para digitar o usuario */

/* btnEnviar - Botão Enviar */