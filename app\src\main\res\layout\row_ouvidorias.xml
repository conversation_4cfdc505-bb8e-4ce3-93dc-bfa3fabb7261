<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="1dp"
    android:background="@color/white"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp"
    android:paddingVertical="16dp">

    <!-- Ícone (opcional) -->
    <!--    <ImageView-->
    <!--        android:id="@+id/iconOuvidoria"-->
    <!--        android:layout_width="24dp"-->
    <!--        android:layout_height="24dp"-->
    <!--        android:layout_gravity="center_vertical"-->
    <!--        android:layout_marginEnd="16dp"-->
    <!--        app:tint="?attr/colorPrimary"-->
    <!--        tools:src="@drawable/ic_notification" />-->

    <!-- Conteúdo -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/txtHeaderText"
            style="@style/TextAppearance.Material3.BodyLarge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?android:attr/textColorPrimary"
            tools:text="NA 123456" />

        <TextView
            android:id="@+id/txtSubheadText"
            style="@style/TextAppearance.Material3.BodyMedium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="?android:attr/textColorSecondary"
            tools:text="CRUZEIRO" />
    </LinearLayout>

    <!-- Seta -->
    <!--    <ImageView-->
    <!--        android:layout_width="24dp"-->
    <!--        android:layout_height="24dp"-->
    <!--        android:layout_gravity="center_vertical"-->
    <!--        android:layout_marginStart="16dp"-->
    <!--        android:src="@drawable/ic_chevron_right"-->
    <!--        app:tint="?android:attr/textColorSecondary" />-->

</LinearLayout>
