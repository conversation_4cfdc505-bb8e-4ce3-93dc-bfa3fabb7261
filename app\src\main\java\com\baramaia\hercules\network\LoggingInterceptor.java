package com.baramaia.hercules.network;

import android.util.Log;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;

public class LoggingInterceptor implements Interceptor {
    private static final String TAG = "API_DEBUG";
    private static final Charset UTF8 = StandardCharsets.UTF_8;

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        
        // Log da requisição
        RequestBody requestBody = request.body();
        String requestBodyString = "";
        if (requestBody != null) {
            Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            
            MediaType contentType = requestBody.contentType();
            if (contentType != null) {
                requestBodyString = buffer.readString(contentType.charset(UTF8));
            } else {
                requestBodyString = buffer.readString(UTF8);
            }
        }
        
        Log.d(TAG, "Request: " + request.method() + " " + request.url());
        Log.d(TAG, "Headers: " + request.headers());
        Log.d(TAG, "Body: " + requestBodyString);
        
        // Executar a requisição
        long startTime = System.currentTimeMillis();
        Response response = chain.proceed(request);
        long endTime = System.currentTimeMillis();
        
        // Log da resposta
        ResponseBody responseBody = response.body();
        String responseBodyString = "";
        if (responseBody != null) {
            responseBodyString = responseBody.string();
            // Recriar o corpo da resposta, já que ele foi consumido ao ler a string
            response = response.newBuilder()
                    .body(ResponseBody.create(responseBody.contentType(), responseBodyString))
                    .build();
        }
        
        Log.d(TAG, "Response: " + response.code() + " " + response.message());
        Log.d(TAG, "Headers: " + response.headers());
        Log.d(TAG, "Body: " + responseBodyString);
        Log.d(TAG, "Time: " + (endTime - startTime) + "ms");
        
        return response;
    }
}
