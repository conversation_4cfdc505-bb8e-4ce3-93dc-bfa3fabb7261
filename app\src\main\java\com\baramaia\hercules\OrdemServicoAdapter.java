package com.baramaia.hercules;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.models.ViewOrdemServico;

import java.util.List;

public class OrdemServicoAdapter extends RecyclerView.Adapter<OrdemServicoAdapter.OrdemServicoViewHolder> {

    private final List<ViewOrdemServico> ordemServicoList;
    private final OnItemClickListener listener;

    public interface OnItemClickListener {
        void onItemClick(ViewOrdemServico ordemServico);
    }
    public OrdemServicoAdapter(List<ViewOrdemServico> ordemServicoList, OnItemClickListener listener) {
        this.ordemServicoList = ordemServicoList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public OrdemServicoViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.row_servico, parent, false);
        return new OrdemServicoViewHolder(view);
    }

    @Override
    public void onBindViewHolder(OrdemServicoViewHolder holder, int position) {
        ViewOrdemServico ordemServico = ordemServicoList.get(position);
        String texto = "CS " + ordemServico.getOrd_cs() + " - " + ordemServico.getMotivo();
        holder.txtOrdem.setText(texto);

        holder.itemView.setOnClickListener(v -> listener.onItemClick(ordemServico));
    }

    @Override
    public int getItemCount() {
        return ordemServicoList.size();
    }

    static class OrdemServicoViewHolder extends RecyclerView.ViewHolder {
        TextView txtOrdem;

        public OrdemServicoViewHolder(View itemView) {
            super(itemView);
            txtOrdem = itemView.findViewById(R.id.txtOrdem);
        }
    }
}

