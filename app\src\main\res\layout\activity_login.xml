<?xml version="1.0" encoding="UTF-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">
    <LinearLayout
        android:id="@+id/linearColumn"
        style="@style/groupStyleblue_gray_50"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:context=".LoginActivity">
        <LinearLayout
            android:id="@+id/linearColumncimalogin"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <FrameLayout
                android:id="@+id/frameStacklogin"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_345pxv"
                android:gravity="center_horizontal">
                <FrameLayout
                    android:id="@+id/frameStackcimalogin"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_327pxv"
                    android:layout_gravity="top|center">
                    <ImageView
                        android:id="@+id/imageCimaloginOne"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_327pxh"
                        android:layout_gravity="center"
                        android:scaleType="fitXY"
                        android:src="@drawable/img_cima_login"
                        tools:ignore="ContentDescription" />
                    <LinearLayout
                        android:id="@+id/linearRowdiamantebran"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top|end"
                        android:layout_marginTop="@dimen/_84pxv"
                        android:layout_marginEnd="@dimen/_35pxh"
                        android:gravity="end"
                        android:orientation="horizontal">
                        <ImageView
                            android:id="@+id/imageDiamantebranco"
                            android:layout_width="@dimen/_57pxh"
                            android:layout_height="@dimen/_48pxh"
                            android:layout_gravity="center_vertical"
                            android:layout_marginTop="@dimen/_4pxv"
                            android:layout_marginBottom="@dimen/_1pxv"
                            android:scaleType="fitXY"
                            android:src="@drawable/img_diamante_branco"
                            tools:ignore="ContentDescription" />
                        <TextView
                            android:id="@+id/txtHrcules"
                            style="@style/txtRobotomedium46"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_10pxh"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:singleLine="true"
                            android:text="@string/lbl_h_rcules"
                            tools:text="@string/lbl_h_rcules" />
                    </LinearLayout>
                </FrameLayout>
                <TextView
                    android:id="@+id/txtLogin"
                    style="@style/txtRubikromanmedium24"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|start"
                    android:layout_marginStart="@dimen/_61pxh"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="@string/lbl_login"
                    tools:text="@string/lbl_login" />
            </FrameLayout>
            <ImageView
                android:id="@+id/imageLineeightOne"
                android:layout_width="@dimen/_40pxh"
                android:layout_height="@dimen/_3pxh"
                android:layout_gravity="start"
                android:layout_marginStart="@dimen/_61pxh"
                android:layout_marginTop="@dimen/_4pxv"
                android:scaleType="fitXY"
                android:src="@drawable/img_line_8"
                tools:ignore="ContentDescription" />
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="54dp"
                android:layout_marginEnd="57dp"
                android:layout_marginTop="21dp"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                app:endIconMode="custom"
                app:endIconDrawable="@drawable/mail">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etEnterwithuser"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/msg_entre_com_usuario"
                    android:inputType="text"
                    android:textColor="@color/blue_gray_800" />
            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="55dp"
                android:layout_marginEnd="56dp"
                android:layout_marginTop="25dp"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                app:endIconMode="password_toggle">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etPasswordOne"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/lbl_senha"
                    android:inputType="textPassword"
                    android:textColor="@color/blue_gray_800" />
            </com.google.android.material.textfield.TextInputLayout>
            <LinearLayout
                android:id="@+id/linearRowmelembre"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_54pxh"
                android:layout_marginTop="@dimen/_16pxv"
                android:layout_marginEnd="@dimen/_54pxh"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <!--<androidx.appcompat.widget.AppCompatCheckBox
                    android:id="@+id/checkBoxMeLembre"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:ellipsize="end"
                    android:fontFamily="@font/arial"
                    android:paddingTop="@dimen/_1pxv"
                    android:paddingBottom="@dimen/_1pxv"
                    android:singleLine="true"
                    android:text="@string/lbl_me_lembre"
                    android:textColor="@color/gray_900"
                    android:textFontWeight="400"
                    android:textSize="@dimen/_9pxh"
                    android:textStyle="bold"
                    app:buttonTint="@color/blue_gray_800"
                    tools:ignore="SpUsage"
                    tools:text="@string/lbl_me_lembre" />-->
                <TextView
                    android:id="@+id/txtEsqueceua"
                    style="@style/txtArialmt9_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_190pxh"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="@string/msg_esqueceu_a_senha"
                    tools:text="@string/msg_esqueceu_a_senha" />
            </LinearLayout>
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="68dp">
                <!-- Botão seguindo padrão da Nova Ordem -->
                <com.github.rygelouv.androidloadingbuttonlib.LoadingButton
                    android:id="@+id/btnEntrar"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginStart="54dp"
                    android:layout_marginEnd="57dp"
                    android:layout_gravity="center"
                    custom:text="@string/lbl_entrar"
                    custom:textColor="@color/white"
                    custom:textSize="16sp"
                    custom:progressColor="@color/white"
                    custom:background="@drawable/button_blue_gray_rounded" />
            </FrameLayout>
        </LinearLayout>
        <FrameLayout
            android:id="@+id/frameBottombar"
            style="@style/groupStylewhite_A700"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal">
            <LinearLayout
                android:id="@+id/linearColumnline"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingStart="@dimen/_25pxh"
                android:paddingEnd="@dimen/_25pxh">
                <View
                    android:id="@+id/lineLine"
                    android:layout_width="@dimen/_360pxh"
                    android:layout_height="@dimen/_1pxv"
                    android:background="@color/gray_300_01" />
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</ScrollView>