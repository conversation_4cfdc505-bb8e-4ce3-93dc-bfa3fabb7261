package com.baramaia.hercules;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.models.OrdemEt;
import com.baramaia.hercules.network.ApiCalls;
import com.baramaia.hercules.network.ApiDados;
import com.baramaia.hercules.utilities.SharedPrefHercules;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class IniciarVisitaOrdemVerticalActivity extends AppCompatActivity {
    private boolean isLoadingOrdens = true;
    private RecyclerView rvOrdensEt;
    private RecyclerView rvSkeletonOrdens;
    private FloatingActionButton fabAddNew;
    ApiDados apiDados = new ApiDados();
    private ApiCalls apiCalls;
    private List<OrdemEt> ordensList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_iniciar_visitas_ordem_vertical);
        rvOrdensEt = findViewById(R.id.rvOrdensEt);
        rvSkeletonOrdens = findViewById(R.id.rvSkeletonOrdens);
        fabAddNew = findViewById(R.id.fabAddNew);

        // Configurar o clique do FAB
        fabAddNew.setOnClickListener(view -> {
            Intent intent = new Intent(IniciarVisitaOrdemVerticalActivity.this, NovaOrdemActivity.class);
            startActivity(intent);
        });

        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(IniciarVisitaOrdemVerticalActivity.this);
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);
        int userid = Integer.parseInt(sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_ID));

        Intent intent = getIntent();
        if (intent.hasExtra("msg")) {
            String mensagem = intent.getStringExtra("msg");
            Toast.makeText(IniciarVisitaOrdemVerticalActivity.this, mensagem, Toast.LENGTH_SHORT).show();
        }

        setupSkeleton();
        buscaOrdens(userid, token);
    }

    private void setupSkeleton() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        rvSkeletonOrdens.setLayoutManager(layoutManager);

        SkeletonAdapter skeletonAdapter = new SkeletonAdapter(5);
        rvSkeletonOrdens.setAdapter(skeletonAdapter);
    }

    private void showLoading(boolean isLoading) {
        if (isLoading) {
            rvSkeletonOrdens.setVisibility(View.VISIBLE);
            rvOrdensEt.setVisibility(View.GONE);
        } else {
            rvSkeletonOrdens.setVisibility(View.GONE);
            rvOrdensEt.setVisibility(View.VISIBLE);
        }
    }

    private void buscaOrdens(int userid, String token) {
        showLoading(true);
        isLoadingOrdens = true;
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        Call<List<OrdemEt>> callouvid = apiCalls.getOrdens(userid, token);
        callouvid.enqueue(new Callback<List<OrdemEt>>() {
            @Override
            public void onResponse(Call<List<OrdemEt>> callouvid, Response<List<OrdemEt>> response) {
                showLoading(false);
                isLoadingOrdens = false;
                if (response.isSuccessful() && response.body() != null) {
                    ordensList = response.body();

                    if (ordensList.isEmpty()) {
                        Toast.makeText(IniciarVisitaOrdemVerticalActivity.this, "Não há ordens!", Toast.LENGTH_SHORT).show();
                    } else {
                        // Configurar o RecyclerView
                        LinearLayoutManager layoutManager = new LinearLayoutManager(
                                IniciarVisitaOrdemVerticalActivity.this,
                                LinearLayoutManager.VERTICAL,
                                false
                        );

                        // Configurar para mostrar todos os itens
                        layoutManager.setInitialPrefetchItemCount(ordensList.size());
                        rvOrdensEt.setLayoutManager(layoutManager);

                        // Adicionar espaçamento entre itens
                        if (rvOrdensEt.getItemDecorationCount() == 0) {
                            rvOrdensEt.addItemDecoration(new RecyclerView.ItemDecoration() {
                                @Override
                                public void getItemOffsets(@NonNull android.graphics.Rect outRect, @NonNull View view,
                                                           @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                                    // Adiciona margem inferior em todos os itens
                                    outRect.bottom = getResources().getDimensionPixelSize(R.dimen._16pxv);
                                    // Adiciona margem nas laterais
                                    outRect.left = getResources().getDimensionPixelSize(R.dimen._8pxh);
                                    outRect.right = getResources().getDimensionPixelSize(R.dimen._8pxh);
                                }
                            });
                        }

                        OrdensEtAdapter ordensEtAdapter = new OrdensEtAdapter(ordensList);
                        rvOrdensEt.setAdapter(ordensEtAdapter);

                        // Forçar o RecyclerView a medir e layoutar novamente
                        rvOrdensEt.post(new Runnable() {
                            @Override
                            public void run() {
                                rvOrdensEt.scrollToPosition(0);
                                rvOrdensEt.invalidate();
                            }
                        });
                    }
                } else {
                    Toast.makeText(IniciarVisitaOrdemVerticalActivity.this, "Não há ordens de serviço designadas", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<OrdemEt>> call, Throwable t) {
                showLoading(false);
                isLoadingOrdens = false;
                Toast.makeText(IniciarVisitaOrdemVerticalActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
    }
    @Override
    public void onBackPressed() {
        Intent intent = new Intent(getApplicationContext(), LoginActivity.class);
        startActivity(intent);
    }
}

/* linearContent card inteiro */

/* txtHeader Titulo do card */

/* etKeytenOne texto do card */