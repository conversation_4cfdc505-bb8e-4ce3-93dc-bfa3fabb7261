package com.baramaia.hercules.widgets;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.baramaia.hercules.R;

/**
 * Botão personalizado com animação de carregamento centralizada.
 */
public class LoadingButton extends FrameLayout {

    private TextView buttonText;
    private ProgressBar progressBar;
    private boolean isLoading = false;
    private OnClickListener clickListener;

    public LoadingButton(@NonNull Context context) {
        super(context);
        init(context);
    }

    public LoadingButton(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public LoadingButton(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        // Inflar o layout
        LayoutInflater.from(context).inflate(R.layout.button_loading, this, true);
        
        // Inicializar views
        buttonText = findViewById(R.id.buttonText);
        progressBar = findViewById(R.id.progressBar);
        
        // Configurar clique
        super.setOnClickListener(v -> {
            if (!isLoading && clickListener != null) {
                clickListener.onClick(v);
            }
        });
    }

    @Override
    public void setOnClickListener(@Nullable OnClickListener l) {
        this.clickListener = l;
    }

    /**
     * Define o texto do botão.
     * @param text Texto a ser exibido
     */
    public void setText(String text) {
        buttonText.setText(text);
    }

    /**
     * Inicia a animação de carregamento.
     */
    public void startLoading() {
        isLoading = true;
        buttonText.setVisibility(INVISIBLE);
        progressBar.setVisibility(VISIBLE);
    }

    /**
     * Para a animação de carregamento.
     */
    public void stopLoading() {
        isLoading = false;
        buttonText.setVisibility(VISIBLE);
        progressBar.setVisibility(GONE);
    }

    /**
     * Verifica se o botão está em estado de carregamento.
     * @return true se estiver carregando, false caso contrário
     */
    public boolean isLoading() {
        return isLoading;
    }
}
