package com.baramaia.hercules;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.drawable.ColorDrawable;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.DAO.VisitaDao;
import com.baramaia.hercules.Data.AppDatabase;
import com.baramaia.hercules.models.Foto;
import com.baramaia.hercules.models.Motivo;
import com.baramaia.hercules.models.Ouvidoria;
import com.baramaia.hercules.models.Visita;
import com.baramaia.hercules.models.VisitaApi;
import com.baramaia.hercules.network.ApiCalls;
import com.baramaia.hercules.network.ApiDados;
import com.baramaia.hercules.utilities.SharedPrefHercules;
import com.github.rygelouv.androidloadingbuttonlib.LoadingButton;
import com.google.android.material.button.MaterialButton;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.gson.Gson;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class RegistroNaoActivity extends AppCompatActivity {
    private FusedLocationProviderClient fusedLocationClient;
    private LoadingButton btnFinalizar;
    private MaterialButton btnIconizercamera;
    private EditText edtObsercacao;
    private ImageView imageArrowleftOne;
    private static final int REQUEST_IMAGE_CAPTURE = 1;
    private static final int REQUEST_PICK_IMAGE = 2;
    private int currentPhotoIndex = -1;
    private RecyclerView row_motivos;
    private MotivosAdapter motivoAdapter;
    private List<Motivo> motivosList = new ArrayList<>();
    Date dataagora = new Date();
    ApiDados apiDados = new ApiDados();
    private ApiCalls apiCalls;
    private List<Foto> fotosList = new ArrayList<>();
    private int ordId;
    private String coordenadas;
    private ImageView checkFoto;
    private int et;

    private Uri photoUri;
    private File photoFile;

    private ImageView thumbnailFoto;
    private CardView cardFoto;
    private Bitmap fullImage;
    private Dialog fullscreenDialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_registro_assinalado_n_o);

        carregarVisitaSalva();
        initializeViews();
        setupRecyclerView();
        setupClickListeners();
        BuscarMotivos();
    }

    private void initializeViews() {
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        edtObsercacao = findViewById(R.id.edtObsercacao);
        row_motivos = findViewById(R.id.row_motivos);
        btnIconizercamera = findViewById(R.id.btnIconizercamera);
        btnFinalizar = findViewById(R.id.btnFinalizar);

        imageArrowleftOne = findViewById(R.id.imageArrowleftOne);

        // Inicializar a miniatura
        thumbnailFoto = findViewById(R.id.thumbnailFoto);
        cardFoto = findViewById(R.id.cardFoto);

        if (thumbnailFoto != null) {
            thumbnailFoto.setOnClickListener(v -> showFullscreenImage(fullImage));
        }

        Intent intent = getIntent();
        ordId = intent.getIntExtra("ordemId", -1);
        et = intent.getIntExtra("et", -1);
    }

    private void setupRecyclerView() {
        motivoAdapter = new MotivosAdapter(motivosList);
        row_motivos.setAdapter(motivoAdapter);
        row_motivos.setLayoutManager(new LinearLayoutManager(this));

        // Adiciona decoração para reduzir o espaçamento entre itens
        row_motivos.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull android.graphics.Rect outRect,
                                       @NonNull View view,
                                       @NonNull RecyclerView parent,
                                       @NonNull RecyclerView.State state) {
                // Define um espaçamento negativo para aproximar os itens
                outRect.top = -10;
                outRect.bottom = 0;
            }
        });
    }

    private void setupClickListeners() {
        btnFinalizar.setOnClickListener(view -> {
            if (!isFinishing()) {
                fusedLocationClient = LocationServices.getFusedLocationProviderClient(this);
                getGPS();
            }
        });

        btnIconizercamera.setOnClickListener(view -> {
            if (!isFinishing()) {
                currentPhotoIndex = 0;
                showImagePickerDialog();
            }
        });

        imageArrowleftOne.setOnClickListener(view -> {
            if (!isFinishing()) {
//                showConfirmDialog();
                enviarTela();
            }
        });
    }

    private void showImagePickerDialog() {
        BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(this);
        View dialogView = getLayoutInflater().inflate(R.layout.dialog_escolha_foto, null);

        MaterialButton btnCamera = dialogView.findViewById(R.id.btnCamera);
        MaterialButton btnGaleria = dialogView.findViewById(R.id.btnGaleria);

        btnCamera.setOnClickListener(v -> {
            dispatchTakePictureIntent();
            bottomSheetDialog.dismiss();
        });

        btnGaleria.setOnClickListener(v -> {
            openGallery();
            bottomSheetDialog.dismiss();
        });

        bottomSheetDialog.setContentView(dialogView);
        bottomSheetDialog.show();
    }

    private void openGallery() {
        Intent intent = new Intent(Intent.ACTION_PICK);
        intent.setType("image/*");
        startActivityForResult(intent, REQUEST_PICK_IMAGE);
    }

    private void dispatchTakePictureIntent() {
        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        if (takePictureIntent.resolveActivity(getPackageManager()) != null) {
            try {
                photoFile = createImageFile(); // Criar arquivo para salvar a foto
                if (photoFile != null) {
                    photoUri = FileProvider.getUriForFile(this, "com.baramaia.hercules", photoFile);
                    takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                    startActivityForResult(takePictureIntent, REQUEST_IMAGE_CAPTURE);
                }
            } catch (IOException ex) {
                ex.printStackTrace();
                Toast.makeText(this, "Erro ao criar arquivo de imagem", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private File createImageFile() throws IOException {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        return File.createTempFile(imageFileName, ".jpg", storageDir);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == RESULT_OK) {
            Foto foto = null;

            if (requestCode == REQUEST_IMAGE_CAPTURE && photoFile != null) {
                // Corrige a orientação da imagem antes de processar
                Bitmap imageBitmap = getCorrectlyOrientedImage(photoFile.getAbsolutePath());
                if (imageBitmap != null) {
                    foto = processarFoto(imageBitmap);
                }
            } else if (requestCode == REQUEST_PICK_IMAGE && data != null && data.getData() != null) {
                try {
                    Uri imageUri = data.getData();
                    String[] filePathColumn = {MediaStore.Images.Media.DATA};
                    Cursor cursor = getContentResolver().query(imageUri, filePathColumn, null, null, null);
                    if (cursor != null) {
                        cursor.moveToFirst();
                        int columnIndex = cursor.getColumnIndex(filePathColumn[0]);
                        String picturePath = cursor.getString(columnIndex);
                        cursor.close();

                        // Corrige a orientação da imagem da galeria também
                        Bitmap selectedImage = getCorrectlyOrientedImage(picturePath);
                        if (selectedImage != null) {
                            foto = processarFoto(selectedImage);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (foto != null) {
                fotosList.add(foto);
                salvarVisitaNoBanco();
                Toast.makeText(this, "Foto adicionada à lista para o registro", Toast.LENGTH_SHORT).show();
            }
        } else {
            Toast.makeText(this, "Seleção de foto cancelada", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void mostrarLoadingBotao() {
        btnFinalizar.startLoading("Processando...");
    }
    
    private void esconderLoadingBotao() {
        btnFinalizar.stopLoading(getString(R.string.msg_proximo));
    }

    private void salvarVisitaNoBanco() {
        String obs = edtObsercacao.getText().toString().trim();

        new Thread(() -> {
            VisitaDao visitaDao = AppDatabase.getDatabase(getApplicationContext()).visitaDao();

            // Recupera a visita salva ou cria uma nova
            Visita visita = visitaDao.getVisitaByOrdId(ordId);
            if (visita == null) {
                visita = new Visita();
                visita.setVis_ordId(ordId);
                visita.setVis_data(pegaDataHora(dataagora) != null ? pegaDataHora(dataagora) : "");
                visita.setVis_efetivado(1);
                visita.setVis_eqpVisitaId(0);
                // Garantir que todos os campos String não sejam null
                visita.setVis_descricao("");
                visita.setVis_problemas("");
                visita.setVis_observacao("");
                visita.setVis_gps("");
                // Garantir que todas as listas não sejam null
                visita.setFotos(new ArrayList<>());
                visita.setEqpInstalados(new ArrayList<>());
                visita.setEqpRetirados(new ArrayList<>());
                visita.setOrdens(new ArrayList<>());
                visita.setMotivo(null); // Este pode ser null
                visita.setVis_status(false);
                visita.setAvulsoId(0);
            }

            // Null safety checks - use empty string instead of null for required fields
            visita.setVis_observacao(obs != null && !obs.isEmpty() ? obs : "");
            visita.setVis_gps(coordenadas != null ? coordenadas : "");
            visita.setFotos(fotosList != null ? fotosList : new ArrayList<>());

            try {
                visitaDao.insert(visita); // Salva ou atualiza no banco
                Log.d("VisitaDebug", "Inserção no RegistroNaoActivity realizada com sucesso!");
            } catch (Exception e) {
                Log.e("VisitaDebug", "Erro ao inserir visita no RegistroNaoActivity: " + e.getMessage(), e);
                runOnUiThread(() -> Toast.makeText(RegistroNaoActivity.this, "Erro ao salvar: " + e.getMessage(), Toast.LENGTH_LONG).show());
            }

        }).start();
    }

    private void carregarVisitaSalva() {
        new Thread(() -> {
            VisitaDao visitaDao = AppDatabase.getDatabase(getApplicationContext()).visitaDao();
            Visita visita = visitaDao.getVisitaByOrdId(ordId);

            if (visita != null) {
                runOnUiThread(() -> {
                    edtObsercacao.setText(visita.getVis_observacao() != null ? visita.getVis_observacao() : "");
                    if (visita.getFotos() != null) {
                        fotosList.addAll(visita.getFotos());
                    }
                    updatePhotoStatus();
                });
            }
        }).start();
    }

    private Foto processarFoto(Bitmap imageBitmap) {
        // Salvar a imagem original para visualização em tela cheia
        fullImage = imageBitmap.copy(imageBitmap.getConfig(), true);

        // Criar e salvar a miniatura
        Bitmap thumbnail = Bitmap.createScaledBitmap(imageBitmap, 200, 200, true);

        // Atualizar a miniatura na tela
        runOnUiThread(() -> {
            if (thumbnailFoto != null) {
                thumbnailFoto.setImageBitmap(thumbnail);
                updatePhotoStatus();
            }
        });

        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        imageBitmap.compress(Bitmap.CompressFormat.JPEG, 50, stream);
        byte[] byteArray = stream.toByteArray();
        String base64Image = Base64.encodeToString(byteArray, Base64.DEFAULT);

        Foto foto = new Foto();
        foto.setFoto_nome("Foto_" + ordId + "_" + pegaDataHora(dataagora));
        foto.setFoto_extensao("PNG");
        foto.setFoto_dado(""); // Garantir que não seja null
        foto.setFoto_dado_base64(base64Image != null ? base64Image : "");
        foto.setFoto_tipo("Visita");

        // Log para verificar os dados da foto
        Log.d("VisitaDebug", "Foto criada no RegistroNaoActivity: nome='" + foto.getFoto_nome() + "', extensao='" + foto.getFoto_extensao() + "', tipo='" + foto.getFoto_tipo() + "', dado='" + foto.getFoto_dado() + "'");

        return foto;
    }

    private String pegaDataHora(Date dt) {
        String dataHora;

        //===============PREECHE DATA INICIAL==============================
        SimpleDateFormat formataData = new SimpleDateFormat("dd/MM/yyyy");
        Date data = new Date();
        if (dt != null) {
            data = dt;
        }
        String dataFormatada = formataData.format(data);
        //===============PREECHE DATA INICIAL==============================

        //===============PREECHE HORA INICIAL==============================
        String sHora;
        String sMinuto;
        if (data.getMinutes() < 10)
            sMinuto = "0" + String.valueOf(data.getMinutes());
        else
            sMinuto = String.valueOf(data.getMinutes());

        if (data.getHours() < 10)
            sHora = "0" + String.valueOf(data.getHours());
        else
            sHora = String.valueOf(data.getHours());
        String sHour = sHora + ":" + sMinuto;
        //===============PREECHE HORA INICIAL==============================

        dataHora = dataFormatada + " " + sHour;

        //================================================
        String dd = dataHora.substring(0, 2);
        String MM = dataHora.substring(3, 5);
        String aaaa = dataHora.substring(6, 10);
        String HH = dataHora.substring(11, 13);
        String mm = dataHora.substring(14, 16);
        dataHora = aaaa + "-" + MM + "-" + dd + "T" + HH + ":" + mm + ":00";
        //================================================

        return dataHora;
    }

    @SuppressLint("MissingPermission")
    private void getGPS() {
        fusedLocationClient.getLastLocation().addOnCompleteListener(new OnCompleteListener<Location>() {
            @Override
            public void onComplete(@NonNull Task<Location> task) {
                Location location = task.getResult();

                if (location != null) {
                    try {
                        Geocoder geocoder = new Geocoder(RegistroNaoActivity.this, Locale.getDefault());
                        List<Address> addresses = geocoder.getFromLocation(location.getLatitude(), location.getLongitude(), 1);

                        coordenadas = addresses.get(0).getLatitude() + "," + addresses.get(0).getLongitude();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                } else {
                    coordenadas = "Localização não encontrada";
                }

                // Após obter as coordenadas, chama salvarVisitaNaoEfetivada()
                enviarVisitaNaoEfetivada();
            }
        });
    }

    public void testarObjetoComoJson(Visita v) {
        // Exemplo de objeto que você deseja enviar


        // Converter objeto para JSON usando Gson
        Gson gson = new Gson();
        String jsonString = gson.toJson(v);

        // Exibir ou usar a string JSON
        System.out.println("JSON gerado: " + jsonString);

        // Agora você pode enviar este JSON para sua Call se necessário
    }

    private void updatePhotoStatus() {
        if (cardFoto != null) {
            cardFoto.setVisibility(View.VISIBLE);
        }
    }

    private void BuscarMotivos() {
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(RegistroNaoActivity.this);
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);

        Call<List<Motivo>> callmotivo = apiCalls.getMotivos(token);
        callmotivo.enqueue(new Callback<List<Motivo>>() {
            @Override
            public void onResponse(Call<List<Motivo>> callmotivo, Response<List<Motivo>> response) {
                Log.d("DEB_MOTIVO", "Entrou no response motivos");
                if (response.isSuccessful() && response.body() != null) {

                    motivosList.clear(); // Limpa a lista antes de adicionar novos dados
                    motivosList.addAll(response.body());
                    if (motivoAdapter == null) {
                        motivoAdapter = new MotivosAdapter(motivosList);
                        row_motivos.setAdapter(motivoAdapter);
                    } else {
                        motivoAdapter.notifyDataSetChanged();
                    }
                } else {
                    Toast.makeText(RegistroNaoActivity.this, "Não há lista de motivos", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<Motivo>> callmotivo, Throwable t) {
                Toast.makeText(RegistroNaoActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
    }
    private void buscaOuvidorias(String token, String userId) {
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(getApplicationContext());
        int userid = Integer.parseInt(userId);

        Call<List<Ouvidoria>> callouvid = apiCalls.getOuvidorias(userid, token);
        callouvid.enqueue(new Callback<List<Ouvidoria>>() {
            @Override
            public void onResponse(Call<List<Ouvidoria>> callouvid, Response<List<Ouvidoria>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA, "1");
                } else {
                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA, "0");
                }
            }
            @Override
            public void onFailure(Call<List<Ouvidoria>> call, Throwable t) {
                Toast.makeText(RegistroNaoActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
    }
    private void enviarVisitaNaoEfetivada() {

        new Thread(() -> {
            String obs = edtObsercacao.getText().toString().trim();
            Motivo selectedMotivo = motivoAdapter.getSelectedMotivo();

            if (obs.isEmpty()) {
                runOnUiThread(() ->
                        Toast.makeText(this, "Por favor, descreva a observação da visita não efetivada.", Toast.LENGTH_SHORT).show()
                );
                return;
            }
            if (selectedMotivo == null) {
                runOnUiThread(() ->
                        Toast.makeText(this, "Por favor, selecione um motivo da visita não efetivada", Toast.LENGTH_SHORT).show()
                );
                return;
            }
            if (fotosList.isEmpty()) {
                runOnUiThread(() ->
                        Toast.makeText(this, "É necessário que tire a foto para executar a visita", Toast.LENGTH_SHORT).show()
                );
                return;
            }
            runOnUiThread(() -> {
                mostrarLoadingBotao();
            });
            VisitaDao visitaDao = AppDatabase.getDatabase(getApplicationContext()).visitaDao();
            Visita visita = visitaDao.getVisitaByOrdId(ordId);

            if (visita == null) {
                runOnUiThread(() -> Toast.makeText(this, "Erro: Nenhuma visita salva!", Toast.LENGTH_SHORT).show());
                return;
            }

            visita.setId(0);
            visita.setVis_ordId(ordId);
            visita.setVis_data(pegaDataHora(dataagora));
            visita.setVis_efetivado(0);
            visita.setVis_eqpVisitaId(0);
            visita.setVis_descricao("");
            visita.setVis_problemas("");
            visita.setVis_observacao(obs != null ? obs : "");
            visita.setVis_gps(coordenadas != null ? coordenadas : "");
            visita.setFotos(fotosList != null ? fotosList : new ArrayList<>());
            visita.setEqpInstalados(new ArrayList<>());
            visita.setEqpRetirados(new ArrayList<>());
            visita.setMotivo(selectedMotivo);
            visita.setOrdens(new ArrayList<>());


            if (visita.getVis_observacao().isEmpty()) {
                runOnUiThread(() -> Toast.makeText(this, "Por favor, descreva a observação da visita não efetivada.", Toast.LENGTH_SHORT).show());
            }
            if (visita.getMotivo() == null) {
                runOnUiThread(() -> Toast.makeText(this, "Por favor, selecione um motivo da visita não efetivada.", Toast.LENGTH_SHORT).show());
            }
            if (visita.getFotos().isEmpty()) {
                runOnUiThread(() -> Toast.makeText(this, "É necessário que tire a foto para executar a visita", Toast.LENGTH_SHORT).show());
                return;
            }
            //mostrarLoadingBotao();
            // Envio para API
            SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(RegistroNaoActivity.this);
            String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);
            String userId = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_ID);
            buscaOuvidorias(token,userId);

            String exOuvidoria = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA);

            // Converter Visita (banco) para VisitaApi (API)
            VisitaApi visitaApi = new VisitaApi(visita);

            //testarObjetoComoJson(visita);

            Call<VisitaApi> callvisita = apiCalls.postVisita(visitaApi, token);
            callvisita.enqueue(new Callback<VisitaApi>() {
                @Override
                public void onResponse(Call<VisitaApi> callvisita, Response<VisitaApi> response) {
                    if (response.isSuccessful() && response.body() != null) {
                        runOnUiThread(() -> {
                            Toast.makeText(RegistroNaoActivity.this, "Visita salva com sucesso!", Toast.LENGTH_SHORT).show();
                        });
                        if (exOuvidoria.equals("1")) {
                            Intent intentVisita = new Intent(getApplicationContext(), IniciarVisitaActivity.class);
                            intentVisita.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                            startActivity(intentVisita);
                            finish();

                        } else {
                            Intent intentVisita = new Intent(getApplicationContext(), IniciarVisitaOrdemVerticalActivity.class);
                            intentVisita.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                            startActivity(intentVisita);
                            finish();
                        }
                    } else {
                        esconderLoadingBotao();
                        Toast.makeText(RegistroNaoActivity.this, "Não foi possível salvar a visita, contate o administrador", Toast.LENGTH_SHORT).show();
                    }
                }

                @Override
                public void onFailure(Call<VisitaApi> callvisita, Throwable t) {
                    esconderLoadingBotao();
                    runOnUiThread(() -> Toast.makeText(RegistroNaoActivity.this, "Erro na conexão!", Toast.LENGTH_SHORT).show());
                }
            });
        }).start();
    }

    private void showConfirmDialog() {
        if (isFinishing()) return;

        Dialog dialog = new Dialog(this, R.style.DialogTheme);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_confirmar_registro);

        Window window = dialog.getWindow();
        if (window != null) {
            window.setLayout(WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT);
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }

        Button btnSim = dialog.findViewById(R.id.btnSim);
        Button btnNo = dialog.findViewById(R.id.btnNo);

        btnSim.setOnClickListener(v -> {
            dialog.dismiss();
            if (!isFinishing()) {
                Intent intentConfirma = new Intent(RegistroNaoActivity.this, RegistroSimActivity.class);
                intentConfirma.putExtra("ordemId", ordId);
                intentConfirma.putExtra("et", et);
                startActivity(intentConfirma);
                finish();
            }
        });

        btnNo.setOnClickListener(v -> {
            dialog.dismiss();
            if (!isFinishing()) {
                finish();
            }
        });

        if (!isFinishing()) {
            dialog.show();
        }
    }
    private void enviarTela() {
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(RegistroNaoActivity.this);
        String exOuvidoria = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA);
        if (exOuvidoria.equals("1")) {
            Intent intentVisita = new Intent(getApplicationContext(), IniciarVisitaActivity.class);
            intentVisita.putExtra("ordemId", ordId);
            intentVisita.putExtra("et", et);
            startActivity(intentVisita);

        } else {
            Intent intentVisita = new Intent(getApplicationContext(), IniciarVisitaOrdemVerticalActivity.class);
            intentVisita.putExtra("ordemId", ordId);
            intentVisita.putExtra("et", et);
            startActivity(intentVisita);
        }
    }

    private void showFullscreenImage(Bitmap imageBitmap) {
        Dialog fullscreenDialog = new Dialog(this, R.style.DialogTheme);
        fullscreenDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        fullscreenDialog.setContentView(R.layout.dialog_fullscreen_image);

        Window window = fullscreenDialog.getWindow();
        if (window != null) {
            window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.setDimAmount(0.0f); // Remove o escurecimento do fundo
        }

        ImageView fullscreenImageView = fullscreenDialog.findViewById(R.id.fullscreenImageView);
        if (fullscreenImageView != null) {
            fullscreenImageView.setImageBitmap(imageBitmap);
            fullscreenImageView.setScaleType(ImageView.ScaleType.FIT_CENTER);
            // Fechar o diálogo ao clicar na imagem
            fullscreenImageView.setOnClickListener(v -> fullscreenDialog.dismiss());
        }

        fullscreenDialog.show();
    }

    private Bitmap getCorrectlyOrientedImage(String photoPath) {
        try {
            // Primeiro decodifica apenas os limites da imagem
            BitmapFactory.Options bounds = new BitmapFactory.Options();
            bounds.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(photoPath, bounds);

            // Calcula o tamanho ideal para carregar a imagem
            BitmapFactory.Options opts = new BitmapFactory.Options();
            int photoWidth = bounds.outWidth;
            int photoHeight = bounds.outHeight;
            int scaleFactor = Math.min(photoWidth / 1024, photoHeight / 1024);
            if (scaleFactor > 0) {
                opts.inSampleSize = scaleFactor;
            }

            // Carrega a imagem com o tamanho reduzido
            Bitmap bitmap = BitmapFactory.decodeFile(photoPath, opts);

            // Obtém a orientação da imagem dos metadados EXIF
            android.media.ExifInterface exif = new android.media.ExifInterface(photoPath);
            int orientation = exif.getAttributeInt(
                    android.media.ExifInterface.TAG_ORIENTATION,
                    android.media.ExifInterface.ORIENTATION_UNDEFINED);

            // Rotaciona a imagem se necessário
            Bitmap rotatedBitmap = null;
            switch (orientation) {
                case android.media.ExifInterface.ORIENTATION_ROTATE_90:
                    rotatedBitmap = rotateImage(bitmap, 90);
                    break;
                case android.media.ExifInterface.ORIENTATION_ROTATE_180:
                    rotatedBitmap = rotateImage(bitmap, 180);
                    break;
                case android.media.ExifInterface.ORIENTATION_ROTATE_270:
                    rotatedBitmap = rotateImage(bitmap, 270);
                    break;
                default:
                    rotatedBitmap = bitmap;
            }

            return rotatedBitmap;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private Bitmap rotateImage(Bitmap source, float angle) {
        Matrix matrix = new Matrix();
        matrix.postRotate(angle);
        return Bitmap.createBitmap(source, 0, 0, source.getWidth(), source.getHeight(),
                matrix, true);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Limpar recursos
        if (fusedLocationClient != null) {
            fusedLocationClient = null;
        }
        if (fullImage != null && !fullImage.isRecycled()) {
            fullImage.recycle();
            fullImage = null;
        }
    }
}