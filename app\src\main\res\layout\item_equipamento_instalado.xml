<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginTop="8dp"
    android:layout_marginEnd="16dp"
    android:layout_marginBottom="8dp"
    android:background="@drawable/rectangle_bg_white_a700_radius_12"
    android:elevation="2dp"
    android:orientation="vertical"
    android:paddingStart="16dp"
    android:paddingTop="16dp"
    android:paddingEnd="16dp"
    android:paddingBottom="16dp">

    <!-- Header do Item -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_check_circle"
            app:tint="@color/custom_border_color" />

        <TextView
            style="@style/txtRobotoregular18"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Equipamento Instalado"
            android:textColor="@color/blue_gray_800" />

        <ImageButton
            android:id="@+id/btnRemover"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Remover equipamento"
            android:padding="4dp"
            android:src="@drawable/ic_close_circle"
            app:tint="@color/gray_600" />
    </LinearLayout>

    <!-- Spinner de Equipamentos -->
    <com.google.android.material.textfield.TextInputLayout
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Selecione o equipamento"
        app:boxStrokeColor="@color/custom_border_color"
        app:hintTextColor="@color/custom_border_color"
        app:endIconTint="@color/custom_border_color"
        app:cursorColor="@color/custom_border_color">

        <com.google.android.material.textfield.MaterialAutoCompleteTextView
            android:id="@+id/spinnerOpcoesInst"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none"
            android:textColor="@color/blue_gray_800" />
    </com.google.android.material.textfield.TextInputLayout>

    <!-- Campo Número EDP -->
    <com.google.android.material.textfield.TextInputLayout
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="Número EDP"
        app:boxStrokeColor="@color/custom_border_color"
        app:cursorColor="@color/custom_border_color"
        app:hintTextColor="@color/custom_border_color">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edtNumeroMedidorInstalado"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:textColor="@color/blue_gray_800" />
    </com.google.android.material.textfield.TextInputLayout>

    <!-- Campo Número Display -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/tilNumeroDisplayInstalado"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="Número Display"
        app:boxStrokeColor="@color/custom_border_color"
        app:cursorColor="@color/custom_border_color"
        app:hintTextColor="@color/custom_border_color">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edtNumeroDisplayInstalado"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:textColor="@color/blue_gray_800" />
    </com.google.android.material.textfield.TextInputLayout>
    <!-- Seção Foto -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical">

        <!-- Header da seção foto -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/txtRobotoregular16"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Foto do equipamento:"
                android:textColor="@color/gray_600" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCamera"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tirar Foto"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:contentDescription="Tirar foto"
                app:backgroundTint="@color/custom_border_color"
                app:cornerRadius="8dp"
                app:icon="@drawable/img_iconizer_camera"
                app:iconTint="@color/white"
                app:iconGravity="start"
                app:iconPadding="8dp" />
        </LinearLayout>

        <!-- Miniatura da foto -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardFotoInstalado"
            android:layout_width="120dp"
            android:layout_height="90dp"
            android:layout_marginTop="12dp"
            android:layout_gravity="center"
            android:visibility="gone"
            android:clickable="true"
            android:focusable="true"
            app:cardCornerRadius="8dp"
            app:cardBackgroundColor="@android:color/white"
            app:cardElevation="4dp">
            
            <ImageView
                android:id="@+id/thumbnailFotoInstalado"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@drawable/rounded_corner" />
                
            <!-- Overlay para indicar que é clicável -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?attr/selectableItemBackground" />
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</LinearLayout>