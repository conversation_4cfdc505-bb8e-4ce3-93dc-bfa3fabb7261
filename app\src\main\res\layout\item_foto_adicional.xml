<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_marginEnd="8dp">

    <androidx.cardview.widget.CardView
        android:layout_width="80dp"
        android:layout_height="80dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <ImageView
            android:id="@+id/imageViewFoto"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:contentDescription="Foto adicional" />

        <!-- Bo<PERSON><PERSON> de remover -->
        <ImageView
            android:id="@+id/btnRemoverFoto"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="top|end"
            android:layout_margin="4dp"
            android:background="@drawable/ic_close_circle"
            android:contentDescription="Remover foto" />
    </androidx.cardview.widget.CardView>

    <!-- Label opcional -->
    <TextView
        android:id="@+id/txtLabelFoto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textSize="10sp"
        android:textColor="@color/blue_gray_800"
        android:gravity="center"
        android:text="Adicional"
        android:visibility="gone" />

</LinearLayout>