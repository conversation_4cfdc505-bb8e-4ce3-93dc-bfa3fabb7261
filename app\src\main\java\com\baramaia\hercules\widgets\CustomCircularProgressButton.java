package com.baramaia.hercules.widgets;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.baramaia.hercules.R;
import com.github.leandroborgesferreira.loadingbutton.customViews.CircularProgressButton;

/**
 * Botão de progresso circular personalizado que mantém o background original
 * durante e após a animação.
 */
public class CustomCircularProgressButton extends CircularProgressButton {

    private Drawable originalBackground;

    public CustomCircularProgressButton(@NonNull Context context) {
        super(context);
        init();
    }

    public CustomCircularProgressButton(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CustomCircularProgressButton(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        // <PERSON>var o background original
        originalBackground = getBackground();

        // Definir o background padrão se não houver um
        if (originalBackground == null) {
            originalBackground = getResources().getDrawable(R.drawable.button_blue_gray_rounded, null);
            setBackground(originalBackground);
        }
    }

    @Override
    public void startAnimation() {
        super.startAnimation();
        // Restaurar o background após iniciar a animação
        post(() -> setBackground(originalBackground));
    }

    @Override
    public void revertAnimation() {
        super.revertAnimation();
        // Restaurar o background após reverter a animação
        post(() -> setBackground(originalBackground));
    }

    @Override
    public void setBackground(Drawable background) {
        super.setBackground(background);
        // Salvar o novo background como original
        if (background != null) {
            originalBackground = background;
        }
    }
}
