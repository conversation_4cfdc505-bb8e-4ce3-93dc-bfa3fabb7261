package com.baramaia.hercules.fragments;

import android.os.Bundle;
import android.text.InputFilter;
import android.text.Spanned;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import com.github.rygelouv.androidloadingbuttonlib.LoadingButton;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.baramaia.hercules.NovaOrdemActivity;
import com.baramaia.hercules.R;

public class TabInfoTecnicasFragment extends Fragment {

    private EditText etCp, etCs, etModulo, etDisplay;
    private Spinner spinnerEmpreiteira;
    private Button btnAnteriorTab3;
    private LoadingButton btnProximoTab3;
    int verifica = 1;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_tab_agendamento, container, false);

        etCp = view.findViewById(R.id.etCp);
        etCs = view.findViewById(R.id.etCs);
        etModulo = view.findViewById(R.id.etModulo);
        etDisplay = view.findViewById(R.id.etDisplay);
        spinnerEmpreiteira = view.findViewById(R.id.spinnerEmpreiteira);

        // Carregar lista de empreiteiras (placeholder estático; integrar API se necessário)
        String[] empreiteiras = {"Selecione", "Empreiteira A", "Empreiteira B", "Empreiteira C"};
        ArrayAdapter<String> empAdapter = new ArrayAdapter<>(requireContext(), android.R.layout.simple_spinner_item, empreiteiras);
        empAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerEmpreiteira.setAdapter(empAdapter);
        btnAnteriorTab3 = view.findViewById(R.id.btnAnteriorTab3);
        btnProximoTab3 = view.findViewById(R.id.btnProximoTab3);

        // Implementação do InputFilter para limitar a 255
        InputFilter[] filters = new InputFilter[1];
        filters[0] = new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end,
                                       Spanned dest, int dstart, int dend) {
                try {
                    String newVal = dest.toString().substring(0, dstart) +
                            source.toString() +
                            dest.toString().substring(dend);
                    if (!newVal.isEmpty()) {
                        int input = Integer.parseInt(newVal);
                        if (input > 255)
                            return "";
                    }
                } catch (NumberFormatException e) {
                    return "";
                }
                return null;
            }
        };
        etCs.setFilters(filters);

        // Carregar dados salvos se existirem
        if (getActivity() instanceof NovaOrdemActivity) {
            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
            if (activity.getCp() != null && !activity.getCp().isEmpty()) {
                etCp.setText(activity.getCp());
            }
            if (activity.getCs() != null && !activity.getCs().isEmpty()) {
                etCs.setText(activity.getCs());
            }
            if (activity.getModulo() != null && !activity.getModulo().isEmpty()) {
                etModulo.setText(activity.getModulo());
            }
            if (activity.getDisplayValue() != null && !activity.getDisplayValue().isEmpty()) {
                etDisplay.setText(activity.getDisplayValue());
            }
        }

        btnAnteriorTab3.setOnClickListener(v -> {
            salvarDados();
            if (getActivity() instanceof NovaOrdemActivity) {
                ((NovaOrdemActivity) getActivity()).setCurrentItem(1);
            }
        });

        btnProximoTab3.setOnClickListener(v -> {
            btnProximoTab3.startLoading("Carregando...");
            boolean invalido = false;
            if (etModulo.getText().toString().isEmpty()){
                Toast.makeText(requireContext(), "Necessário preencher o número do módulo! " , Toast.LENGTH_SHORT).show();
                invalido = true;
            }
            if (etDisplay.getText().toString().isEmpty()){
                Toast.makeText(requireContext(), "Necessário preencher o número do display! " , Toast.LENGTH_SHORT).show();
                invalido = true;
            }
            if (!invalido){
                salvarDados();
                if (getActivity() instanceof NovaOrdemActivity) {
                    ((NovaOrdemActivity) getActivity()).setCurrentItem(2);
                }
            }
            btnProximoTab3.stopLoading("Próximo");
        });

        return view;
    }

    // Removida validação de posições

    private void salvarDados() {
        if (getActivity() instanceof NovaOrdemActivity) {
            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
            activity.setCp(etCp.getText().toString().trim());
            activity.setEt("");
            activity.setCs(etCs.getText().toString().trim());
            activity.setModulo(etModulo.getText().toString().trim());

            // Salvar valores selecionados nos spinners (ignorando valores vazios)
            activity.setPos1("");
            activity.setPos2("");
            activity.setPos3("");
            activity.setTanque("");
            activity.setDisplayValue(etDisplay.getText().toString().trim());
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        salvarDados();
    }

    /**
     * Encontra a posição de um valor no adapter
     *
     * @param adapter O adapter a ser pesquisado
     * @param value   O valor a ser encontrado
     * @return A posição do valor no adapter ou -1 se não encontrado
     */
    private int findPositionInAdapter(ArrayAdapter<String> adapter, String value) {
        for (int i = 0; i < adapter.getCount(); i++) {
            if (adapter.getItem(i).equals(value)) {
                return i;
            }
        }
        return -1;
    }
}

