package com.baramaia.hercules;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.view.MotionEvent;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.baramaia.hercules.Data.AppDatabase;
import com.baramaia.hercules.models.Ouvidoria;
import com.baramaia.hercules.models.UserLogin;
import com.baramaia.hercules.network.ApiCalls;
import com.baramaia.hercules.network.ApiDados;
import com.baramaia.hercules.network.ApiResponse;
import com.baramaia.hercules.utilities.SharedPrefHercules;
import com.github.rygelouv.androidloadingbuttonlib.LoadingButton;
import com.google.android.material.snackbar.Snackbar;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class LoginActivity extends AppCompatActivity {
    private static final int LOCATION_PERMISSION_CODE = 100;
    private static final int CAMERA_PERMISSION_CODE = 100;
    private static final int WRITE_EXTERNAL_STORAGE = 112;

    ApiDados apiDados = new ApiDados();
    String BASEURL = apiDados.BASEURL;
    private ApiCalls apiCalls;
    private EditText etEnterwithuser;
    private EditText etPasswordOne;
    private LoadingButton btnEntrar;
    //private CheckBox checkBoxMeLembre;
    private TextView txtEsqueceua;
    private boolean passwordVisible = false;
    private String token;
    private int userid;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);
        new Thread(() -> {
            AppDatabase db = AppDatabase.getDatabase(getApplicationContext());
            db.visitaDao().deleteAll();
            db.equipamentoVisitaDao().deleteAll();
        }).start();

        // Verifica se as permissões de localização, camera e arazenamento interno foi concedida
        if (!checkPermission(android.Manifest.permission.ACCESS_FINE_LOCATION, LOCATION_PERMISSION_CODE)) {
            // Se a permissão não foi concedida, solicita novamente
            ActivityCompat.requestPermissions(LoginActivity.this, new String[]{android.Manifest.permission.ACCESS_FINE_LOCATION}, LOCATION_PERMISSION_CODE);
        }
        if (!checkPermission(android.Manifest.permission.CAMERA, CAMERA_PERMISSION_CODE)) {
            // Se a permissão não foi concedida, solicita novamente
            ActivityCompat.requestPermissions(LoginActivity.this, new String[]{Manifest.permission.CAMERA}, CAMERA_PERMISSION_CODE);
        }
        if (!checkPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE, WRITE_EXTERNAL_STORAGE)) {
            // Se a permissão não foi concedida, solicita novamente
            ActivityCompat.requestPermissions(LoginActivity.this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, WRITE_EXTERNAL_STORAGE);
        }


        Intent intent = getIntent();
        if (intent.hasExtra("msg")) {
            String mensagem = intent.getStringExtra("msg");
            Toast.makeText(LoginActivity.this, mensagem, Toast.LENGTH_SHORT).show();
        }

        btnEntrar = findViewById(R.id.btnEntrar);
        etEnterwithuser = findViewById(R.id.etEnterwithuser);
        etPasswordOne = findViewById(R.id.etPasswordOne);
        //checkBoxMeLembre = findViewById(R.id.checkBoxMeLembre);
        txtEsqueceua = findViewById(R.id.txtEsqueceua);

        btnEntrar.setOnClickListener(view -> {
            if (!checkPermission(Manifest.permission.ACCESS_FINE_LOCATION, LOCATION_PERMISSION_CODE)) {
                Toast.makeText(LoginActivity.this, "Acesse Informações do aplicativo e permita a Localização!", Toast.LENGTH_SHORT).show();
                ActivityCompat.requestPermissions(LoginActivity.this, new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, LOCATION_PERMISSION_CODE);
                return;
            }
            if (!checkPermission(Manifest.permission.CAMERA, CAMERA_PERMISSION_CODE)) {
                Toast.makeText(LoginActivity.this, "Acesse Informações do aplicativo e permita a Câmera!", Toast.LENGTH_SHORT).show();
                ActivityCompat.requestPermissions(LoginActivity.this, new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, LOCATION_PERMISSION_CODE);
                return;
            }
            if (!isOnline()) {
                Snackbar.make(view, "Não há conexão com a internet", Snackbar.LENGTH_LONG)
                        .setAction("Action", null).show();
                return;
            }
            if (validateLogin()) {
                checkLogin(etEnterwithuser.getText().toString(), etPasswordOne.getText().toString());
            } else {
                Snackbar.make(view, "Os campos de usuário e senha devem ser preenchidos!", Snackbar.LENGTH_LONG)
                        .setAction("Action", null).show();
            }
        });
        txtEsqueceua.setOnClickListener(View -> {
            Intent inte = new Intent(getApplicationContext(), RecuperarActivity.class);
            startActivity(inte);
        });
        setupPasswordVisibilityToggle();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (btnEntrar != null) {
            btnEntrar.stopLoading(getString(R.string.lbl_entrar));
        }
    }

    private void checkLogin(String user, String password) {
        // Inicia loading
        btnEntrar.startLoading(getString(R.string.lbl_entrar));

        // Inicializar API de questões
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(getApplicationContext());

        UserLogin userLogin = new UserLogin();

        userLogin.setEmail(user);
        userLogin.setPassword(password);
        String appVersion = BuildConfig.APP_VERSION;
        userLogin.setNome(appVersion);

        Call<UserLogin> calls = apiCalls.login(userLogin);

        calls.enqueue(new Callback<UserLogin>() {
            @Override
            public void onResponse(Call<UserLogin> calls, Response<UserLogin> response) {
                if (response.isSuccessful()) {
                    UserLogin usuarioList = response.body();

                    if (!usuarioList.isStatus()) {
                        btnEntrar.stopLoading(getString(R.string.lbl_entrar));
                        Toast.makeText(getApplicationContext(), R.string.msg_erro_login_expired, Toast.LENGTH_LONG).show();
                        return;
                    }

                    // Em caso de sucesso, não reverte a animação
                    token = usuarioList.getToken();
                    userid = usuarioList.getId();
                    try {
                        // Verificar se o token já tem o prefixo "Bearer "
                        if (token != null && token.startsWith("Bearer ")) {
                            // Salvar o token como está
                            sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_TOKEN, token);
                            android.util.Log.d("LoginActivity", "Token já tem prefixo Bearer: " + token);
                        } else {
                            // Adicionar o prefixo "Bearer "
                            sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_TOKEN, "Bearer " + token);
                            android.util.Log.d("LoginActivity", "Token com prefixo Bearer adicionado: Bearer " + token);
                        }
                    } catch (Exception ex) {
                        android.util.Log.e("LoginActivity", "Erro ao salvar token: " + ex.getMessage());
                    }
                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_ID, String.valueOf(usuarioList.getId()));
                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_EMAIL, usuarioList.getEmail());

                    buscaOuvidorias(usuarioList);
                } else {
                    // Em caso de erro
                    btnEntrar.stopLoading(getString(R.string.lbl_entrar));
                    ApiResponse msgError = new ApiResponse();
                    msgError.message(getApplicationContext(), response);
                }
            }

            @Override
            public void onFailure(Call<UserLogin> calls, Throwable t) {
                btnEntrar.stopLoading(getString(R.string.lbl_entrar));
                String errorMessage = "Falha na solicitação: " + t.getMessage();
                Toast.makeText(LoginActivity.this, errorMessage, Toast.LENGTH_SHORT).show();
            }
        });
    }

    public boolean checkPermission(String permission, int requestCode) {
        if (ContextCompat.checkSelfPermission(getApplicationContext(), permission) == PackageManager.PERMISSION_DENIED) {
            // Requesting the permission
            ActivityCompat.requestPermissions(LoginActivity.this, new String[]{permission}, requestCode);
            return false;
        } else {
            return true;
        }
    }

    private boolean validateLogin() {
        return (!etEnterwithuser.getText().toString().isEmpty() && !etPasswordOne.getText().toString().isEmpty());
    }

    public boolean isOnline() {
        ConnectivityManager cm = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo netInfo = cm.getActiveNetworkInfo();
        if (netInfo != null && netInfo.isConnectedOrConnecting()) {
            return true;
        } else {
            return false;
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private void setupPasswordVisibilityToggle() {
        etPasswordOne.setOnTouchListener((v, event) -> {
            final int DRAWABLE_RIGHT = 2;

            if (event.getAction() == MotionEvent.ACTION_UP) {
                if (event.getRawX() >= (etPasswordOne.getRight() - etPasswordOne.getCompoundDrawables()[DRAWABLE_RIGHT].getBounds().width())) {
                    int selection = etPasswordOne.getSelectionEnd();
                    if (passwordVisible) {
                        // Esconde a senha
                        etPasswordOne.setTransformationMethod(PasswordTransformationMethod.getInstance());
                        etPasswordOne.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_visibility_off, 0);
                        passwordVisible = false;
                    } else {
                        // Mostra a senha
                        etPasswordOne.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                        etPasswordOne.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_visibility, 0);
                        passwordVisible = true;
                    }
                    etPasswordOne.setSelection(selection);
                    return true;
                }
            }
            return false;
        });
    }
    private void buscaOuvidorias(UserLogin user) {
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(getApplicationContext());
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);

        Call<List<Ouvidoria>> callouvid = apiCalls.getOuvidorias(userid, token);
        callouvid.enqueue(new Callback<List<Ouvidoria>>() {
            @Override
            public void onResponse(Call<List<Ouvidoria>> callouvid, Response<List<Ouvidoria>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    Intent intentOuv = new Intent(getApplicationContext(), IniciarVisitaActivity.class);
                    intentOuv.putExtra("userId", user.getId());
                    intentOuv.putExtra("useruser", user.getNome());
                    intentOuv.putExtra("username", user.getNome());
                    intentOuv.putExtra("useremail", user.getEmail());
                    intentOuv.putExtra("userEmail", user.getEmail());
                    intentOuv.putExtra("usertelefone", 0);
                    intentOuv.putExtra("usercpf", 0);
                    intentOuv.putExtra("userrg", 0); //Valor enviado para SelectActivity
                    intentOuv.putExtra("userstatus", user.isStatus());

                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA, "1");
                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_NOME, user.getNome());

                    ContentValues contentValues = new ContentValues();
                    ContentValues values = new ContentValues();
                    values.put("id_now", 1);

                    startActivity(intentOuv);
                } else {
                    Intent intent = new Intent(getApplicationContext(), IniciarVisitaOrdemVerticalActivity.class);
                    intent.putExtra("userId", user.getId());
                    intent.putExtra("useruser", user.getNome());
                    intent.putExtra("username", user.getNome());
                    intent.putExtra("useremail", user.getEmail());
                    intent.putExtra("userEmail", user.getEmail());
                    intent.putExtra("usertelefone", 0);
                    intent.putExtra("usercpf", 0);
                    intent.putExtra("userrg", 0); //Valor enviado para SelectActivity
                    intent.putExtra("userstatus", user.isStatus());

                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA, "0");
                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_NOME, user.getNome());

                    ContentValues contentValues = new ContentValues();
                    ContentValues values = new ContentValues();
                    values.put("id_now", 1);

                    startActivity(intent);
                }
            }

            @Override
            public void onFailure(Call<List<Ouvidoria>> call, Throwable t) {
                btnEntrar.stopLoading(getString(R.string.lbl_entrar));
                Toast.makeText(LoginActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
    }
}
/*etEnterwithuser - Entrada de usuário */

/* etPasswordOne - Entrada de senha */

/* checkBoxMeLembre - Caixa de seleção "Me Lembre" */

/* btnEntrar - Botão "Entrar" */


