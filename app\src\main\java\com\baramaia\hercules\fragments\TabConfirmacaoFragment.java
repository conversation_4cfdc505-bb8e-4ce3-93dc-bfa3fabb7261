package com.baramaia.hercules.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.baramaia.hercules.DescricaoActivity;
import com.baramaia.hercules.RegistroNaoActivity;
import com.baramaia.hercules.NovaOrdemActivity;
import com.baramaia.hercules.R;
import com.baramaia.hercules.models.Avulso;
import com.baramaia.hercules.network.ApiCalls;
import com.baramaia.hercules.network.ApiDados;
import com.baramaia.hercules.network.LoggingInterceptor;
import com.baramaia.hercules.utilities.SharedPrefHercules;
import com.baramaia.hercules.utilities.ToastUtils;
import com.baramaia.hercules.widgets.LoadingButton;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import android.app.Dialog;
import android.view.Window;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class TabConfirmacaoFragment extends Fragment {

    private TextView tvMotivo, tvDisplay;
    private TextView tvMunicipio;
    private TextView tvCp, tvCs, tvModulo;
    private com.google.android.material.button.MaterialButton btnAnteriorTab4;
    private LoadingButton btnSalvar;
    private Boolean foiExecutado; // true = Sim (Descrição), false = Não (Visita não efetivada)

    private SharedPrefHercules sharedPrefHercules;
    private ApiCalls apiCalls;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_tab_confirmacao, container, false);

        // Inicializar SharedPreferences e API
        sharedPrefHercules = new SharedPrefHercules(requireContext());
        ApiDados apiDados = new ApiDados();

        // Configurar OkHttpClient com o interceptor de log
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(new LoggingInterceptor())
                .build();

        // Configurar Retrofit com o cliente OkHttp
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        // Inicializar TextViews
        tvMotivo = view.findViewById(R.id.tvMotivo);
        tvDisplay = view.findViewById(R.id.tvDisplay);
        tvMunicipio = view.findViewById(R.id.tvMunicipio);
        tvCp = view.findViewById(R.id.tvCp);
        tvCs = view.findViewById(R.id.tvCs);
        tvModulo = view.findViewById(R.id.tvModulo);

        btnAnteriorTab4 = view.findViewById(R.id.btnAnteriorTab4);
        btnSalvar = view.findViewById(R.id.btnSalvar);

        // Carregar dados para exibição
        atualizarDadosExibidos();

        btnAnteriorTab4.setOnClickListener(v -> {
            if (getActivity() instanceof NovaOrdemActivity) {
                ((NovaOrdemActivity) getActivity()).setCurrentItem(0);
            }
        });

        // Configurar o botão de salvar
        btnSalvar.setText("Salvar");
        btnSalvar.setOnClickListener(v -> {
            // Perguntar se o serviço foi executado (Sim/Não)
            mostrarDialogConfirmacao();
        });

        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
        atualizarDadosExibidos();
    }

    private void atualizarDadosExibidos() {
        if (getActivity() instanceof NovaOrdemActivity) {
            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();

            // Atualizar motivo
            tvMotivo.setText(activity.getMotivoDescricao().isEmpty() ? "Não informado" : activity.getMotivoDescricao());

            // Atualizar localização
            tvMunicipio.setText(activity.getMunicipio().isEmpty() ? "Não informado" : activity.getMunicipio());

            // Atualizar informações técnicas
            tvCp.setText(activity.getCp().isEmpty() ? "Não informado" : activity.getCp());
            tvCs.setText(activity.getCs().isEmpty() ? "Não informado" : activity.getCs());
            tvModulo.setText(activity.getModulo().isEmpty() ? "Não informado" : activity.getModulo());
            tvDisplay.setText(activity.getDisplayValue().isEmpty() ? "Não informado" : activity.getDisplayValue());
        }
    }
    public void testarObjetoComoJson(Avulso v) {
        // Converter objeto para JSON usando Gson
        Gson gson = new GsonBuilder().serializeNulls().setPrettyPrinting().create();
        String jsonString = gson.toJson(v);

        // Exibir JSON completo nos logs
        android.util.Log.e("JSON_ENVIADO", "=== JSON COMPLETO SENDO ENVIADO ===");
        android.util.Log.e("JSON_ENVIADO", jsonString);
        android.util.Log.e("JSON_ENVIADO", "=== FIM DO JSON ===");
        
        // Log específico dos campos que podem causar duplicação
        android.util.Log.e("DUPLICACAO_CHECK", "=== CAMPOS QUE DETERMINAM UNICIDADE ===");
        android.util.Log.e("DUPLICACAO_CHECK", "avu_display: " + v.getDisplay());
        android.util.Log.e("DUPLICACAO_CHECK", "avu_cp: " + v.getCp());
        android.util.Log.e("DUPLICACAO_CHECK", "avu_cs: " + v.getCs());
        android.util.Log.e("DUPLICACAO_CHECK", "avu_modulo: " + v.getModulo());
        android.util.Log.e("DUPLICACAO_CHECK", "avu_municipioId: " + v.getMunicipioId());
        android.util.Log.e("DUPLICACAO_CHECK", "avu_empreiteiraId: " + v.getEmpreiteiraId());
        android.util.Log.e("DUPLICACAO_CHECK", "avu_motivoId: " + v.getMotivoId());
        android.util.Log.e("DUPLICACAO_CHECK", "avu_tecnico: " + v.getTecnico());
        android.util.Log.e("DUPLICACAO_CHECK", "=== FIM CAMPOS UNICIDADE ===");
        
        System.out.println("=== JSON COMPLETO SENDO ENVIADO ===");
        System.out.println(jsonString);
        System.out.println("=== FIM DO JSON ===");
    }

    private void salvarOrdem() {
        if (getActivity() instanceof NovaOrdemActivity) {
            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();

            String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);
            String nome = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_NOME);

            android.util.Log.e("DEBUG_SALVAR", "=== INICIANDO SALVAMENTO ===");
            android.util.Log.e("DEBUG_SALVAR", "Display atual: " + activity.getDisplayValue());
            android.util.Log.e("DEBUG_SALVAR", "Módulo atual: " + activity.getModulo());
            android.util.Log.e("DEBUG_SALVAR", "Motivo atual: " + activity.getMotivoDescricao() + " (ID: " + activity.getMotivoId() + ")");

            // Criar objeto Avulso a partir dos dados do formulário
            Avulso avulso = activity.criarAvulso();
            avulso.setTecnico(nome);
            testarObjetoComoJson(avulso);
            // Obter token de autenticação


            // Log da URL e headers antes de enviar
            android.util.Log.e("API_REQUEST", "=== ENVIANDO REQUISIÇÃO ===");
            android.util.Log.e("API_REQUEST", "Endpoint: POST /api/Avulsos");
            android.util.Log.e("API_REQUEST", "Token: " + (token != null ? token.substring(0, Math.min(20, token.length())) + "..." : "null"));
            android.util.Log.e("API_REQUEST", "=== FIM REQUEST INFO ===");

            // Enviar para a API
            Call<Object> call = apiCalls.postAvulso(avulso, token);
            call.enqueue(new Callback<Object>() {
                @Override
                public void onResponse(Call<Object> call, Response<Object> response) {
                    // Parar animação de carregamento
                    btnSalvar.stopLoading();

                    if (response.isSuccessful()) {
                        if (getActivity() != null) {
                            // Criar mensagem de sucesso
                            String successMessage = "Ordem salva com sucesso!";

                            // Criar intent para a próxima tela
                            Intent intent = new Intent(getActivity(), DescricaoActivity.class);
                            // Valor padrão para o ID
                            int avulsoId = 0;

                            // Tentar extrair informações da resposta
                            if (response.body() != null) {
                                // Log da resposta completa para depuração
                                android.util.Log.d("AVULSO_DEBUG", "Resposta de sucesso: " + response.body().toString());

                                // Se a resposta for uma string, usar como mensagem de sucesso
                                if (response.body() instanceof String) {
                                    successMessage = (String) response.body();
                                }
                                // Se for um objeto com um campo 'id', extrair o ID
                                else if (response.body() instanceof com.google.gson.internal.LinkedTreeMap) {
                                    try {
                                        com.google.gson.internal.LinkedTreeMap<?, ?> map =
                                                (com.google.gson.internal.LinkedTreeMap<?, ?>) response.body();
                                        if (map.containsKey("id")) {
                                            double idDouble = (double) map.get("id");
                                            avulsoId = (int) idDouble;
                                            android.util.Log.d("AVULSO_DEBUG", "ID do avulso: " + avulsoId);
                                        }
                                    } catch (Exception e) {
                                        android.util.Log.e("AVULSO_DEBUG", "Erro ao extrair ID: " + e.getMessage());
                                    }
                                }
                            }

                            // Exibir mensagem de sucesso
                            ToastUtils.showShortToast(TabConfirmacaoFragment.this, successMessage);

                            // Decidir próxima tela conforme a escolha do usuário
                            Intent nextIntent;
                            if (Boolean.TRUE.equals(foiExecutado)) {
                                // Serviço executado → Descrição da Atividade
                                nextIntent = new Intent(getActivity(), DescricaoActivity.class);
                            } else {
                                // Serviço não executado → Visita não efetivada
                                nextIntent = new Intent(getActivity(), RegistroNaoActivity.class);
                            }
                            nextIntent.putExtra("avulsoId", avulsoId);
                            nextIntent.putExtra("nomeCliente", activity.getNomeCliente());
                            nextIntent.putExtra("uc", activity.getUc());
                            btnSalvar.stopLoading();
                            startActivity(nextIntent);
                            getActivity().finish();
                        }
                    } else {
                        // Tratar erro de duplicata especificamente
                        if (response.code() == 400) {
                            try {
                                if (response.errorBody() != null) {
                                    String errorBody = response.errorBody().string();
                                    
                                    // Log detalhado do erro para análise
                                    android.util.Log.e("DUPLICACAO_ERROR", "=== ERRO DE DUPLICAÇÃO DETECTADO ===");
                                    android.util.Log.e("DUPLICACAO_ERROR", "Código HTTP: " + response.code());
                                    android.util.Log.e("DUPLICACAO_ERROR", "Corpo do erro: " + errorBody);
                                    android.util.Log.e("DUPLICACAO_ERROR", "Dados que causaram duplicação:");
                                    android.util.Log.e("DUPLICACAO_ERROR", "Display: " + activity.getDisplayValue());
                                    android.util.Log.e("DUPLICACAO_ERROR", "CP: " + activity.getCp());
                                    android.util.Log.e("DUPLICACAO_ERROR", "CS: " + activity.getCs());
                                    android.util.Log.e("DUPLICACAO_ERROR", "Módulo: " + activity.getModulo());
                                    android.util.Log.e("DUPLICACAO_ERROR", "Município ID: " + activity.getMunicipioId());
                                    android.util.Log.e("DUPLICACAO_ERROR", "Empreiteira ID: " + activity.getEmpreiteiraId());
                                    android.util.Log.e("DUPLICACAO_ERROR", "Motivo ID: " + activity.getMotivoId());
                                    android.util.Log.e("DUPLICACAO_ERROR", "=== FIM ERRO DUPLICAÇÃO ===");
                                    
                                    // Verificar se é erro de duplicata
                                    if (errorBody.contains("já cadastrado") || errorBody.contains("duplicado")) {
                                        analisarDuplicacao(); // Fazer análise detalhada
                                        mostrarDialogDuplicataDetalhado(errorBody);
                                        return;
                                    }
                                }
                            } catch (Exception e) {
                                android.util.Log.e("AVULSO_ERROR", "Erro ao ler corpo da resposta: " + e.getMessage());
                            }
                        }

                        // Criar mensagem de erro resumida para o Toast
                        String shortErrorMessage = "Erro ao salvar";
                        if (response != null) {
                            shortErrorMessage += " (" + response.code() + ")";
                        }

                        // Criar mensagem de erro detalhada para o log
                        StringBuilder detailedErrorMessage = new StringBuilder("Erro ao salvar avulso:\n");

                        // Adicionar detalhes da resposta
                        if (response != null) {
                            detailedErrorMessage.append("Código HTTP: ").append(response.code()).append("\n");
                            detailedErrorMessage.append("Mensagem: ").append(response.message()).append("\n");

                            // Adicionar headers da resposta
                            detailedErrorMessage.append("Headers:\n");
                            for (String name : response.headers().names()) {
                                detailedErrorMessage.append("  ").append(name).append(": ")
                                        .append(response.headers().get(name)).append("\n");
                            }

                            // Adicionar corpo do erro
                            try {
                                if (response.errorBody() != null) {
                                    String errorBody = response.errorBody().string();
                                    detailedErrorMessage.append("Corpo do erro:\n").append(errorBody).append("\n");

                                    // Adicionar detalhes do erro ao Toast
                                    if (errorBody != null && !errorBody.isEmpty()) {
                                        shortErrorMessage += ": " + errorBody;
                                    }
                                }
                            } catch (Exception e) {
                                detailedErrorMessage.append("Erro ao ler corpo da resposta: ")
                                        .append(e.getMessage()).append("\n");
                            }
                        }

                        // Adicionar detalhes do objeto enviado
                        detailedErrorMessage.append("\nDados enviados:\n")
                                .append("Motivo: ").append(activity.getMotivoDescricao()).append(" (ID: ")
                                .append(activity.getMotivoId()).append(")\n")
                                .append("Display: ").append(activity.getDisplayValue()).append("\n")
                                .append("Município: ").append(activity.getMunicipio()).append(" (ID: ")
                                .append(activity.getMunicipioId()).append(")\n")
                                .append("CP: ").append(activity.getCp()).append("\n")
                                .append("CS: ").append(activity.getCs()).append("\n")
                                .append("Módulo: ").append(activity.getModulo()).append("\n");

                        // Logar erro detalhado e exibir Toast com mensagem resumida
                        ToastUtils.logAndShowError(TabConfirmacaoFragment.this, "AVULSO_ERROR",
                                shortErrorMessage, detailedErrorMessage.toString(), null);
                    }
                }

                @Override
                public void onFailure(Call<Object> call, Throwable t) {
                    // Parar animação de carregamento
                    btnSalvar.stopLoading();

                    // Criar mensagem de erro resumida para o Toast
                    String shortErrorMessage = "Falha na conexão";
                    if (t != null && t.getMessage() != null) {
                        shortErrorMessage += ": " + t.getMessage();
                    }

                    // Criar mensagem de erro detalhada para o log
                    StringBuilder detailedErrorMessage = new StringBuilder("Falha na chamada à API:\n");

                    // Adicionar detalhes da chamada
                    detailedErrorMessage.append("URL: ").append(call.request().url()).append("\n");
                    detailedErrorMessage.append("Método: ").append(call.request().method()).append("\n");

                    // Adicionar headers da requisição
                    detailedErrorMessage.append("Headers da requisição:\n");
                    for (String name : call.request().headers().names()) {
                        detailedErrorMessage.append("  ").append(name).append(": ")
                                .append(call.request().headers().get(name)).append("\n");
                    }

                    // Adicionar detalhes do erro
                    if (t != null) {
                        detailedErrorMessage.append("\nExceção: ").append(t.getClass().getName()).append("\n");
                        detailedErrorMessage.append("Mensagem: ").append(t.getMessage()).append("\n");

                        // Adicionar stack trace
                        detailedErrorMessage.append("\nStack trace:\n");
                        for (StackTraceElement element : t.getStackTrace()) {
                            detailedErrorMessage.append("  ").append(element.toString()).append("\n");
                        }
                    }

                    // Adicionar detalhes do objeto enviado
                    detailedErrorMessage.append("\nDados que seriam enviados:\n")
                            .append("Motivo: ").append(activity.getMotivoDescricao()).append(" (ID: ")
                            .append(activity.getMotivoId()).append(")\n")
                            .append("Display: ").append(activity.getDisplayValue()).append("\n")
                            .append("Município: ").append(activity.getMunicipio()).append(" (ID: ")
                            .append(activity.getMunicipioId()).append(")\n")
                            .append("CP: ").append(activity.getCp()).append("\n")
                            .append("CS: ").append(activity.getCs()).append("\n")
                            .append("Módulo: ").append(activity.getModulo()).append("\n");

                    // Logar erro detalhado e exibir Toast com mensagem resumida
                    ToastUtils.logAndShowError(TabConfirmacaoFragment.this, "AVULSO_ERROR",
                            shortErrorMessage, detailedErrorMessage.toString(), t);
                }
            });
        }
    }

    private void mostrarDialogConfirmacao() {
        if (getActivity() == null) return;

        Dialog dialog = new Dialog(getActivity());
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_confirmar_registro);

        TextView txtTitle = dialog.findViewById(R.id.txtTitle);
        TextView txtMessage = dialog.findViewById(R.id.txtMessage);
        android.widget.Button btnNo = dialog.findViewById(R.id.btnNo);
        android.widget.Button btnSim = dialog.findViewById(R.id.btnSim);

        txtTitle.setText("Confirmar Registro");
        txtMessage.setText("O serviço foi executado?");

        btnNo.setOnClickListener(v -> {
            dialog.dismiss();
            // Fluxo: serviço NÃO executado → RegistroNaoActivity
            foiExecutado = false;
            btnSalvar.startLoading();
            salvarOrdem();
        });

        btnSim.setOnClickListener(v -> {
            dialog.dismiss();
            // Fluxo: serviço executado → DescricaoActivity
            foiExecutado = true;
            btnSalvar.startLoading();
            salvarOrdem();
        });

        dialog.show();
    }

    private void mostrarDialogDuplicata() {
        mostrarDialogDuplicataDetalhado("Registro já existe no sistema");
    }

    private void mostrarDialogDuplicataDetalhado(String errorMessage) {
        if (getActivity() == null) return;

        Dialog dialog = new Dialog(getActivity());
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_confirmar_registro);

        TextView txtTitle = dialog.findViewById(R.id.txtTitle);
        TextView txtMessage = dialog.findViewById(R.id.txtMessage);
        android.widget.Button btnNo = dialog.findViewById(R.id.btnNo);
        android.widget.Button btnSim = dialog.findViewById(R.id.btnSim);

        txtTitle.setText("Registro Duplicado");
        
        // Criar mensagem detalhada com os dados atuais
        NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
        StringBuilder mensagem = new StringBuilder();
        mensagem.append("Já existe um registro com esta combinação de dados:\n\n");
        mensagem.append("• Display: ").append(activity.getDisplayValue()).append("\n");
        mensagem.append("• CP: ").append(activity.getCp().isEmpty() ? "Não informado" : activity.getCp()).append("\n");
        mensagem.append("• CS: ").append(activity.getCs().isEmpty() ? "Não informado" : activity.getCs()).append("\n");
        mensagem.append("• Módulo: ").append(activity.getModulo()).append("\n");
        mensagem.append("• Município: ").append(activity.getMunicipio()).append("\n");
        mensagem.append("\nDeseja alterar os dados e tentar novamente?");
        
        txtMessage.setText(mensagem.toString());
        
        btnNo.setText("Cancelar");
        btnSim.setText("Alterar Dados");

        btnNo.setOnClickListener(v -> {
            dialog.dismiss();
            // Voltar para a tela anterior ou fechar
            if (getActivity() != null) {
                getActivity().finish();
            }
        });

        btnSim.setOnClickListener(v -> {
            dialog.dismiss();
            // Voltar para a primeira aba para alterar os dados
            if (getActivity() instanceof NovaOrdemActivity) {
                ((NovaOrdemActivity) getActivity()).setCurrentItem(0);
                ToastUtils.showShortToast(TabConfirmacaoFragment.this, 
                    "Altere os dados técnicos para evitar duplicação");
            }
        });

        dialog.show();
    }

    /**
     * Método para analisar e sugerir quais campos podem ser alterados para evitar duplicação
     */
    private void analisarDuplicacao() {
        if (getActivity() instanceof NovaOrdemActivity) {
            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
            
            android.util.Log.e("ANALISE_DUPLICACAO", "=== ANÁLISE DE DUPLICAÇÃO ===");
            android.util.Log.e("ANALISE_DUPLICACAO", "Possíveis campos que causam duplicação:");
            android.util.Log.e("ANALISE_DUPLICACAO", "1. Display: " + activity.getDisplayValue() + " (campo obrigatório)");
            android.util.Log.e("ANALISE_DUPLICACAO", "2. CP: " + activity.getCp() + " (pode ser alterado)");
            android.util.Log.e("ANALISE_DUPLICACAO", "3. CS: " + activity.getCs() + " (pode ser alterado)");
            android.util.Log.e("ANALISE_DUPLICACAO", "4. Módulo: " + activity.getModulo() + " (campo obrigatório)");
            android.util.Log.e("ANALISE_DUPLICACAO", "5. Município: " + activity.getMunicipio() + " (ID: " + activity.getMunicipioId() + ")");
            android.util.Log.e("ANALISE_DUPLICACAO", "6. Empreiteira: " + activity.getEmpreiteiraDescricao() + " (ID: " + activity.getEmpreiteiraId() + ")");
            android.util.Log.e("ANALISE_DUPLICACAO", "7. Motivo: " + activity.getMotivoDescricao() + " (ID: " + activity.getMotivoId() + ")");
            
            // Sugestões de alteração
            android.util.Log.e("ANALISE_DUPLICACAO", "SUGESTÕES PARA EVITAR DUPLICAÇÃO:");
            android.util.Log.e("ANALISE_DUPLICACAO", "- Verificar se o Display está correto");
            android.util.Log.e("ANALISE_DUPLICACAO", "- Alterar CP ou CS se não forem obrigatórios");
            android.util.Log.e("ANALISE_DUPLICACAO", "- Verificar se o Módulo está correto");
            android.util.Log.e("ANALISE_DUPLICACAO", "- Considerar se é o mesmo local/cliente");
            android.util.Log.e("ANALISE_DUPLICACAO", "=== FIM ANÁLISE ===");
        }
    }
}
