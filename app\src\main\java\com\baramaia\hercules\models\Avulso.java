package com.baramaia.hercules.models;

import com.google.gson.annotations.SerializedName;

public class Avulso {

    @SerializedName("id")
    private int id;

    @SerializedName("avu_nome")
    private String nome;

    @SerializedName("avu_uc")
    private int uc;

    @SerializedName("avu_logradouro")
    private String logradouro;

    @SerializedName("avu_numero")
    private String numero;

    @SerializedName("avu_complemento")
    private String complemento;

    @SerializedName("avu_cp")
    private int cp;

    @SerializedName("avu_et")
    private int et;

    @SerializedName("avu_cs")
    private int cs;

    @SerializedName("avu_pos1")
    private String pos1;

    @SerializedName("avu_pos2")
    private String pos2;

    @SerializedName("avu_pos3")
    private String pos3;

    @SerializedName("avu_modulo")
    private int modulo;

    @SerializedName("avu_display")
    private String display;

    @SerializedName("avu_municipioId")
    private int municipioId;

    @SerializedName("municipio")
    private String municipio;

    @SerializedName("avu_nucleoId")
    private int nucleoId;

    @SerializedName("nucleo")
    private String nucleo;

    @SerializedName("avu_tanque")
    private int tanque;

    @SerializedName("avu_motivoId")
    private int motivoId;

    public String getTecnico() {
        return tecnico;
    }

    public void setTecnico(String tecnico) {
        this.tecnico = tecnico;
    }

    @SerializedName("avu_tecnico")
    private String tecnico;

    @SerializedName("motivo")
    private MotivoAvulso motivo;

    @SerializedName("avu_empreiteiraId")
    private int empreiteiraId;

    @SerializedName("empreiteira")
    private String empreiteira;

    // Getters e Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public int getUc() {
        return uc;
    }

    public void setUc(int uc) {
        this.uc = uc;
    }

    public String getLogradouro() {
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public int getCp() {
        return cp;
    }

    public void setCp(int cp) {
        this.cp = cp;
    }

    public int getEt() {
        return et;
    }

    public void setEt(int et) {
        this.et = et;
    }

    public int getCs() {
        return cs;
    }

    public void setCs(int cs) {
        this.cs = cs;
    }

    public String getPos1() {
        return pos1;
    }

    public void setPos1(String pos1) {
        this.pos1 = pos1;
    }

    public String getPos2() {
        return pos2;
    }

    public void setPos2(String pos2) {
        this.pos2 = pos2;
    }

    public String getPos3() {
        return pos3;
    }

    public void setPos3(String pos3) {
        this.pos3 = pos3;
    }

    public int getModulo() {
        return modulo;
    }

    public void setModulo(int modulo) {
        this.modulo = modulo;
    }

    public String getDisplay() {
        return display;
    }

    public void setDisplay(String display) {
        this.display = display;
    }

    public int getMunicipioId() {
        return municipioId;
    }

    public void setMunicipioId(int municipioId) {
        this.municipioId = municipioId;
    }

    public String getMunicipio() {
        return municipio;
    }

    public void setMunicipio(String municipio) {
        this.municipio = municipio;
    }

    public int getNucleoId() {
        return nucleoId;
    }

    public void setNucleoId(int nucleoId) {
        this.nucleoId = nucleoId;
    }

    public String getNucleo() {
        return nucleo;
    }

    public void setNucleo(String nucleo) {
        this.nucleo = nucleo;
    }

    public int getTanque() {
        return tanque;
    }

    public void setTanque(int tanque) {
        this.tanque = tanque;
    }
    public int getMotivoId() {
        return motivoId;
    }

    public void setMotivoId(int motivoId) {
        this.motivoId = motivoId;
    }

    public MotivoAvulso getMotivo() {
        return motivo;
    }

    public void setMotivo(MotivoAvulso motivo) {
        this.motivo = motivo;
    }

    public int getEmpreiteiraId() {
        return empreiteiraId;
    }

    public void setEmpreiteiraId(int empreiteiraId) {
        this.empreiteiraId = empreiteiraId;
    }

    public String getEmpreiteira() {
        return empreiteira;
    }

    public void setEmpreiteira(String empreiteira) {
        this.empreiteira = empreiteira;
    }

}
