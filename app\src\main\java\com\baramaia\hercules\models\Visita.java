package com.baramaia.hercules.models;

import androidx.annotation.Nullable;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

@Entity
public class Visita {
    @PrimaryKey(autoGenerate = true)
    int id;
    int vis_ordId;
    @Nullable
    String vis_data;
    int vis_efetivado;
    int vis_eqpVisitaId;
    @Nullable
    String vis_descricao;
    @Nullable
    String vis_problemas;
    @Nullable
    String vis_observacao;
    @Nullable
    String vis_gps;
    @Nullable
    public String fotos; // JSON string ao invés de List
    @Nullable
    public String eqpInstalados; // JSON string ao invés de List
    @Nullable
    public String eqpRetirados; // JSON string ao invés de List
    @Nullable
    public String motivo; // JSON string ao invés de objeto
    @Nullable
    public String ordens; // JSON string ao invés de List
    boolean vis_status;
    int avulsoId;

    // Construtor padrão que inicializa campos com valores seguros
    public Visita() {
        this.vis_data = "";
        this.vis_descricao = "";
        this.vis_problemas = "";
        this.vis_observacao = "";
        this.vis_gps = "";
        this.fotos = "[]"; // JSON array vazio
        this.eqpInstalados = "[]"; // JSON array vazio
        this.eqpRetirados = "[]"; // JSON array vazio
        this.ordens = "[]"; // JSON array vazio
        this.motivo = null; // Pode ser null
        this.vis_status = false;
        this.avulsoId = 0;
        this.vis_efetivado = 0;
        this.vis_eqpVisitaId = 0;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getVis_ordId() {
        return vis_ordId;
    }

    public void setVis_ordId(int vis_ordId) {
        this.vis_ordId = vis_ordId;
    }

    public String getVis_data() {
        return vis_data;
    }

    public void setVis_data(String vis_data) {
        this.vis_data = vis_data;
    }

    public int getVis_efetivado() {
        return vis_efetivado;
    }

    public void setVis_efetivado(int vis_efetivado) {
        this.vis_efetivado = vis_efetivado;
    }

    public int getVis_eqpVisitaId() {
        return vis_eqpVisitaId;
    }

    public void setVis_eqpVisitaId(int vis_eqpVisitaId) {
        this.vis_eqpVisitaId = vis_eqpVisitaId;
    }

    public String getVis_descricao() {
        return vis_descricao;
    }

    public void setVis_descricao(String vis_descricao) {
        this.vis_descricao = vis_descricao;
    }

    public String getVis_problemas() {
        return vis_problemas;
    }

    public void setVis_problemas(String vis_problemas) {
        this.vis_problemas = vis_problemas;
    }

    public String getVis_observacao() {
        return vis_observacao;
    }

    public void setVis_observacao(String vis_observacao) {
        this.vis_observacao = vis_observacao;
    }

    public String getVis_gps() {
        return vis_gps;
    }

    public void setVis_gps(String vis_gps) {
        this.vis_gps = vis_gps;
    }

    public List<Foto> getFotos() {
        if (fotos == null || fotos.isEmpty()) return new ArrayList<>();
        try {
            Type listType = new TypeToken<List<Foto>>() {}.getType();
            List<Foto> result = new Gson().fromJson(fotos, listType);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public void setFotos(List<Foto> fotosList) {
        if (fotosList == null || fotosList.isEmpty()) {
            this.fotos = "[]";
        } else {
            this.fotos = new Gson().toJson(fotosList);
        }
    }

    public List<EquipamentoVisita> getEqpInstalados() {
        if (eqpInstalados == null || eqpInstalados.isEmpty()) return new ArrayList<>();
        try {
            Type listType = new TypeToken<List<EquipamentoVisita>>() {}.getType();
            List<EquipamentoVisita> result = new Gson().fromJson(eqpInstalados, listType);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public void setEqpInstalados(List<EquipamentoVisita> eqpInstaladosList) {
        if (eqpInstaladosList == null || eqpInstaladosList.isEmpty()) {
            this.eqpInstalados = "[]";
        } else {
            this.eqpInstalados = new Gson().toJson(eqpInstaladosList);
        }
    }

    public List<EquipamentoVisita> getEqpRetirados() {
        if (eqpRetirados == null || eqpRetirados.isEmpty()) return new ArrayList<>();
        try {
            Type listType = new TypeToken<List<EquipamentoVisita>>() {}.getType();
            List<EquipamentoVisita> result = new Gson().fromJson(eqpRetirados, listType);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public void setEqpRetirados(List<EquipamentoVisita> eqpRetiradosList) {
        if (eqpRetiradosList == null || eqpRetiradosList.isEmpty()) {
            this.eqpRetirados = "[]";
        } else {
            this.eqpRetirados = new Gson().toJson(eqpRetiradosList);
        }
    }

    public Motivo getMotivo() {
        if (motivo == null || motivo.isEmpty()) return null;
        try {
            return new Gson().fromJson(motivo, Motivo.class);
        } catch (Exception e) {
            return null;
        }
    }

    public void setMotivo(Motivo motivoObj) {
        if (motivoObj == null) {
            this.motivo = null;
        } else {
            this.motivo = new Gson().toJson(motivoObj);
        }
    }

    public List<Integer> getOrdens() {
        if (ordens == null || ordens.isEmpty()) return new ArrayList<>();
        try {
            Type listType = new TypeToken<List<Integer>>() {}.getType();
            List<Integer> result = new Gson().fromJson(ordens, listType);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public void setOrdens(List<Integer> ordensList) {
        if (ordensList == null || ordensList.isEmpty()) {
            this.ordens = "[]";
        } else {
            this.ordens = new Gson().toJson(ordensList);
        }
    }
    public boolean getVis_status() {
        return vis_status;
    }

    public void setVis_status(boolean vis_status) {
        this.vis_status = vis_status;
    }
    public int getAvulsoId() {
        return avulsoId;
    }

    public void setAvulsoId(int avulsoId) {
        this.avulsoId = avulsoId;
    }

    // Métodos auxiliares para conversão para API (retorna objetos reais)
    public List<Foto> getFotosForApi() {
        return getFotos(); // Já retorna List<Foto>
    }

    public List<EquipamentoVisita> getEqpInstaladosForApi() {
        return getEqpInstalados(); // Já retorna List<EquipamentoVisita>
    }

    public List<EquipamentoVisita> getEqpRetiradosForApi() {
        return getEqpRetirados(); // Já retorna List<EquipamentoVisita>
    }

    public Motivo getMotivoForApi() {
        return getMotivo(); // Já retorna Motivo
    }

    public List<Integer> getOrdensForApi() {
        return getOrdens(); // Já retorna List<Integer>
    }
}
