package com.baramaia.hercules.Data;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;

import com.baramaia.hercules.DAO.EquipamentoVisitaDao;
import com.baramaia.hercules.DAO.VisitaDao;
import com.baramaia.hercules.models.Converters;
import com.baramaia.hercules.models.EquipamentoVisita;
import com.baramaia.hercules.models.Visita;

@Database(entities = {EquipamentoVisita.class, Visita.class}, version = 7, exportSchema = false)
public abstract class AppDatabase extends RoomDatabase {
    private static volatile AppDatabase INSTANCE;

    public abstract EquipamentoVisitaDao equipamentoVisitaDao();
    public abstract VisitaDao visitaDao();

    public static AppDatabase getDatabase(Context context) {
        if (INSTANCE == null) {
            synchronized (AppDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(context.getApplicationContext(),
                                    AppDatabase.class, "app_database")
                            .fallbackToDestructiveMigration()
                            .build();
                }
            }
        }
        return INSTANCE;
    }
}