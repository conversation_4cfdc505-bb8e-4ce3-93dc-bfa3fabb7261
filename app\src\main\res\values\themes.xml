<resources>
    <!-- Base application theme -->
    <style name="Theme.HerculesTestes" parent="Theme.Material3.Light.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/blue_gray_800</item>
        <item name="colorPrimaryContainer">@color/blue_gray_800</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/blue_gray_800</item>
        <item name="colorSecondaryContainer">@color/blue_gray_800</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color -->
        <item name="android:statusBarColor">@color/custom_border_color</item>
        <item name="android:windowLightStatusBar">false</item>
        <!-- Text appearance -->
        <item name="textAppearanceHeadlineMedium">@style/TextAppearance.Material3.HeadlineMedium</item>
        <item name="textAppearanceBodyLarge">@style/TextAppearance.Material3.BodyLarge</item>
        <item name="textAppearanceBodyMedium">@style/TextAppearance.Material3.BodyMedium</item>
        <!-- Button style -->
        <item name="materialButtonStyle">@style/Widget.App.Button</item>
        <!-- Cores de fundo e superfície -->
        <item name="android:colorBackground">@color/background_gray</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorSurfaceVariant">@color/background_gray</item>
    </style>
    <!-- Adicione este estilo personalizado -->
    <style name="Widget.App.Button" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/blue_gray_800</item>
        <item name="android:textColor">@color/white</item>
    </style>
</resources>