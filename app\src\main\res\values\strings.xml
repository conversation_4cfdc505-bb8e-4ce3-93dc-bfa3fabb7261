<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<resources>
    <string name="app_name">Hercules</string>
    <string name="loading">Loading…</string>
    <string name="no_internet_connection">Sem conexão com a internet!</string>
    <string name="lbl_info_1">Info 1</string>
    <string name="lbl_n_medidor">Nº EDP</string>
    <string name="lbl_n_display">Nº Display</string>
    <string name="lbl_cs_123">CS 123</string>
    <string name="lbl_na_123456">NA 123456</string>
    <string name="lbl_vila_uni_o">Vila União</string>
    <string name="lbl_badges">Badges</string>
    <string name="lbl_label4">Label4</string>
    <string name="lbl_label3">Label3</string>
    <string name="lbl_label5">Label5</string>
    <string name="msg_quais_foram_os_problemas">Quais foram os problemas encontrados?</string>
    <string name="msg_atividade_realizada">Descreva as atividades realizadas:</string>
    <string name="lbl_label2">Label2</string>
    <string name="lbl_label1">Label1</string>
    <string name="msg_registre_a_visita">Registre a visita</string>
    <string name="msg_rua_prefeito_almeir_o">Rua Prefeito Almeirão, 1245</string>
    <string name="msg_ordens_de_servi_o">Ordens de Serviço</string>
    <string name="msg_cumbica_ii_cp">Cumbica II - CP 123</string>
    <string name="msg_cabecalho2">ET 5658565 - Rotary Itq</string>
    <string name="lbl_id_557637">ID 557637</string>
    <string name="lbl_pos_1">POS 1</string>
    <string name="msg_esqueceu_a_senha">Esqueceu a senha ?</string>
    <string name="msg_descri_o_atividade">Descrição Atividade</string>
    <string name="lbl_guarulhos">Guarulhos</string>
    <string name="lbl_display">Display: </string>
    <string name="msg_marcia_moraes_magalh_es">Marcia Moraes Magalhães</string>
    <string name="lbl_descreva">Descreva.</string>
    <string name="lbl_uc_150150150">UC: 150150150</string>
    <string name="lbl_observa_o">Observação:</string>
    <string name="msg_ns_2024110007">CS 10 - Retirada de medidor</string>
    <string name="lbl_imovel_fechado">Imóvel fechado</string>
    <string name="lbl_cliente_recusou">Cliente recusou</string>
    <string name="lbl_medidor_grandes">Medidor grandes consumidores</string>
    <string name="lbl_necessario_desligamento">Necessário desligamento do trafo</string>
    <string name="lbl_adequacao_entrada">Adequação do padrão de entrada</string>
    <string name="lbl_local_inacessivel">CS/Medidor inacessivel</string>
    <string name="lbl_medidor">Medidor: </string>
    <string name="msg_visita_efetivada">Visita Efetivada</string>
    <string name="lbl_142536">142536</string>
    <string name="msg_essa_atividade_envolveu">Essa Atividade envolveu outra NS?</string>
    <string name="msg_retirada_de_medidor"> Retirada de Medidor</string>
    <string name="lbl_cp_789">CP 789</string>
    <string name="lbl_uc_1501501502">UC: 150150150</string>
    <string name="lbl_h_rcules">Hércules</string>
    <string name="msg_foi_retirado_algum">Foi retirado algum equipamento?</string>
    <string name="msg_see_design_guideline">See design guideline</string>
    <string name="msg_23_528119372568856">-23.528119372568856, -46.19807504689209</string>
    <string name="lbl_n_o">Não</string>
    <string name="msg_foi_instalado_algum">Foi instalado algum equipamento?</string>
    <string name="lbl_cs_37">CS 37</string>
    <string name="lbl_sim">Sim</string>
    <string name="lbl">+</string>
    <string name="lbl_1a_2b_3c">1A - 2B - 3C</string>
    <string name="lbl_placeholder">Placeholder</string>
    <string name="lbl_entrar">Entrar</string>
    <string name="lbl_ns_2024110006">NS 2024110006</string>
    <string name="lbl_me_lembre">Me lembre</string>
    <string name="msg_entre_com_usuario">Entre com usuario</string>
    <string name="msg_badges_are_used">Os emblemas são usados para transmitir informações dinâmicas, como contagem ou status. Um crachá pode incluir texto, rótulos ou números. </string>
    <string name="lbl_ouvidoria">Ouvidoria</string>
    <string name="msg_finalizar_registro">Finalizar Registro</string>
    <string name="msg_proximo">Próxima</string>
    <string name="msg_visita_n_o_efetivada">Visita não efetivada</string>
    <string name="msg_invers_o_cs_16_pos"> Inversão Cs 16 Pos 11 e 12</string>
    <string name="msg_registrar_visita">Registrar Visita</string>
    <string name="lbl_enviar">Enviar</string>
    <string name="lbl_iniciar_visitas">Iniciar Visitas</string>
    <string name="lbl_comp_n_o_h">Comp: Não Há</string>
    <string name="msg_ambos_os_clientes">Ambos os clientes são modificação. Tentar pegar conta antiga (antes do BTZero), verificar se cada UC ainda tem o mesmo complemento</string>
    <string name="lbl_ns_do_n_cleo">NS do núcleo</string>
    <string name="lbl_display_142536">Display: 142536</string>
    <string name="lbl_rua_j_123"> Rua J, 123 </string>
    <string name="msg_7_02_cs_erro_de"> 7.02 Cs Erro de Comunicação</string>
    <string name="msg_marcos_magalh_es">Marcos Magalhães</string>
    <string name="lbl_login">Login</string>
    <string name="lbl_equipamento">Equipamento</string>
    <string name="lbl_motivo">Motivo:</string>
    <string name="lbl_et_123456">ET 123456</string>
    <string name="lbl_fachada">Fachada</string>
    <string name="lbl_fundos">Fundos</string>
    <string name="lbl_recuperar">Recuperar</string>
    <string name="lbl_senha">Senha</string>
    <string name="lbl_ouvidorias">Ouvidorias</string>
    <string name="msg_entre_com_o_usuario">Usuário</string>
    <string name="lbl_nota_de_servi_o">Nota de serviço</string>
    <string name="lbl_et_667059">ET 667059</string>
    <string name="msg_descreva_as_atividades">Descreva as atividades realizadas:</string>
    <string name="msg_atividade_efetivada">A visita foi efetivada?</string>
    <string name="lbl_medidor_142536">Medidor: 142536</string>
    <string name="lbl_maria_roberta">Maria Roberta</string>
    <string name="label_selecione_opcao">Selecione uma opção</string>
    <string name="label">Selecione uma opção</string>
    <string name="msg_erro_login_expired">Usuário desativado, contate o adminstrador do sistema!</string>
    <!-- Array de prioridades para o spinner -->
    <string-array name="prioridades_array">
        <item>Normal</item>
        <item>Alta</item>
        <item>Urgente</item>
        <item>Emergencial</item>
    </string-array>
</resources>