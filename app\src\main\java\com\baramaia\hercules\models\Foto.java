package com.baramaia.hercules.models;

import androidx.annotation.Nullable;
import androidx.room.ColumnInfo;
import androidx.room.PrimaryKey;

public class Foto {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "foto_id")
    int id;
    @Nullable
    String foto_nome;
    @Nullable
    String foto_extensao;
    @Nullable
    String foto_dado;
    @Nullable
    String foto_tipo;
    @Nullable
    private String foto_dado_base64;

    // Construtor padrão que inicializa campos com valores seguros
    public Foto() {
        this.foto_nome = "";
        this.foto_extensao = "";
        this.foto_dado = "";
        this.foto_tipo = "";
        this.foto_dado_base64 = "";
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getFoto_nome() {
        return foto_nome;
    }

    public void setFoto_nome(String foto_nome) {
        this.foto_nome = foto_nome;
    }

    public String getFoto_extensao() {
        return foto_extensao;
    }

    public void setFoto_extensao(String foto_extensao) {
        this.foto_extensao = foto_extensao;
    }

    public String getFoto_dado() {
        return foto_dado;
    }

    public void setFoto_dado(String foto_dado) {
        this.foto_dado = foto_dado;
    }

    public String getFoto_tipo() {
        return foto_tipo;
    }

    public void setFoto_tipo(String foto_tipo) {
        this.foto_tipo = foto_tipo;
    }

    public String getFoto_dado_base64() {
        return foto_dado_base64;
    }

    public void setFoto_dado_base64(String foto_dado_base64) {
        this.foto_dado_base64 = foto_dado_base64;
    }
}
