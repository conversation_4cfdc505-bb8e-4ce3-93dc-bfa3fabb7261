<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_bottom_sheet_dialog"
    android:orientation="vertical"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp">

    <!-- Informações Principais -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- UC como título -->
        <TextView
            android:id="@+id/txtUc"
            style="@style/TextAppearance.Material3.TitleMedium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="?android:attr/textColorPrimary"
            tools:text="UC: 123456" />

        <!-- Endereço -->
        <TextView
            android:id="@+id/txtEndereco"
            style="@style/TextAppearance.Material3.BodyMedium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="?android:attr/textColorSecondary"
            tools:text="Rua das Flores, 123" />

        <!-- Complemento -->
        <TextView
            android:id="@+id/txtComplemento"
            style="@style/TextAppearance.Material3.BodyMedium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="?android:attr/textColorSecondary"
            tools:text="Apto 101" />
    </LinearLayout>

    <!-- Grid de Informações -->
    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:columnCount="2"
        android:rowCount="3">

        <!-- ET e CS -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="horizontal"
            android:padding="4dp">

            <TextView
                style="@style/TextAppearance.Material3.BodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorSecondary"
                android:text="ET: " />

            <TextView
                android:id="@+id/txtEt"
                style="@style/TextAppearance.Material3.BodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorPrimary"
                tools:text="789" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="horizontal"
            android:padding="4dp">

            <TextView
                style="@style/TextAppearance.Material3.BodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorSecondary"
                android:text="CS: " />

            <TextView
                android:id="@+id/txtCs"
                style="@style/TextAppearance.Material3.BodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorPrimary"
                tools:text="104" />
        </LinearLayout>

        <!-- Módulo e Display -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="horizontal"
            android:padding="4dp">

            <TextView
                style="@style/TextAppearance.Material3.BodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorSecondary"
                android:text="Módulo: " />

            <TextView
                android:id="@+id/txtModulo"
                style="@style/TextAppearance.Material3.BodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorPrimary"
                tools:text="123" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="horizontal"
            android:padding="4dp">

            <TextView
                style="@style/TextAppearance.Material3.BodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorSecondary"
                android:text="Display: " />

            <TextView
                android:id="@+id/txtDisplay"
                style="@style/TextAppearance.Material3.BodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorPrimary"
                tools:text="456" />
        </LinearLayout>

        <!-- Posição -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="horizontal"
            android:padding="4dp">

            <TextView
                style="@style/TextAppearance.Material3.BodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorSecondary"
                android:text="Posição: " />

            <TextView
                android:id="@+id/txtPos1"
                style="@style/TextAppearance.Material3.BodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorPrimary"
                tools:text="789" />
        </LinearLayout>
    </GridLayout>
</LinearLayout>