package com.baramaia.hercules.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.R;
import com.baramaia.hercules.models.Servico;

import java.util.List;

public class ServicoAdapter extends RecyclerView.Adapter<ServicoAdapter.ViewHolder> {
    private List<Servico> servicoList;

    public ServicoAdapter(List<Servico> servicoList) {
        this.servicoList = servicoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.row_servico, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Servico servico = servicoList.get(position);
        holder.txtOrdem.setText(servico.getDescricao());
    }

    @Override
    public int getItemCount() {
        return servicoList.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView txtOrdem;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            txtOrdem = itemView.findViewById(R.id.txtOrdem);
        }
    }
} 