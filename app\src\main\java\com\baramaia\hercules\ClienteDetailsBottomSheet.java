package com.baramaia.hercules;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

public class ClienteDetailsBottomSheet extends BottomSheetDialogFragment {

    private static final String ARG_NOME_CLIENTE = "nome_cliente";
    private static final String ARG_ENDERECO = "endereco";
    private static final String ARG_COMPLEMENTO = "complemento";
    private static final String ARG_UC = "uc";
    private static final String ARG_ET = "et";
    private static final String ARG_CS = "cs";
    private static final String ARG_POS = "pos";
    private static final String ARG_MEDIDOR = "medidor";
    private static final String ARG_DISPLAY = "display";

    public static ClienteDetailsBottomSheet newInstance(
            String nomeCliente, String endereco, String complemento,
            String uc, String et, String cs,
            String pos, String medidor, String display) {
        ClienteDetailsBottomSheet fragment = new ClienteDetailsBottomSheet();
        Bundle args = new Bundle();
        args.putString(ARG_NOME_CLIENTE, nomeCliente);
        args.putString(ARG_ENDERECO, endereco);
        args.putString(ARG_COMPLEMENTO, complemento);
        args.putString(ARG_UC, uc);
        args.putString(ARG_ET, et);
        args.putString(ARG_CS, cs);
        args.putString(ARG_POS, pos);
        args.putString(ARG_MEDIDOR, medidor);
        args.putString(ARG_DISPLAY, display);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.item_cliente_detalhe, container, false);
        view.setBackground(ContextCompat.getDrawable(requireContext(), R.drawable.bg_item_rounded));
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }

        // Recuperando os argumentos
        String nomeCliente = getArguments() != null ? getArguments().getString(ARG_NOME_CLIENTE) : "";
        String endereco = getArguments() != null ? getArguments().getString(ARG_ENDERECO) : "";
        String complemento = getArguments() != null ? getArguments().getString(ARG_COMPLEMENTO) : "";
        String uc = getArguments() != null ? getArguments().getString(ARG_UC) : "";
        String et = getArguments() != null ? getArguments().getString(ARG_ET) : "";
        String cs = getArguments() != null ? getArguments().getString(ARG_CS) : "";
        String pos = getArguments() != null ? getArguments().getString(ARG_POS) : "";
        String medidor = getArguments() != null ? getArguments().getString(ARG_MEDIDOR) : "";
        String display = getArguments() != null ? getArguments().getString(ARG_DISPLAY) : "";

        // Encontrar as views
        TextView txtUc = view.findViewById(R.id.txtUc);
        TextView txtEndereco = view.findViewById(R.id.txtEndereco);
        TextView txtComplemento = view.findViewById(R.id.txtComplemento);
        TextView txtEt = view.findViewById(R.id.txtEt);
        TextView txtCs = view.findViewById(R.id.txtCs);
        TextView txtPos1 = view.findViewById(R.id.txtPos1);
        TextView txtModulo = view.findViewById(R.id.txtModulo);
        TextView txtDisplay = view.findViewById(R.id.txtDisplay);

        if (medidor.equals("0")){
            txtModulo.setVisibility(View.GONE);
        }
        if (display.equals("null")){
            txtDisplay.setVisibility(View.GONE);
        }
        // Configurar os dados
        txtUc.setText("UC: " + uc);
        txtEndereco.setText(endereco);
        txtComplemento.setText(complemento);
        txtEt.setText("ET: " + et);
        txtCs.setText("CS: " + cs);
        txtPos1.setText("Posição: " + pos);
        txtModulo.setText("Módulo: " + medidor);
        txtDisplay.setText(display);

    }

    // Método opcional para definir o estilo do BottomSheet
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialog);
    }
    @Override
    public void onStart() {
        super.onStart();

        if (getDialog() != null) {
            View bottomSheet = ((View) getView().getParent());
            BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(bottomSheet);
            behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
            behavior.setSkipCollapsed(true);
        }
    }

    /*@Override
    public void onStart() {
        super.onStart();

        if (getDialog() != null) {
            View bottomSheet = ((View) getView().getParent());
            bottomSheet.setBackgroundResource(R.drawable.bg_item_rounded);

            BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(bottomSheet);
            behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
            behavior.setSkipCollapsed(true);

            // Altura máxima opcional
            behavior.setMaxHeight((int) (getResources().getDisplayMetrics().heightPixels * 0.90));
        }
    }*/
}