package com.baramaia.hercules.models;

import androidx.room.TypeConverter;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class Converters {
    private static Gson gson = new Gson();

    @TypeConverter
    public static String fromFotoList(List<Foto> fotos) {
        if (fotos == null || fotos.isEmpty()) return "[]";
        return gson.toJson(fotos);
    }

    @TypeConverter
    public static List<Foto> toFotoList(String data) {
        if (data == null || data.isEmpty()) return new ArrayList<>();
        try {
            Type listType = new TypeToken<List<Foto>>() {}.getType();
            List<Foto> result = gson.fromJson(data, listType);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    @TypeConverter
    public static String fromIntegerList(List<Integer> list) {
        if (list == null || list.isEmpty()) return "[]";
        return gson.toJson(list);
    }

    @TypeConverter
    public static List<Integer> toIntegerList(String data) {
        if (data == null || data.isEmpty()) return new ArrayList<>();
        try {
            Type listType = new TypeToken<List<Integer>>() {}.getType();
            List<Integer> result = gson.fromJson(data, listType);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }
    @TypeConverter
    public static String fromEquipamentoVisitaList(List<EquipamentoVisita> list) {
        if (list == null || list.isEmpty()) return "[]";
        Gson gson = new Gson();
        return gson.toJson(list);
    }

    @TypeConverter
    public static List<EquipamentoVisita> toEquipamentoVisitaList(String json) {
        if (json == null || json.isEmpty()) return new ArrayList<>();
        try {
            Gson gson = new Gson();
            Type listType = new TypeToken<List<EquipamentoVisita>>() {}.getType();
            List<EquipamentoVisita> result = gson.fromJson(json, listType);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }
    // Conversor para Motivo
    @TypeConverter
    public static String fromMotivo(Motivo motivo) {
        if (motivo == null) return null; // Motivo pode ser null
        return gson.toJson(motivo);
    }

    @TypeConverter
    public static Motivo toMotivo(String json) {
        if (json == null || json.isEmpty()) return null; // Motivo pode ser null
        try {
            return gson.fromJson(json, Motivo.class);
        } catch (Exception e) {
            return null;
        }
    }
}
