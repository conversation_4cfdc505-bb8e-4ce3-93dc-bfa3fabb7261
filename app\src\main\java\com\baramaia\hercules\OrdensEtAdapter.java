package com.baramaia.hercules;

import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.models.OrdemEt;

import java.util.List;

public class OrdensEtAdapter extends RecyclerView.Adapter<OrdensEtAdapter.ViewHolder> {
    private List<OrdemEt> ordensEtList;

    public OrdensEtAdapter(List<OrdemEt> ordensEtList) {
        this.ordensEtList = ordensEtList;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_ordens_et, parent, false);
        return new ViewHolder(view);
    }
    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        OrdemEt ordemEt = ordensEtList.get(position);
        holder.txtCabecalho.setText(ordemEt.getNucleo() + " - CP " + ordemEt.getCp());
        holder.txtCabecalho2.setText("ET " + ordemEt.getEt() + " - " + ordemEt.getEmpreiteira());

        OrdemServicoAdapter ordemServicoAdapter = new OrdemServicoAdapter(ordemEt.getOrdens(), ordemServico -> {
            // Ação ao clicar em um item de ordem
            Intent intent = new Intent(holder.itemView.getContext(), NotaServicoDetalheActivity.class);
            intent.putExtra("ORDEM_ID", ordemServico.getId());
            intent.putExtra("ORDEM_NS", ordemServico.getOrd_ordem());
            intent.putExtra("ORDEM_CLID", ordemServico.getOrd_cliId());
            intent.putExtra("ORDEM_NOME", ordemServico.getOrd_nome());
            intent.putExtra("ORDEM_LOG", ordemServico.getOrd_logradouro());
            intent.putExtra("ORDEM_NUM", ordemServico.getOrd_numero());
            intent.putExtra("ORDEM_COMP", ordemServico.getOrd_complemento());
            intent.putExtra("ORDEM_UC", ordemServico.getOrd_uc());
            intent.putExtra("ORDEM_ET", ordemServico.getOrd_et());
            intent.putExtra("ORDEM_CS", ordemServico.getOrd_cs());
            intent.putExtra("ORDEM_POS1", ordemServico.getOrd_pos1());
            intent.putExtra("ORDEM_POS2", ordemServico.getOrd_pos2());
            intent.putExtra("ORDEM_POS3", ordemServico.getOrd_pos3());
            intent.putExtra("ORDEM_DISP", ordemServico.getOrd_display());
            intent.putExtra("ORDEM_MED", ordemServico.getOrd_modulo());
            intent.putExtra("ORDEM_SERV", ordemServico.getOrd_tipoServ());
            intent.putExtra("ORDEM_MOT", ordemServico.getOrd_motivo());
            intent.putExtra("ORDEM_OBS", ordemServico.getOrd_obs());
            intent.putExtra("ORDEM_GPS", ordemServico.getOrd_gps());
            holder.itemView.getContext().startActivity(intent);
        });

        holder.rvOrdens.setLayoutManager(new LinearLayoutManager(holder.itemView.getContext(), LinearLayoutManager.VERTICAL, false));
        holder.rvOrdens.setAdapter(ordemServicoAdapter);
    }

    @Override
    public int getItemCount() {
        return ordensEtList.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView txtCabecalho;
        TextView txtCabecalho2;
        RecyclerView rvOrdens;

        public ViewHolder(View itemView) {
            super(itemView);
            txtCabecalho = itemView.findViewById(R.id.txtCabecalho);
            txtCabecalho2 = itemView.findViewById(R.id.txtCabecalho2);
            rvOrdens = itemView.findViewById(R.id.rvOrdens);
            // Inicialize os componentes do item aqui
        }
    }
}