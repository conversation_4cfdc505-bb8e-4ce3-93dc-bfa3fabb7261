package com.baramaia.hercules;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;

public class ConfirmarCardActivity extends AppCompatActivity {
    private Button btnSim;
    private Button btnNo;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_confirmar_registro);

        btnSim = findViewById(R.id.btnSim);
        btnNo = findViewById(R.id.btnNo);

        btnSim.setOnClickListener(View -> {
            Intent intentRegSim = new Intent(getApplicationContext(), RegistroSimActivity.class);
            startActivity(intentRegSim);
        });
        
        btnNo.setOnClickListener(View -> {
            Intent intentRegNao = new Intent(getApplicationContext(), RegistroNaoActivity.class);
            startActivity(intentRegNao);
        });
    }
}

/* txtRegistreA - Titulo da tela */

/* txtVocquersalvara - Titulo de pergunta */

/* btnSim - Botão sim */

/* btnNo - Botão não */