package com.baramaia.hercules.models;

import java.util.List;

public class Ouvidoria {
    int id;
    int ouv_na;
    String ouv_data;
    String ouv_municipio;
    String ouv_nucleo;
    String ouv_observacao;
    String ouv_gps;
    int ouv_status;
    int ouv_municipioId;
    int ouv_nucleoId;
    int ouv_ordId;
    List<ClienteOuvidoria> clientes;
    List<Integer>userId;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getOuv_na() {
        return ouv_na;
    }

    public void setOuv_na(int ouv_na) {
        this.ouv_na = ouv_na;
    }

    public String getOuv_data() {
        return ouv_data;
    }

    public void setOuv_data(String ouv_data) {
        this.ouv_data = ouv_data;
    }

    public String getOuv_municipio() {
        return ouv_municipio;
    }

    public void setOuv_municipio(String ouv_municipio) {
        this.ouv_municipio = ouv_municipio;
    }

    public String getOuv_nucleo() {
        return ouv_nucleo;
    }

    public void setOuv_nucleo(String ouv_nucleo) {
        this.ouv_nucleo = ouv_nucleo;
    }
    public int getOuv_ordId() {
        return ouv_ordId;
    }

    public void setOuv_ordId(int ouv_ordId) {
        this.ouv_ordId = ouv_ordId;
    }

    public String getOuv_observacao() {
        return ouv_observacao;
    }

    public void setOuv_observacao(String ouv_observacao) {
        this.ouv_observacao = ouv_observacao;
    }
    public String getOuv_gps() {
        return ouv_gps;
    }

    public void setOuv_gps(String ouv_gps) {
        this.ouv_gps = ouv_gps;
    }
    public int getOuv_status() {
        return ouv_status;
    }

    public void setOuv_status(int ouv_status) {
        this.ouv_status = ouv_status;
    }

    public int getOuv_municipioId() {
        return ouv_municipioId;
    }

    public void setOuv_municipioId(int ouv_municipioId) {
        this.ouv_municipioId = ouv_municipioId;
    }

    public int getOuv_nucleoId() {
        return ouv_nucleoId;
    }

    public void setOuv_nucleoId(int ouv_nucleoId) {
        this.ouv_nucleoId = ouv_nucleoId;
    }

    public List<ClienteOuvidoria> getClientes() {
        return clientes;
    }

    public void setClientes(List<ClienteOuvidoria> clientes) {
        this.clientes = clientes;
    }

    public List<Integer> getUserId() {
        return userId;
    }

    public void setUserId(List<Integer> userId) {
        this.userId = userId;
    }
    public Ouvidoria(int id, int ouv_na, String ouv_data, String ouv_municipio, String ouv_nucleo, String ouv_observacao, int ouv_status) {
        this.id = id;
        this.ouv_na = ouv_na;
        this.ouv_data = ouv_data;
        this.ouv_municipio = ouv_municipio;
        this.ouv_nucleo = ouv_nucleo;
        this.ouv_observacao = ouv_observacao;
        this.ouv_status = ouv_status;
    }
    public Ouvidoria(){

    }
}
