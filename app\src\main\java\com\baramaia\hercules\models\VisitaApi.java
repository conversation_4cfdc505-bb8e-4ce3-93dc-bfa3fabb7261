package com.baramaia.hercules.models;

import java.util.List;

/**
 * Classe DTO para enviar dados da Visita para a API
 * Mantém os campos como objetos reais (não JSON strings)
 */
public class VisitaApi {
    public int id;
    public int vis_ordId;
    public String vis_data;
    public int vis_efetivado;
    public int vis_eqpVisitaId;
    public String vis_descricao;
    public String vis_problemas;
    public String vis_observacao;
    public String vis_gps;
    public List<Foto> fotos;
    public List<EquipamentoVisita> eqpInstalados;
    public List<EquipamentoVisita> eqpRetirados;
    public Motivo motivo;
    public List<Integer> ordens;
    public boolean vis_status;
    public int avulsoId;

    // Construtor padrão para Gson
    public VisitaApi() {
    }

    // Construtor que converte de Visita (banco) para VisitaApi (API)
    public VisitaApi(Visita visita) {
        this.id = visita.getId();
        this.vis_ordId = visita.getVis_ordId();
        this.vis_data = visita.getVis_data();
        this.vis_efetivado = visita.getVis_efetivado();
        this.vis_eqpVisitaId = visita.getVis_eqpVisitaId();
        this.vis_descricao = visita.getVis_descricao();
        this.vis_problemas = visita.getVis_problemas();
        this.vis_observacao = visita.getVis_observacao();
        this.vis_gps = visita.getVis_gps();
        this.fotos = visita.getFotos(); // Converte JSON para List<Foto>
        this.eqpInstalados = visita.getEqpInstalados(); // Converte JSON para List<EquipamentoVisita>
        this.eqpRetirados = visita.getEqpRetirados(); // Converte JSON para List<EquipamentoVisita>
        this.motivo = visita.getMotivo(); // Converte JSON para Motivo
        this.ordens = visita.getOrdens(); // Converte JSON para List<Integer>
        this.vis_status = visita.getVis_status();
        this.avulsoId = visita.getAvulsoId();
    }

    // Getters e setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getVis_ordId() {
        return vis_ordId;
    }

    public void setVis_ordId(int vis_ordId) {
        this.vis_ordId = vis_ordId;
    }

    public String getVis_data() {
        return vis_data;
    }

    public void setVis_data(String vis_data) {
        this.vis_data = vis_data;
    }

    public int getVis_efetivado() {
        return vis_efetivado;
    }

    public void setVis_efetivado(int vis_efetivado) {
        this.vis_efetivado = vis_efetivado;
    }

    public int getVis_eqpVisitaId() {
        return vis_eqpVisitaId;
    }

    public void setVis_eqpVisitaId(int vis_eqpVisitaId) {
        this.vis_eqpVisitaId = vis_eqpVisitaId;
    }

    public String getVis_descricao() {
        return vis_descricao;
    }

    public void setVis_descricao(String vis_descricao) {
        this.vis_descricao = vis_descricao;
    }

    public String getVis_problemas() {
        return vis_problemas;
    }

    public void setVis_problemas(String vis_problemas) {
        this.vis_problemas = vis_problemas;
    }

    public String getVis_observacao() {
        return vis_observacao;
    }

    public void setVis_observacao(String vis_observacao) {
        this.vis_observacao = vis_observacao;
    }

    public String getVis_gps() {
        return vis_gps;
    }

    public void setVis_gps(String vis_gps) {
        this.vis_gps = vis_gps;
    }

    public List<Foto> getFotos() {
        return fotos;
    }

    public void setFotos(List<Foto> fotos) {
        this.fotos = fotos;
    }

    public List<EquipamentoVisita> getEqpInstalados() {
        return eqpInstalados;
    }

    public void setEqpInstalados(List<EquipamentoVisita> eqpInstalados) {
        this.eqpInstalados = eqpInstalados;
    }

    public List<EquipamentoVisita> getEqpRetirados() {
        return eqpRetirados;
    }

    public void setEqpRetirados(List<EquipamentoVisita> eqpRetirados) {
        this.eqpRetirados = eqpRetirados;
    }

    public Motivo getMotivo() {
        return motivo;
    }

    public void setMotivo(Motivo motivo) {
        this.motivo = motivo;
    }

    public List<Integer> getOrdens() {
        return ordens;
    }

    public void setOrdens(List<Integer> ordens) {
        this.ordens = ordens;
    }

    public boolean isVis_status() {
        return vis_status;
    }

    public void setVis_status(boolean vis_status) {
        this.vis_status = vis_status;
    }

    public int getAvulsoId() {
        return avulsoId;
    }

    public void setAvulsoId(int avulsoId) {
        this.avulsoId = avulsoId;
    }
}
