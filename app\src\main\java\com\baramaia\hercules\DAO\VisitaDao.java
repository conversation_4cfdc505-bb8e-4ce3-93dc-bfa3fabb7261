package com.baramaia.hercules.DAO;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.baramaia.hercules.models.Visita;

import java.util.List;

@Dao
public interface VisitaDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Visita visita);

    @Query("SELECT * FROM Visita ORDER BY id DESC")
    List<Visita> getAll();

    @Query("DELETE FROM Visita")
    void deleteAll();

    // ✅ Método corrigido para buscar uma visita pelo `ordId`
    @Query("SELECT * FROM Visita WHERE vis_ordId = :ordId LIMIT 1")
    Visita getVisitaByOrdId(int ordId);
}
