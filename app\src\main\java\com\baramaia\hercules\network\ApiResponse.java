package com.baramaia.hercules.network;

import android.content.Context;
import android.widget.Toast;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

import retrofit2.Response;

public class ApiResponse {
    private int statusCode;
    private String message;
    private String qrCodeUrl;
    private String manualKey;
    private String token;
    public ApiResponse() {
    }
    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
    public String getQrCodeUrl() {
        return qrCodeUrl;
    }

    public void setQrCodeUrl(String qrCodeUrl) {
        this.qrCodeUrl = qrCodeUrl;
    }
    public String getManualKey() {
        return manualKey; // Adicione este getter
    }
    public int status() {
        return statusCode;
    }

    public void message(Context context, Response response) {

        BufferedReader reader = null;
        StringBuilder sb = new StringBuilder();

        reader = new BufferedReader(new InputStreamReader(response.errorBody().byteStream()));
        String line;
        try {
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        message = sb.substring(8).replace("}", "").replace("\"", "");

        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
        //return message;
    }
}
