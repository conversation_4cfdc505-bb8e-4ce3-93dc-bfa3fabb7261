<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Estilo customizado para dropdowns -->
    <style name="CustomDropdownStyle" parent="Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="boxStrokeColor">@color/dropdown_stroke_selector</item>
        <item name="hintTextColor">@color/dropdown_hint_selector</item>
        <item name="endIconTint">@color/custom_border_color</item>
        <item name="cursorColor">@color/custom_border_color</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="boxStrokeWidthFocused">2dp</item>
        <item name="materialThemeOverlay">@style/CustomDropdownThemeOverlay</item>
    </style>

    <!-- Theme overlay para sobrescrever cores do Material Design -->
    <style name="CustomDropdownThemeOverlay">
        <item name="colorPrimary">@color/custom_border_color</item>
        <item name="colorOnSurface">@color/blue_gray_800</item>
        <item name="colorOutline">@color/gray_400</item>
    </style>
</resources>