<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            style="@style/txtRobotoregular22"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:text="Resumo da Ordem"
            android:textColor="@color/blue_gray_800" />

        <!-- Card: Motivo da Visita -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/rectangle_bg_white_a700_radius_12"
            android:elevation="2dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="@style/txtRobotoregular18"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Motivo da Visita"
                    android:textColor="@color/blue_gray_800" />

                <TextView
                    android:id="@+id/tvMotivo"
                    style="@style/txtRobotoregular16"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/gray_600" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Card: Localização -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/rectangle_bg_white_a700_radius_12"
            android:elevation="2dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="@style/txtRobotoregular18"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Localização"
                    android:textColor="@color/blue_gray_800" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/txtRobotoregular16"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Município: "
                        android:textColor="@color/blue_gray_800" />

                    <TextView
                        android:id="@+id/tvMunicipio"
                        style="@style/txtRobotoregular16"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/gray_600" />
                </LinearLayout>


            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
        <!-- Card: Informações Técnicas -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:background="@drawable/rectangle_bg_white_a700_radius_12"
            android:elevation="2dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="@style/txtRobotoregular18"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Informações Técnicas"
                    android:textColor="@color/blue_gray_800" />

                <!-- Linha 1: CP e CS -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/txtRobotoregular16"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="CP: "
                            android:textColor="@color/blue_gray_800" />

                        <TextView
                            android:id="@+id/tvCp"
                            style="@style/txtRobotoregular16"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/gray_600" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/txtRobotoregular16"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="CS: "
                            android:textColor="@color/blue_gray_800" />

                        <TextView
                            android:id="@+id/tvCs"
                            style="@style/txtRobotoregular16"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/gray_600" />
                    </LinearLayout>
                </LinearLayout>

                <!-- Linha 2: Módulo e Display -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/txtRobotoregular16"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Módulo: "
                            android:textColor="@color/blue_gray_800" />

                        <TextView
                            android:id="@+id/tvModulo"
                            style="@style/txtRobotoregular16"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/gray_600" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/txtRobotoregular16"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Display: "
                            android:textColor="@color/blue_gray_800" />

                        <TextView
                            android:id="@+id/tvDisplay"
                            style="@style/txtRobotoregular16"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/gray_600" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnAnteriorTab4"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/button_gray_rounded"
                android:text="Anterior"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <com.baramaia.hercules.widgets.LoadingButton
                android:id="@+id/btnSalvar"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:background="@drawable/button_blue_gray_rounded"
                android:text="Salvar"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>
</ScrollView>