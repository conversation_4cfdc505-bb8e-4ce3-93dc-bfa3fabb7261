<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:padding="16dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="16dp"
            android:text="Endereço"
            android:textSize="16sp"
            android:textStyle="bold" />
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Logradouro"
            android:textStyle="bold" />
        <EditText
            android:id="@+id/etLogradouro"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
            android:hint="Digite o logradouro"
            android:inputType="textPostalAddress"
            android:padding="12dp" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Número"
                    android:textStyle="bold" />
                <EditText
                    android:id="@+id/etNumero"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
                    android:hint="Número"
                    android:inputType="number"
                    android:padding="12dp" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Complemento"
                    android:textStyle="bold" />
                <EditText
                    android:id="@+id/etComplemento"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
                    android:hint="Complemento"
                    android:inputType="text"
                    android:padding="12dp" />
            </LinearLayout>
        </LinearLayout>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Município"
            android:textStyle="bold" />
        <Spinner
            android:id="@+id/spinnerMunicipio"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
            android:padding="12dp"
            android:popupBackground="@android:color/white"
            android:spinnerMode="dropdown" />
        <TextView
            android:id="@+id/tvNucleoLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Núcleo"
            android:textStyle="bold"
            android:visibility="gone" />
        <Spinner
            android:id="@+id/spinnerNucleo"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
            android:padding="12dp"
            android:popupBackground="@android:color/white"
            android:spinnerMode="dropdown"
            android:visibility="gone" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">
            <Button
                android:id="@+id/btnAnteriorTab2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
                android:padding="12dp"
                android:text="Anterior"
                android:textColor="@color/white" />
            <com.github.rygelouv.androidloadingbuttonlib.LoadingButton
                android:id="@+id/btnProximoTab2"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:padding="0dp"
                custom:text="Próximo"
                custom:textColor="@android:color/white"
                custom:textSize="16sp"
                custom:progressColor="@android:color/white"
                custom:background="@drawable/button_blue_gray_rounded" />
        </LinearLayout>
    </LinearLayout>
</ScrollView>