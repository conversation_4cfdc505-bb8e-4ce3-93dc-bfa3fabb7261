package com.baramaia.hercules;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.models.Ouvidoria;

import java.util.List;

public class RowOuvidoriasAdapter extends RecyclerView.Adapter<RowOuvidoriasAdapter.ViewHolder> {

    private List<Ouvidoria> ouvidoriaList;
    private OnItemClickListener listener;
    public interface OnItemClickListener {
        void onItemClick(int id, int ouvOrdId);
    }

    public RowOuvidoriasAdapter(List<Ouvidoria> ouvidoriaList) {
        this.ouvidoriaList = ouvidoriaList;
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.row_ouvidorias, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        Ouvidoria ouvidoria = ouvidoriaList.get(position);

        holder.txtHeaderText.setText("NA " + String.valueOf(ouvidoria.getOuv_na()));
        holder.txtSubheadText.setText(ouvidoria.getOuv_municipio());

        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onItemClick(ouvidoria.getId(),ouvidoria.getOuv_ordId());
            }
        });
    }

    @Override
    public int getItemCount() {
        return ouvidoriaList.size();
    }
    public static class ViewHolder extends RecyclerView.ViewHolder {
        public TextView txtHeaderText;
        public TextView txtSubheadText;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            txtHeaderText = itemView.findViewById(R.id.txtHeaderText);
            txtSubheadText = itemView.findViewById(R.id.txtSubheadText);
        }
    }
}
