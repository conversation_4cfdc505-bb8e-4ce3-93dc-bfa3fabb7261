<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <application
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.HerculesTestes"
        tools:ignore="DataExtractionRules"
        tools:targetApi="31">
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.baramaia.hercules"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
        </provider>
        <activity
            android:name=".LoginActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.HerculesTestes">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".IniciarVisitaActivity"
            android:exported="false"
            android:label="Iniciar Visitas"
            android:theme="@style/Theme.HerculesTestes" />
        <activity
            android:name=".IniciarVisitaOrdemVerticalActivity"
            android:exported="false"
            android:label="Iniciar Visitas"
            android:theme="@style/Theme.HerculesTestes" />
        <activity
            android:name=".OuvidoriaDetalheActivity"
            android:exported="false"
            android:label="Ouvidorias"
            android:theme="@style/Theme.HerculesTestes" />
        <activity
            android:name=".NotaServicoDetalheActivity"
            android:exported="false"
            android:label="Ordens de Serviço"
            android:theme="@style/Theme.HerculesTestes" />
        <activity
            android:name=".ConfirmarCardActivity"
            android:exported="false"
            android:theme="@style/Theme.HerculesTestes" />
        <activity
            android:name=".RegistroSimActivity"
            android:exported="false"
            android:label="Registrar Visita Efetivada"
            android:theme="@style/Theme.HerculesTestes"
            android:screenOrientation="portrait"/>
        <activity
            android:name=".RegistroNaoActivity"
            android:exported="false"
            android:label="Registrar Visita Não Efetivada"
            android:theme="@style/Theme.HerculesTestes"
            android:screenOrientation="portrait"/>
        <activity
            android:name=".DescricaoActivity"
            android:exported="false"
            android:label="Descrição de Atividade"
            android:theme="@style/Theme.HerculesTestes"
            android:screenOrientation="portrait"/>
        <activity
            android:name=".ImagemCard"
            android:exported="false"
            android:theme="@style/Theme.HerculesTestes" />
        <activity
            android:name=".ClienteOuvidoriaCardActivity"
            android:exported="false"
            android:theme="@style/Theme.HerculesTestes" />
        <activity
            android:name=".RecuperarActivity"
            android:exported="false"
            android:theme="@style/Theme.HerculesTestes" />
        <activity
            android:name=".NovaOrdemActivity"
            android:exported="false"
            android:label="Nova Ordem"
            android:theme="@style/Theme.HerculesTestes" />
    </application>
</manifest>