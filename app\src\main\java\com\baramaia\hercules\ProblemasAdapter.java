package com.baramaia.hercules;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

public class ProblemasAdapter extends RecyclerView.Adapter<ProblemasAdapter.ProblemaViewHolder> {
    
    private List<ProblemaItem> problemas;
    private OnProblemaSelectedListener listener;
    
    public interface OnProblemaSelectedListener {
        void onProblemaSelected(int position, boolean isSelected, String texto);
    }
    
    public static class ProblemaItem {
        private String texto;
        private boolean selecionado;
        private String textoDescricao;
        
        public ProblemaItem(String texto, String textoDescricao) {
            this.texto = texto;
            this.textoDescricao = textoDescricao;
            this.selecionado = false;
        }
        
        // Getters e setters
        public String getTexto() { return texto; }
        public void setTexto(String texto) { this.texto = texto; }
        public boolean isSelecionado() { return selecionado; }
        public void setSelecionado(boolean selecionado) { this.selecionado = selecionado; }
        public String getTextoDescricao() { return textoDescricao; }
        public void setTextoDescricao(String textoDescricao) { this.textoDescricao = textoDescricao; }
    }
    
    public ProblemasAdapter(List<ProblemaItem> problemas, OnProblemaSelectedListener listener) {
        this.problemas = problemas;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public ProblemaViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_checkbox_problema, parent, false);
        return new ProblemaViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ProblemaViewHolder holder, int position) {
        ProblemaItem problema = problemas.get(position);
        holder.checkbox.setText(problema.getTexto());
        holder.checkbox.setChecked(problema.isSelecionado());
        
        holder.checkbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            problema.setSelecionado(isChecked);
            if (listener != null) {
                listener.onProblemaSelected(position, isChecked, problema.getTextoDescricao());
            }
        });
    }
    
    @Override
    public int getItemCount() {
        return problemas.size();
    }
    
    public static class ProblemaViewHolder extends RecyclerView.ViewHolder {
        CheckBox checkbox;
        
        public ProblemaViewHolder(@NonNull View itemView) {
            super(itemView);
            checkbox = itemView.findViewById(R.id.checkbox);
        }
    }
}