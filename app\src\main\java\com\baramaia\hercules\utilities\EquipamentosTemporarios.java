package com.baramaia.hercules.utilities;

import com.baramaia.hercules.models.EquipamentoVisita;
import java.util.List;

public class EquipamentosTemporarios {
    private static EquipamentosTemporarios instance;
    private List<EquipamentoVisita> equipamentosRetirados;
    private List<EquipamentoVisita> equipamentosInstalados;

    private EquipamentosTemporarios() {}

    public static EquipamentosTemporarios getInstance() {
        if (instance == null) {
            instance = new EquipamentosTemporarios();
        }
        return instance;
    }

    public void setEquipamentosRetirados(List<EquipamentoVisita> equipamentos) {
        this.equipamentosRetirados = equipamentos;
    }

    public void setEquipamentosInstalados(List<EquipamentoVisita> equipamentos) {
        this.equipamentosInstalados = equipamentos;
    }

    public List<EquipamentoVisita> getEquipamentosRetirados() {
        return equipamentosRetirados;
    }

    public List<EquipamentoVisita> getEquipamentosInstalados() {
        return equipamentosInstalados;
    }

    public void clear() {
        equipamentosRetirados = null;
        equipamentosInstalados = null;
    }
} 