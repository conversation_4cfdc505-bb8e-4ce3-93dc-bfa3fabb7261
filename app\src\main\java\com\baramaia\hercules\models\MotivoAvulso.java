package com.baramaia.hercules.models;

import android.util.Log;

import com.google.gson.annotations.SerializedName;

public class MotivoAvulso {
    private static final String TAG = "Motivo";

    @SerializedName("id")
    private int id;

    @SerializedName("mav_descricao")
    private String mavDescricao;

    // Campo para armazenar a descrição formatada
    private transient String descricaoFormatada;

    // Getters e Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getMavDescricao() {
        return mavDescricao;
    }

    public void setMavDescricao(String mavDescricao) {
        this.mavDescricao = mavDescricao;
        Log.d(TAG, "setMavDescricao: " + mavDescricao);
    }

    public String getDescricao() {
        // Se a descrição formatada estiver definida, retorná-la
        if (descricaoFormatada != null) {
            return descricaoFormatada;
        }

        // <PERSON><PERSON><PERSON> contrá<PERSON>, retornar a descrição original
        if (mavDescricao != null) {
            return mavDescricao;
        }

        // Se não houver descrição, retornar um valor padrão
        return "Motivo " + id;
    }

    public void setDescricao(String descricao) {
        this.descricaoFormatada = descricao;
        Log.d(TAG, "setDescricao: " + descricao);
    }

    @Override
    public String toString() {
        return getDescricao();
    }
}
