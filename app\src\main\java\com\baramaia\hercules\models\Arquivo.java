package com.baramaia.hercules.models;

public class Arquivo {
    int id;
    String arq_nome;
    String arq_extensao;
    byte[] arq_dado;
    String arq_tipo;
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getArq_nome() {
        return arq_nome;
    }

    public void setArq_nome(String arq_nome) {
        this.arq_nome = arq_nome;
    }

    public String getArq_extensao() {
        return arq_extensao;
    }

    public void setArq_extensao(String arq_extensao) {
        this.arq_extensao = arq_extensao;
    }

    public byte[] getArq_dado() {
        return arq_dado;
    }

    public void setArq_dado(byte[] arq_dado) {
        this.arq_dado = arq_dado;
    }

    public String getArq_tipo() {
        return arq_tipo;
    }

    public void setArq_tipo(String arq_tipo) {
        this.arq_tipo = arq_tipo;
    }
}
