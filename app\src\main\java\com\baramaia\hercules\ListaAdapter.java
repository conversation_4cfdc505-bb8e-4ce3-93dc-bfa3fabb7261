package com.baramaia.hercules;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.models.ClienteOuvidoria;

import java.util.List;

public class ListaAdapter extends RecyclerView.Adapter<ListaAdapter.ViewHolder> {

    private List<ClienteOuvidoria> clientes;
    private OnItemClickListener listener;

    public interface OnItemClickListener {
        void onItemClick(ClienteOuvidoria cliente);
    }

    public ListaAdapter(List<ClienteOuvidoria> clientes, OnItemClickListener listener) {
        this.clientes = clientes;
        this.listener = listener;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private TextView textView;

        public ViewHolder(View view) {
            super(view);
            textView = view.findViewById(R.id.txtTitular);
            
            view.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && listener != null) {
                    listener.onItemClick(clientes.get(position));
                }
            });
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.row_cliente_ouvidoria, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ClienteOuvidoria cliente = clientes.get(position);
        holder.textView.setText(cliente.getOuv_titular());
    }

    @Override
    public int getItemCount() {
        return clientes.size();
    }
}