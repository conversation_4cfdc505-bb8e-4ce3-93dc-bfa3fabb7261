<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="065aadfd-fa5f-4101-98a5-0d64d58bb429" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/../.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/appInsightsSettings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/appInsightsSettings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/deploymentTargetSelector.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/deploymentTargetSelector.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/gradle.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/gradle.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/inspectionProfiles/Project_Default.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/inspectionProfiles/Project_Default.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/kotlinc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/kotlinc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/material_theme_project_new.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/material_theme_project_new.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/migrations.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/migrations.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/studiobot.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/studiobot.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/vcs.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/build.gradle.kts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proguard-rules.pro" beforeDir="false" afterPath="$PROJECT_DIR$/proguard-rules.pro" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/androidTest/java/com/example/herculestestes/ExampleInstrumentedTest.kt" beforeDir="false" afterPath="$PROJECT_DIR$/src/androidTest/java/com/example/herculestestes/ExampleInstrumentedTest.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ClienteDetailsBottomSheet.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ClienteDetailsBottomSheet.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ClienteDetalhe.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ClienteDetalhe.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ClienteOuvidoriaCardActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ClienteOuvidoriaCardActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ConfirmarCardActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ConfirmarCardActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/DescricaoActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/DescricaoActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/DescricaoNsAdapter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/DescricaoNsAdapter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ImagemCard.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ImagemCard.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/IniciarVisitaActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/IniciarVisitaActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/IniciarVisitaOrdemVerticalActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/IniciarVisitaOrdemVerticalActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ListItem.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ListItem.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ListaAdapter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ListaAdapter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/LoginActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/LoginActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/MotivosAdapter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/MotivosAdapter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/NotaServicoDetalheActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/NotaServicoDetalheActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/OnItemClickListener.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/OnItemClickListener.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/OrdemServicoAdapter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/OrdemServicoAdapter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/OrdensEtAdapter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/OrdensEtAdapter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/OuvidoriaDetalheActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/OuvidoriaDetalheActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/RecuperarActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/RecuperarActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/RecyclerItemClickListener.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/RecyclerItemClickListener.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/RegistroNaoActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/RegistroNaoActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/RegistroSimActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/RegistroSimActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/RowOuvidoriasAdapter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/RowOuvidoriasAdapter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/SkeletonAdapter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/SkeletonAdapter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/adapters/ServicoAdapter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/adapters/ServicoAdapter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/adapters/SkeletonAdapter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/adapters/SkeletonAdapter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/app/appcomponents/ui/CustomBindingAdapter.kt" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/app/appcomponents/ui/CustomBindingAdapter.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/app/appcomponents/ui/RecyclerItemDecoration.kt" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/app/appcomponents/ui/RecyclerItemDecoration.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Arquivo.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Arquivo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/ClienteOuvidoria.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/ClienteOuvidoria.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Equipamento.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Equipamento.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/EquipamentoVisita.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/EquipamentoVisita.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Foto.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Foto.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Motivo.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Motivo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/OrdemEt.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/OrdemEt.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/OrdemServico.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/OrdemServico.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Ouvidoria.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Ouvidoria.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Servico.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Servico.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/UserLogin.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/UserLogin.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/ViewOrdemServico.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/ViewOrdemServico.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Visita.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/models/Visita.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/network/ApiCalls.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/network/ApiCalls.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/network/ApiDados.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/network/ApiDados.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/network/ApiResponse.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/network/ApiResponse.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ui/theme/Theme.kt" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/ui/theme/Theme.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/utilities/EquipamentosTemporarios.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/utilities/EquipamentosTemporarios.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/utilities/RowOrdensAdapter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/utilities/RowOrdensAdapter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/utilities/SharedPrefHercules.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/herculestestes/utilities/SharedPrefHercules.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/color/spinner_background_selector.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/color/spinner_background_selector.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/color/spinner_stroke_selector.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/color/spinner_stroke_selector.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/color/switch_thumb_selector.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/color/switch_thumb_selector.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/color/switch_track_selector.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/color/switch_track_selector.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable-v24/ic_launcher_foreground.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable-v24/ic_launcher_foreground.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/bg_bottom_sheet_dialog.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/bg_bottom_sheet_dialog.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/bg_dialog_rounded.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/bg_dialog_rounded.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/bg_item_alternate.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/bg_item_alternate.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/bg_item_rounded.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/bg_item_rounded.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/blur_background.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/blur_background.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/cursor_color.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/cursor_color.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/dialog_background.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/dialog_background.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/divider_servico.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/divider_servico.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/drawable_img_close.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/drawable_img_close.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/edittext_background.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/edittext_background.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_check_circle.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_check_circle.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_chevron_right.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_chevron_right.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_close_circle.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_close_circle.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_dropdown_arrow.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_dropdown_arrow.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_launcher_background.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_launcher_background.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_launcher_foreground.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_launcher_foreground.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_loading.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_loading.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_loading_animation.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_loading_animation.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_ordem_servico.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_ordem_servico.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_ouvidoria.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_ouvidoria.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_photo_library.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_photo_library.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/ic_placeholder.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/ic_placeholder.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_arrow_left.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_arrow_left.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_arrow_right.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_arrow_right.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_arrowdown.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_arrowdown.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_arrowdown_blue_gray_900.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_arrowdown_blue_gray_900.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_close.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_close.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_contrast.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_contrast.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_iconizer_camera.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_iconizer_camera.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_line_8.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_line_8.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_lock.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_lock.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_lock_blue_800.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_lock_blue_800.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_lock_blue_800_24x24.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_lock_blue_800_24x24.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_logo.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_logo.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_save_01.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_save_01.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/img_vector.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/img_vector.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/layer_list_bg_gray_50_border_gray_400_radius_8.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/layer_list_bg_gray_50_border_gray_400_radius_8.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/layer_list_bg_white_a700_border_gray_400_radius_8.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/layer_list_bg_white_a700_border_gray_400_radius_8.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/photo_icon.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/photo_icon.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_blue_800_14_radius_12.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_blue_800_14_radius_12.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_blue_gray_50.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_blue_gray_50.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_blue_gray_800_radius_10.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_blue_gray_800_radius_10.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_blue_gray_800_radius_8.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_blue_gray_800_radius_8.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_blue_gray_800_radius_9.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_blue_gray_800_radius_9.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_300.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_300.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_300_02_radius_2.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_300_02_radius_2.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_400_33_radius_10.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_400_33_radius_10.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_500_radius_2.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_500_radius_2.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50_border_blue_gray_100_radius_8.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50_border_blue_gray_100_radius_8.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50_border_gray_400_01_radius_12.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50_border_gray_400_01_radius_12.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50_border_gray_50_radius_8.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50_border_gray_50_radius_8.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50_border_gray_900_33_radius_20.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50_border_gray_900_33_radius_20.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50_radius_12.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_gray_50_radius_12.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_white_a700.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_white_a700.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_white_a700_border_white_a700_radius_8.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_white_a700_border_white_a700_radius_8.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_white_a700_radius_12.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_white_a700_radius_12.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_white_radius_8.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_white_radius_8.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_yellow_100_border_yellow_50_radius_24.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_bg_yellow_100_border_yellow_50_radius_24.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_border_gray_400_01_radius_12.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_border_gray_400_01_radius_12.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_border_gray_50_radius_12.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_border_gray_50_radius_12.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_border_gray_600_radius_4.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_border_gray_600_radius_4.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rectangle_border_gray_800_radius_4.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rectangle_border_gray_800_radius_4.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/rounded_background.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/rounded_background.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/spinner_background.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/spinner_background.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/drawable/spinner_dropdown_background.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/drawable/spinner_dropdown_background.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/font/arial.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/font/arial.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/font/inter.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/font/inter.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/font/montserrat.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/font/montserrat.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/font/poppins.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/font/poppins.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/font/roboto.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/font/roboto.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/font/roboto_condensed.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/font/roboto_condensed.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/font/rubik.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/font/rubik.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/activity_descricao_da_atividade.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/activity_descricao_da_atividade.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/activity_iniciar_visitas.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/activity_iniciar_visitas.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/activity_iniciar_visitas_ordem_vertical.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/activity_iniciar_visitas_ordem_vertical.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/activity_login.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/activity_login.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/activity_notas_de_servi_o_detalhes.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/activity_notas_de_servi_o_detalhes.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/activity_ouvidoria_detalhe.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/activity_ouvidoria_detalhe.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/activity_recuperar.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/activity_recuperar.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/activity_registro_assinalado_n_o.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/activity_registro_assinalado_n_o.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/activity_registro_assinalado_sim.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/activity_registro_assinalado_sim.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/dialog_confirmar_registro.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/dialog_confirmar_registro.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/dialog_escolha_foto.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/dialog_escolha_foto.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/dialog_image.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/dialog_image.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/dialog_imagem.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/dialog_imagem.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/item_cliente_detalhe.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/item_cliente_detalhe.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/item_descricao.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/item_descricao.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/item_equipamento_instalado.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/item_equipamento_instalado.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/item_equipamento_retirado.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/item_equipamento_retirado.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/item_ordem_skeleton.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/item_ordem_skeleton.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/item_ordens_et.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/item_ordens_et.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/item_skeleton.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/item_skeleton.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/item_skeleton_ordem.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/item_skeleton_ordem.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/item_skeleton_ouvidoria.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/item_skeleton_ouvidoria.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/item_spinner.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/item_spinner.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/item_spinner_dropdown.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/item_spinner_dropdown.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/layout_cliente_details_bottom_sheet.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/layout_cliente_details_bottom_sheet.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/row_cliente_ouvidoria.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/row_cliente_ouvidoria.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/row_equipamento_instalado.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/row_equipamento_instalado.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/row_equipamento_retirado.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/row_equipamento_retirado.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/row_motivos.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/row_motivos.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/row_ns.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/row_ns.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/row_ouvidorias.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/row_ouvidorias.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/row_servico.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/row_servico.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/skeleton_equipamento.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/skeleton_equipamento.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/layout/spinner_item.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/layout/spinner_item.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/raw/lottie_loading.json" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/raw/lottie_loading.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/values-h1004dp/dimen.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/values-h1004dp/dimen.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/values-night/themes.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/values-night/themes.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/values/colors.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/values/colors.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/values/dimen.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/values/dimen.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/values/strings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/values/strings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/values/styles.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/values/styles.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/values/themes.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/values/themes.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/xml/backup_rules.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/xml/backup_rules.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/xml/data_extraction_rules.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/xml/data_extraction_rules.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/res/xml/filepaths.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/res/xml/filepaths.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/example/herculestestes/ExampleUnitTest.kt" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/example/herculestestes/ExampleUnitTest.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../build.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/../build.gradle.kts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../gradle.properties" beforeDir="false" afterPath="$PROJECT_DIR$/../gradle.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../gradle/libs.versions.toml" beforeDir="false" afterPath="$PROJECT_DIR$/../gradle/libs.versions.toml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../gradle/wrapper/gradle-wrapper.properties" beforeDir="false" afterPath="$PROJECT_DIR$/../gradle/wrapper/gradle-wrapper.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../gradlew" beforeDir="false" afterPath="$PROJECT_DIR$/../gradlew" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../gradlew.bat" beforeDir="false" afterPath="$PROJECT_DIR$/../gradlew.bat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../settings.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/../settings.gradle.kts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "customColor": "",
  "associatedIndex": 3
}]]></component>
  <component name="ProjectId" id="2uBDPwArtwsUvF9TvVJo6v5pn8b" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "android.gradle.sync.needed": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "main",
    "kotlin-language-version-configured": "true"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="065aadfd-fa5f-4101-98a5-0d64d58bb429" name="Changes" comment="" />
      <created>1741713053443</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1741713053443</updated>
    </task>
    <servers />
  </component>
</project>