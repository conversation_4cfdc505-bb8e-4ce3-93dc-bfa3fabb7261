<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/groupStyleblue_gray_50"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <!-- Header <PERSON><PERSON><PERSON> pad<PERSON><PERSON> da Nova Ordem -->
    <LinearLayout
        android:id="@+id/headerLayout"
        style="@style/groupStyleblue_gray_50"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingTop="16dp"
        android:paddingEnd="16dp"
        android:paddingBottom="16dp">
        <ImageView
            android:id="@+id/imageArrowleftOne"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Voltar"
            android:padding="4dp"
            android:src="@drawable/img_arrow_left" />
        <TextView
            android:id="@+id/txtTitle"
            style="@style/txtRobotoregular22"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="@string/msg_descri_o_atividade"
            tools:text="Descrição Atividade" />
    </LinearLayout>
    <!-- Conteúdo com ScrollView e padding para botão fixo -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:fillViewport="true"
        android:paddingBottom="80dp">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">
            <!-- Card para os inputs seguindo padrão -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/rectangle_bg_white_a700_radius_12"
                android:elevation="2dp"
                android:orientation="vertical"
                android:padding="16dp">
                <!-- Seção de Problemas com Checkboxes -->
                <TextView
                    style="@style/txtRobotoregular16"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Quais foram os problemas encontrados:"
                    android:textColor="@color/blue_gray_800" />
                <!-- Container com ScrollView para checkboxes - Versão mais agressiva -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="280dp"
                    android:background="@drawable/rectangle_bg_gray_50_radius_12"
                    android:orientation="vertical">
                    <!-- Indicador visual de scroll -->
                    <com.baramaia.hercules.CustomScrollView
                        android:id="@+id/scrollViewProblemas"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:fadeScrollbars="false"
                        android:fillViewport="false"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="always"
                        android:padding="12dp"
                        android:scrollbarAlwaysDrawVerticalTrack="true"
                        android:scrollbarStyle="insideOverlay"
                        android:scrollbars="vertical">
                        <LinearLayout
                            android:id="@+id/layoutProblemasContainer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingBottom="8dp">
                            <CheckBox
                                android:id="@+id/cbCadastroIncorreto"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="Cadastro incorreto ou invertido"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbCadastroProvisorio"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="Cadastro provisório"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbCsErroComunicacao"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="CS erro de comunicação ou configuração"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbFalhaContator"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="Falha de acionamento do contador"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbMedidorAntigo"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="Medidor antigo no local"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbMedidorNaoExibido"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="Medidor não é exibido na coletora ou APP"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbMedidorNaoResponde"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="Medidor não responde comando de corte"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbModuloDivergente"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="Módulo ou display divergente do Árteri"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbPosicaoIncorreta"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="Transform Selected Code..."
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbTliAnilhamento"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="TLI anilhamento incorreto OU ausente"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbTliDesligado"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="TLI desligado"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbTliAguarde"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:buttonTint="@color/custom_border_color"
                                android:text="TLI exibindo aguarde"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                            <CheckBox
                                android:id="@+id/cbOutros"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:buttonTint="@color/custom_border_color"
                                android:text="Outros"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </com.baramaia.hercules.CustomScrollView>
                </LinearLayout>
                <!-- Seção de Descrição seguindo padrão -->
                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/msg_atividade_realizada"
                    app:boxStrokeColor="@color/custom_border_color"
                    app:boxStrokeWidth="1dp"
                    app:boxStrokeWidthFocused="2dp"
                    app:hintTextColor="@color/custom_border_color">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtDescricao"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="top"
                        android:inputType="textMultiLine"
                        android:minLines="4"
                        android:textColor="@color/blue_gray_800" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
            <!-- Container da seção "Essa atividade envolveu" - Visível apenas quando há dados -->
            <LinearLayout
                android:id="@+id/containerNsSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">
                <!-- Título da seção melhorado -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/txtEssaatividade"
                        style="@style/txtRobotoregular20"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/msg_essa_atividade_envolveu"
                        android:textColor="@color/blue_gray_800" />
                    <!-- Contador de itens -->
                    <TextView
                        android:id="@+id/txtContadorNs"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/rectangle_bg_blue_gray_800_radius_8"
                        android:paddingStart="8dp"
                        android:paddingTop="4dp"
                        android:paddingEnd="8dp"
                        android:paddingBottom="4dp"
                        android:text="0"
                        android:textColor="@color/white"
                        android:textSize="12sp" />
                </LinearLayout>
                <!-- Card melhorado para o RecyclerView -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">
                        <!-- Header do card -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="12dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">
                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_marginEnd="8dp"
                                android:src="@drawable/ic_ordem_servico"
                                app:tint="@color/custom_border_color" />
                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Notas de Serviço Relacionadas"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="14sp"
                                android:textStyle="bold" />
                        </LinearLayout>
                        <!-- RecyclerView com melhor espaçamento -->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/row_ns"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:nestedScrollingEnabled="false"
                            android:orientation="vertical"
                            android:scrollbarStyle="outsideOverlay" />
                        <!-- Mensagem quando não há itens -->
                        <TextView
                            android:id="@+id/txtEmptyNs"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:padding="24dp"
                            android:text="Nenhuma nota de serviço relacionada"
                            android:textColor="@color/blue_gray_800"
                            android:textSize="14sp"
                            android:visibility="gone" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </LinearLayout>
            <!-- Seção de Fotos Redesenhada -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <View
                    android:layout_width="4dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:background="@color/custom_border_color" />
                <TextView
                    style="@style/txtRobotoregular20"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Fotos do Serviço"
                    android:textColor="@color/blue_gray_800" />
                <!-- Contador de fotos -->
                <TextView
                    android:id="@+id/txtContadorFotos"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/rectangle_bg_blue_gray_800_radius_8"
                    android:paddingStart="8dp"
                    android:paddingTop="4dp"
                    android:paddingEnd="8dp"
                    android:paddingBottom="4dp"
                    android:text="0/2"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>
            <!-- Card para as Fotos -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">
                    <!-- Fotos Obrigatórias -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="Fotos Obrigatórias"
                        android:textColor="@color/blue_gray_800"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="2">
                        <!-- Fachada -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">
                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnFachada"
                                android:layout_width="match_parent"
                                android:layout_height="48dp"
                                android:text="FACHADA"
                                android:textAllCaps="false"
                                android:textColor="@color/white"
                                android:textSize="12sp"
                                app:backgroundTint="@color/custom_border_color"
                                app:cornerRadius="8dp"
                                app:icon="@drawable/img_iconizer_camera"
                                app:iconGravity="textStart"
                                app:iconSize="16dp"
                                app:iconTint="@color/white" />
                            <androidx.cardview.widget.CardView
                                android:id="@+id/cardFachada"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:layout_marginTop="8dp"
                                android:visibility="gone"
                                app:cardCornerRadius="8dp"
                                app:cardElevation="2dp">
                                <ImageView
                                    android:id="@+id/thumbnailFachada"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:contentDescription="Foto da fachada"
                                    android:scaleType="centerCrop" />
                            </androidx.cardview.widget.CardView>
                        </LinearLayout>
                        <!-- Medidor -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">
                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnMedidor"
                                android:layout_width="match_parent"
                                android:layout_height="48dp"
                                android:text="MEDIDOR"
                                android:textAllCaps="false"
                                android:textColor="@color/white"
                                android:textSize="12sp"
                                app:backgroundTint="@color/custom_border_color"
                                app:cornerRadius="8dp"
                                app:icon="@drawable/img_iconizer_camera"
                                app:iconGravity="textStart"
                                app:iconSize="16dp"
                                app:iconTint="@color/white" />
                            <androidx.cardview.widget.CardView
                                android:id="@+id/cardMedidor"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:layout_marginTop="8dp"
                                android:visibility="gone"
                                app:cardCornerRadius="8dp"
                                app:cardElevation="2dp">
                                <ImageView
                                    android:id="@+id/thumbnailMedidor"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:contentDescription="Foto do medidor"
                                    android:scaleType="centerCrop" />
                            </androidx.cardview.widget.CardView>
                        </LinearLayout>
                    </LinearLayout>
                    <!-- Divisor -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="16dp"
                        android:background="@color/gray_300" />
                    <!-- Fotos Adicionais -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">
                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Fotos Adicionais (Opcional)"
                            android:textColor="@color/blue_gray_800"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                        <!-- Botão para adicionar foto -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnAdicionarFoto"
                            style="@style/Widget.Material3.Button.IconButton.Filled"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:insetLeft="0dp"
                            android:insetTop="0dp"
                            android:insetRight="0dp"
                            android:insetBottom="0dp"
                            app:backgroundTint="@color/custom_border_color"
                            app:cornerRadius="20dp"
                            app:icon="@drawable/plusicon"
                            app:iconGravity="textStart"
                            app:iconPadding="0dp"
                            app:iconSize="20dp"
                            app:iconTint="@color/white" />
                    </LinearLayout>
                    <!-- Container para fotos adicionais -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerFotosAdicionais"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        android:orientation="horizontal"
                        android:visibility="gone" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <!-- Botão Finalizar seguindo padrão -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="8dp">
        <com.github.rygelouv.androidloadingbuttonlib.LoadingButton
            android:id="@+id/btnFinalizar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_margin="16dp"
            android:paddingTop="0dp"
            android:paddingBottom="0dp"
            custom:progressColor="@android:color/white"
            custom:text="@string/msg_proximo"
            custom:textColor="@android:color/white"
            custom:textSize="16sp"
            custom:background="@drawable/button_blue_gray_rounded" />
    </FrameLayout>
</LinearLayout>