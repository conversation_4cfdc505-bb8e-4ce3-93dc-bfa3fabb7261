<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">
  <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp">
    <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
      <ImageView
                android:id="@+id/fullscreenImageView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter" />
      <ImageButton
                android:id="@+id/btnClose"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="top|end"
                android:layout_margin="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_close"
                android:tint="@color/white" />
    </FrameLayout>
  </androidx.cardview.widget.CardView>
</FrameLayout>