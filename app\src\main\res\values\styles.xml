<?xml version="1.0" encoding="utf-8" ?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="txtRobotoregular22">
        <item name="android:textSize">@dimen/_22pxh</item>
        <item name="android:fontFamily">@font/roboto</item>6
        <item name="android:textColor">@color/black_900</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtInterregular18">
        <item name="android:textSize">@dimen/_18pxh</item>
        <item name="android:textColor">@color/black_900</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtRobotoregular20">
        <item name="android:textSize">@dimen/_20pxh</item>
        <item name="android:textColor">@color/black_900</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtInterregular16">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/black_900</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtInterregular14">
        <item name="android:textSize">@dimen/_14pxh</item>
        <item name="android:textColor">@color/blue_gray_700</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtRobotoromanregular24">
        <item name="android:textSize">@dimen/_24pxh</item>
        <item name="android:textColor">@color/gray_900_02</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="etSolidRounded_2">
        <item name="android:background">@drawable/rectangle_bg_blue_800_14_radius_12</item>
    </style>

    <style name="btnSolidRounded_3">
        <item name="android:textSize">@dimen/_14pxh</item>
        <item name="android:textColor">@color/white_A700</item>

        <item name="android:textFontWeight" tools:targetApi="p">500</item>
        <item name="android:background">@drawable/rectangle_bg_blue_gray_800_radius_10</item>
    </style>

    <style name="btnSolidRounded_2">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/gray_50</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">600</item>
        <item name="android:background">@drawable/rectangle_bg_blue_gray_800_radius_8</item>
    </style>

    <style name="btnSolidRounded_1" parent="Widget.Material3.Button">
        <item name="android:textSize">@dimen/_14pxh</item>
        <item name="android:textColor">@color/white_A700</item>
        <item name="android:textFontWeight" tools:targetApi="p">500</item>
        <item name="backgroundTint">@color/blue_gray_800</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.App.Button</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>

    <style name="ThemeOverlay.App.Button" parent="">
        <item name="colorPrimary">@color/blue_gray_800</item>
    </style>

    <style name="txtRobotoregular18_1">
        <item name="android:textSize">@dimen/_18pxh</item>
        <item name="android:textColor">@color/black_900</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="groupStylecornerRadius">
        <item name="android:background">@drawable/rectangle_border_gray_400_01_radius_12</item>
    </style>

    <style name="etSolidRounded_1">
        <item name="android:textSize">@dimen/_14pxh</item>
        <item name="android:textColor">@color/gray_50_01</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">500</item>
        <item name="android:background">@drawable/rectangle_bg_blue_gray_800_radius_9</item>
    </style>

    <style name="txtRobotomedium46">
        <item name="android:textSize">@dimen/_46pxh</item>
        <item name="android:textColor">@color/gray_50_01</item>
        <item name="android:textFontWeight" tools:targetApi="p">500</item>
    </style>

    <style name="txtPoppinsmedium12">
        <item name="android:textSize">@dimen/_12pxh</item>
        <item name="android:textColor">@color/blue_gray_900</item>
        <item name="android:fontFamily">@font/poppins</item>
        <item name="android:textFontWeight" tools:targetApi="p">500</item>
    </style>

    <style name="etRobotoregular15">
        <item name="android:textSize">@dimen/_15pxh</item>
        <item name="android:textColor">@color/gray_900_03</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="btnSolidRoundedOutline">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/blue_gray_800_02</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">600</item>
        <item name="android:background">
            @drawable/rectangle_bg_gray_50_border_blue_gray_100_radius_8
        </item>
    </style>

    <style name="groupStylewhite_A700">
        <item name="android:background">@drawable/rectangle_bg_white_a700</item>
    </style>

    <style name="etSolidRounded">
        <item name="android:textSize">@dimen/_14pxh</item>
        <item name="android:textColor">@color/black_900_7f</item>
        <item name="android:fontFamily">@font/montserrat</item>
        <item name="android:textFontWeight" tools:targetApi="p">300</item>
        <item name="android:background">@drawable/rectangle_bg_gray_400_33_radius_10</item>
    </style>

    <style name="btnSolidRounded">
        <item name="android:textSize">@dimen/_20pxh</item>
        <item name="android:textColor">@color/gray_50_01</item>
        <item name="android:fontFamily">@font/montserrat</item>
        <item name="android:textFontWeight" tools:targetApi="p">600</item>
        <item name="android:background">@drawable/rectangle_bg_blue_gray_800_radius_10</item>
    </style>

    <style name="groupStylecornerRadius_1">
        <item name="android:background">@drawable/rectangle_border_gray_50_radius_12</item>
    </style>

    <style name="txtRobotoregular18">
        <item name="android:textSize">@dimen/_18pxh</item>
        <item name="android:textColor">@color/black_900_01</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="groupStylewhite_1">
        <item name="android:background">@drawable/rectangle_bg_white_a700</item>
    </style>

    <style name="txtRobotoregular16">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/gray_900_03</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="groupStylewhite_3">
        <item name="android:background">@drawable/rectangle_bg_white_a700</item>
    </style>

    <style name="groupStylewhite_2">
        <item name="android:background">
            @drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12
        </item>
    </style>

    <style name="txtRobotoregular14">
        <item name="android:textSize">@dimen/_14pxh</item>
        <item name="android:textColor">@color/gray_900_03</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="etSolidRoundedOutline" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="android:textSize">@dimen/_14pxh</item>
        <item name="android:textColor">@color/blue_gray_900_01</item>

        <item name="android:textFontWeight" tools:targetApi="p">300</item>
        <item name="android:background">
            @drawable/rectangle_bg_white_a700_border_white_a700_radius_8
        </item>
    </style>

    <style name="txtRobotoregular15">
        <item name="android:textSize">@dimen/_15pxh</item>
        <item name="android:textColor">@color/gray_900_03</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtRobotoregular12">
        <item name="android:textSize">@dimen/_12pxh</item>
        <item name="android:textColor">@color/gray_700</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtSolid">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/blue_gray_800_01</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
        <item name="android:background">@drawable/rectangle_bg_gray_50</item>
    </style>

    <style name="etSolid">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/gray_900_03</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
        <item name="android:background">@drawable/rectangle_bg_white_a700</item>
    </style>

    <style name="groupStylegray_2">
        <item name="android:background">
            @drawable/rectangle_bg_gray_50_border_gray_900_33_radius_20
        </item>
    </style>

    <style name="txtIntermedium14">
        <item name="android:textSize">@dimen/_14pxh</item>
        <item name="android:textColor">@color/gray_50_01</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">500</item>
    </style>

    <style name="txtIntermedium16">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/black_900</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">500</item>
    </style>

    <style name="groupStyleblue_800_14cornerRadius">
        <item name="android:background">@drawable/rectangle_bg_blue_800_14_radius_12</item>
    </style>

    <style name="txtRobotomedium16">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/gray_900_03</item>

        <item name="android:textFontWeight" tools:targetApi="p">500</item>
    </style>

    <style name="groupStylegray_50">
        <item name="android:background">@drawable/rectangle_bg_gray_50</item>
    </style>

    <style name="groupStylegray_1">
        <item name="android:background">@drawable/rectangle_bg_gray_50_radius_12</item>
    </style>

    <style name="txtSolidRoundedOutline">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/black_900</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
        <item name="android:background">
            @drawable/rectangle_bg_gray_50_border_gray_900_33_radius_20
        </item>
    </style>

    <style name="txtSolidRounded">
        <item name="android:textSize">@dimen/_28pxh</item>
        <item name="android:textColor">@color/white_A700</item>

        <item name="android:textFontWeight" tools:targetApi="p">500</item>
        <item name="android:background">@drawable/rectangle_bg_blue_gray_800_radius_9</item>
    </style>

    <style name="groupStyleblue_gray_50">
        <item name="android:background">@drawable/rectangle_bg_blue_gray_50</item>
    </style>

    <style name="txtMontserratlight14">
        <item name="android:textSize">@dimen/_14pxh</item>
        <item name="android:textColor">@color/black_900_7f</item>
        <item name="android:fontFamily">@font/montserrat</item>
        <item name="android:textFontWeight" tools:targetApi="p">300</item>
    </style>

    <style name="etSolidRoundedOutline_1">
        <item name="android:textSize">@dimen/_14pxh</item>
        <item name="android:textColor">@color/blue_gray_900_01</item>

        <item name="android:textFontWeight" tools:targetApi="p">300</item>
        <item name="android:background">@drawable/rectangle_bg_gray_50_border_gray_50_radius_8
        </item>
    </style>

    <style name="txtRubikromanmedium24">
        <item name="android:textSize">@dimen/_24pxh</item>
        <item name="android:textColor">@color/blue_gray_800</item>
        <item name="android:fontFamily">@font/rubik</item>
        <item name="android:textFontWeight" tools:targetApi="p">500</item>
    </style>

    <style name="txtRobotoromanregular80">
        <item name="android:textSize">@dimen/_80pxh</item>
        <item name="android:textColor">@color/black_900_01</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="etInterregular16">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/black_900</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtRobotolight14">
        <item name="android:textSize">@dimen/_14pxh</item>
        <item name="android:textColor">@color/blue_gray_900_01</item>

        <item name="android:textFontWeight" tools:targetApi="p">300</item>
    </style>

    <style name="txtInterregular16_1">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/black_900_01</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtArialmt9">
        <item name="android:textSize">@dimen/_9pxh</item>
        <item name="android:textColor">@color/gray_900</item>
        <item name="android:fontFamily">@font/arial</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtRobotoCondensedregular20">
        <item name="android:textSize">@dimen/_20pxh</item>
        <item name="android:textColor">@color/black_900_01</item>
        <item name="android:fontFamily">@font/roboto_condensed</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="groupStylegray_50cornerRadius">
        <item name="android:background">
            @drawable/rectangle_bg_gray_50_border_gray_400_01_radius_12
        </item>
    </style>

    <style name="txtRoundedOutline">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/blue_gray_800_01</item>

        <item name="android:textFontWeight" tools:targetApi="p">400</item>
        <item name="android:background">@drawable/rectangle_border_gray_600_radius_4</item>
    </style>

    <style name="groupStylegray_300">
        <item name="android:background">@drawable/rectangle_bg_gray_300</item>
    </style>

    <style name="groupStylewhite_A700cornerRadius">
        <item name="android:background">@drawable/rectangle_bg_white_a700_radius_12</item>
    </style>

    <style name="txtInterregular16_2">
        <item name="android:textSize">@dimen/_16pxh</item>
        <item name="android:textColor">@color/gray_900_03</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtArialmt9_1">
        <item name="android:textSize">@dimen/_9pxh</item>
        <item name="android:textColor">@color/gray_900_02</item>
        <item name="android:fontFamily">@font/arial</item>
        <item name="android:textFontWeight" tools:targetApi="p">400</item>
    </style>

    <style name="txtIntersemibold18">
        <item name="android:textSize">@dimen/_18pxh</item>
        <item name="android:textColor">@color/gray_900_01</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textFontWeight" tools:targetApi="p">600</item>
    </style>

    <style name="CustomBottomSheetDialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:background">@android:color/transparent</item>
        
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="CustomBottomSheet" parent="Widget.MaterialComponents.BottomSheet">
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="RoundedBottomSheetDialog" parent="@style/ThemeOverlay.MaterialComponents.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/RoundedBottomSheet</item>
    </style>

    <style name="RoundedBottomSheet" parent="Widget.MaterialComponents.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/RoundedBottomSheetShape</item>
    </style>

    <style name="RoundedBottomSheetShape">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">24dp</item>
        <item name="cornerSizeTopLeft">24dp</item>
    </style>

    <style name="DialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
    </style>

    <!-- Estilo para o Spinner -->
    <style name="Widget.App.Spinner" parent="Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu">
        <item name="boxBackgroundColor">@color/spinner_background_selector</item>
        <item name="boxStrokeColor">@color/spinner_stroke_selector</item>
        <item name="android:textColor">@color/black</item>
        <item name="hintTextColor">@color/blue_gray_800</item>
        <item name="endIconTint">@color/blue_gray_800</item>
        <item name="boxStrokeErrorColor">@color/blue_gray_800</item>
        <item name="errorIconTint">@color/blue_gray_800</item>
        <item name="errorTextColor">@color/blue_gray_800</item>
        <item name="helperTextTextColor">@color/blue_gray_800</item>
        <item name="boxBackgroundMode">outline</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="boxStrokeWidthFocused">2dp</item>
    </style>

    <style name="SwitchTheme">
        <item name="colorControlActivated">@color/blue_gray_800</item>
        <item name="colorSwitchThumbNormal">@color/white</item>
        <item name="android:colorForeground">@color/blue_gray_800</item>
    </style>

    <style name="Widget.App.CardView.NoCorners" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">0dp</item>
        <item name="cardElevation">0dp</item>
        <item name="strokeWidth">0dp</item>
    </style>

    <style name="Widget.App.LoadingButton" parent="Widget.Material3.Button">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">16dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/blue_gray_800</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

</resources>