package com.baramaia.hercules.utilities;

import android.content.Context;
import android.content.SharedPreferences;

public class SharedPrefHercules {
    public final String USUARIO_TOKEN = "USUARIO_TOKEN";
    public final String USUARIO_ID = "USUARIO_ID";
    public final String USUARIO_EMAIL = "USUARIO_EMAIL";
    public final String USUARIO_OUVIDORIA = "USUARIO_OUVIDORIA";
    public final String USUARIO_NOME = "USUARIO_NOME";
    Context context;

    public SharedPrefHercules(Context context) {
        this.context = context;
    }
    private void saveSetting(Context context, String key, String value) {
        SharedPreferences mSharedPreferences = context.getSharedPreferences(USUARIO_TOKEN, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        editor.putString(key, value);
        editor.commit();
    }
    private String loadSetting(Context context, String key, String defvalue) {
        SharedPreferences mSharedPreferences = context.getSharedPreferences(USUARIO_TOKEN, Context.MODE_PRIVATE);
        return mSharedPreferences.getString(key, defvalue);
    }
    public void salvarPrefHercules(String key, String value){
        saveSetting(context, key,value);
    }
    public String carregarPrefHercules(String key){
        return loadSetting(context, key, "");
    }

    /**
     * Retorna o token de autenticação do usuário
     * @return Token de autenticação ou string vazia se não existir
     */
    public String getToken() {
        // Obter o token das preferências compartilhadas
        String token = carregarPrefHercules(USUARIO_TOKEN);
        android.util.Log.d("SharedPrefHercules", "Token bruto: " + token);

        // Remover o prefixo "Bearer " se estiver presente
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7); // Remove "Bearer " (7 caracteres)
            android.util.Log.d("SharedPrefHercules", "Token sem prefixo: " + token);
        }

        return token;
    }
}
