package com.baramaia.hercules;

import android.graphics.Bitmap;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

public class FotosAdicionaisAdapter extends RecyclerView.Adapter<FotosAdicionaisAdapter.FotoViewHolder> {
    
    private List<Bitmap> fotos;
    private OnFotoRemovidaListener listener;
    
    public interface OnFotoRemovidaListener {
        void onFotoRemovida(int position);
    }
    
    public FotosAdicionaisAdapter(List<Bitmap> fotos, OnFotoRemovidaListener listener) {
        this.fotos = fotos;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public FotoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_foto_adicional, parent, false);
        return new FotoViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull FotoViewHolder holder, int position) {
        Bitmap foto = fotos.get(position);
        holder.imageViewFoto.setImageBitmap(foto);
        
        // Configura o botão de remover
        holder.btnRemoverFoto.setOnClickListener(v -> {
            if (listener != null) {
                listener.onFotoRemovida(position);
            }
        });
        
        // Mostra o número da foto adicional
        holder.txtLabelFoto.setText("+" + (position + 1));
        holder.txtLabelFoto.setVisibility(View.VISIBLE);
    }
    
    @Override
    public int getItemCount() {
        return fotos.size();
    }
    
    public static class FotoViewHolder extends RecyclerView.ViewHolder {
        ImageView imageViewFoto;
        ImageView btnRemoverFoto;
        TextView txtLabelFoto;
        
        public FotoViewHolder(@NonNull View itemView) {
            super(itemView);
            imageViewFoto = itemView.findViewById(R.id.imageViewFoto);
            btnRemoverFoto = itemView.findViewById(R.id.btnRemoverFoto);
            txtLabelFoto = itemView.findViewById(R.id.txtLabelFoto);
        }
    }
}