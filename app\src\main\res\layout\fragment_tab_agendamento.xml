<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:padding="16dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="16dp"
            android:text="Informações Técnicas"
            android:textSize="16sp"
            android:textStyle="bold" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="CP"
                    android:textStyle="bold" />
                <EditText
                    android:id="@+id/etCp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
                    android:hint="CP"
                    android:inputType="number"
                    android:maxLength="4"
                    android:padding="12dp" />
            </LinearLayout>
            <!-- Removido ET -->
        </LinearLayout>
        <!-- Linha: CS -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                android:layout_weight="1"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="CS"
                    android:textStyle="bold" />
                <EditText
                    android:id="@+id/etCs"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
                    android:hint="CS"
                    android:inputType="number"
                    android:maxLength="3"
                    android:padding="12dp" />
            </LinearLayout>
        </LinearLayout>
        <!-- Removidas Posições -->
        <!-- Quarta linha: Módulo e Display -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Módulo"
                    android:textStyle="bold" />
                <EditText
                    android:id="@+id/etModulo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
                    android:hint="Módulo"
                    android:inputType="number"
                    android:maxLength="8"
                    android:padding="12dp" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Número do Display"
                    android:textStyle="bold" />
                <EditText
                    android:id="@+id/etDisplay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
                    android:hint="Display"
                    android:inputType="number"
                    android:maxLength="7"
                    android:padding="12dp" />
            </LinearLayout>
        </LinearLayout>
        <!-- Empreiteira -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Empreiteira"
            android:textStyle="bold" />
        <Spinner
            android:id="@+id/spinnerEmpreiteira"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
            android:padding="12dp"
            android:popupBackground="@android:color/white"
            android:spinnerMode="dropdown" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">
            <Button
                android:id="@+id/btnAnteriorTab3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/rectangle_bg_white_a700_border_gray_400_01_radius_12"
                android:padding="12dp"
                android:text="Anterior"
                android:textColor="@color/white" />
            <com.github.rygelouv.androidloadingbuttonlib.LoadingButton
                android:id="@+id/btnProximoTab3"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:padding="0dp"
                custom:text="Próximo"
                custom:textColor="@android:color/white"
                custom:textSize="16sp"
                custom:progressColor="@android:color/white"
                custom:background="@drawable/button_blue_gray_rounded" />
        </LinearLayout>
    </LinearLayout>
</ScrollView>