package com.baramaia.hercules;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ViewParent;
import android.widget.ScrollView;

public class CustomScrollView extends ScrollView {
    
    private float startY;
    private boolean isScrolling = false;
    
    public CustomScrollView(Context context) {
        super(context);
        init();
    }
    
    public CustomScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public CustomScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        // Força o ScrollView a ser focável
        setFocusable(true);
        setFocusableInTouchMode(true);
    }
    
    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                startY = ev.getY();
                // Sempre intercepta o primeiro toque
                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_MOVE:
                float deltaY = Math.abs(ev.getY() - startY);
                if (deltaY > 10) { // Threshold para detectar scroll
                    isScrolling = true;
                    getParent().requestDisallowInterceptTouchEvent(true);
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                isScrolling = false;
                break;
        }
        return super.onInterceptTouchEvent(ev);
    }
    
    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        // Sempre impede que o pai intercepte
        ViewParent parent = getParent();
        if (parent != null) {
            parent.requestDisallowInterceptTouchEvent(true);
        }
        
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // Força o foco neste view
                requestFocus();
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                // Permite que o pai volte a interceptar após o toque
                if (parent != null) {
                    parent.requestDisallowInterceptTouchEvent(false);
                }
                break;
        }
        
        return super.onTouchEvent(ev);
    }
    
    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        // Mantém o controle durante o scroll
        ViewParent parent = getParent();
        if (parent != null && isScrolling) {
            parent.requestDisallowInterceptTouchEvent(true);
        }
    }
}