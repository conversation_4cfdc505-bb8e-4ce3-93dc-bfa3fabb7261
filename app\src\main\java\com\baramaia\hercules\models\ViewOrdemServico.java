package com.baramaia.hercules.models;

import java.util.List;

public class ViewOrdemServico {
    int id;
    String ord_ordem;
    int ord_cliId;
    int ord_uc;
    int ord_na;
    String ord_nome;
    String ord_municipio;
    String ord_nucleo;
    String ord_logradouro;
    String ord_numero;
    String ord_complemento;
    String ord_bloco;
    int ord_cp;
    int ord_cs;
    String ord_pos1;
    String ord_pos2;
    String ord_pos3;
    int ord_et;
    int ord_modulo;
    String ord_display;
    String ord_motivo;
    String ord_empreiteira;
    String ord_status_uc;
    String ord_situacao;
    String ord_gps;
    String ord_dataEntrada;
    String ord_dataFechamento;
    int ord_status;
    String ord_obs;
    int totalOrdens;
    String motivo;
    String ord_tipoServ;
    String tecnicos;
    List<Integer> userId;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
    public int getOrd_cliId() {
        return ord_cliId;
    }

    public void setOrd_cliId(int ord_cliId) {
        this.ord_cliId = ord_cliId;
    }

    public int getOrd_uc() {
        return ord_uc;
    }

    public void setOrd_uc(int ord_uc) {
        this.ord_uc = ord_uc;
    }
    public String getOrd_ordem() {
        return ord_ordem;
    }

    public void setOrd_ordem(String ord_ordem) {
        this.ord_ordem = ord_ordem;
    }
    public String getOrd_nome() {
        return ord_nome;
    }

    public void setOrd_nome(String ord_nome) {
        this.ord_nome = ord_nome;
    }

    public String getOrd_municipio() {
        return ord_municipio;
    }

    public void setOrd_municipio(String ord_municipio) {
        this.ord_municipio = ord_municipio;
    }

    public String getOrd_nucleo() {
        return ord_nucleo;
    }

    public void setOrd_nucleo(String ord_nucleo) {
        this.ord_nucleo = ord_nucleo;
    }

    public String getOrd_logradouro() {
        return ord_logradouro;
    }

    public void setOrd_logradouro(String ord_logradouro) {
        this.ord_logradouro = ord_logradouro;
    }

    public String getOrd_numero() {
        return ord_numero;
    }

    public void setOrd_numero(String ord_numero) {
        this.ord_numero = ord_numero;
    }

    public String getOrd_complemento() {
        return ord_complemento;
    }

    public void setOrd_complemento(String ord_complemento) {
        this.ord_complemento = ord_complemento;
    }

    public String getOrd_bloco() {
        return ord_bloco;
    }

    public void setOrd_bloco(String ord_bloco) {
        this.ord_bloco = ord_bloco;
    }

    public int getOrd_cp() {
        return ord_cp;
    }

    public void setOrd_cp(int ord_cp) {
        this.ord_cp = ord_cp;
    }

    public int getOrd_cs() {
        return ord_cs;
    }

    public void setOrd_cs(int ord_cs) {
        this.ord_cs = ord_cs;
    }

    public String getOrd_pos1() {
        return ord_pos1;
    }

    public void setOrd_pos1(String ord_pos1) {
        this.ord_pos1 = ord_pos1;
    }

    public String getOrd_pos2() {
        return ord_pos2;
    }

    public void setOrd_pos2(String ord_pos2) {
        this.ord_pos2 = ord_pos2;
    }

    public String getOrd_pos3() {
        return ord_pos3;
    }

    public void setOrd_pos3(String ord_pos3) {
        this.ord_pos3 = ord_pos3;
    }

    public int getOrd_et() {
        return ord_et;
    }

    public void setOrd_et(int ord_et) {
        this.ord_et = ord_et;
    }

    public int getOrd_modulo() {
        return ord_modulo;
    }

    public void setOrd_modulo(int ord_modulo) {
        this.ord_modulo = ord_modulo;
    }

    public String getOrd_display() {
        return ord_display;
    }

    public void setOrd_display(String ord_display) {
        this.ord_display = ord_display;
    }

    public String getOrd_motivo() {
        return ord_motivo;
    }

    public void setOrd_motivo(String ord_motivo) {
        this.ord_motivo = ord_motivo;
    }

    public String getOrd_empreiteira() {
        return ord_empreiteira;
    }

    public void setOrd_empreiteira(String ord_empreiteira) {
        this.ord_empreiteira = ord_empreiteira;
    }

    public String getOrd_status_uc() {
        return ord_status_uc;
    }

    public void setOrd_status_uc(String ord_status_uc) {
        this.ord_status_uc = ord_status_uc;
    }

    public String getOrd_situacao() {
        return ord_situacao;
    }

    public void setOrd_situacao(String ord_situacao) {
        this.ord_situacao = ord_situacao;
    }

    public String getOrd_gps() {
        return ord_gps;
    }

    public void setOrd_gps(String ord_gps) {
        this.ord_gps = ord_gps;
    }

    public String getOrd_dataEntrada() {
        return ord_dataEntrada;
    }

    public void setOrd_dataEntrada(String ord_dataEntrada) {
        this.ord_dataEntrada = ord_dataEntrada;
    }

    public String getOrd_dataFechamento() {
        return ord_dataFechamento;
    }

    public void setOrd_dataFechamento(String ord_dataFechamento) {
        this.ord_dataFechamento = ord_dataFechamento;
    }

    public int isOrd_status() {
        return ord_status;
    }

    public void setOrd_status(int ord_status) {
        this.ord_status = ord_status;
    }

    public String getOrd_obs() {
        return ord_obs;
    }

    public void setOrd_obs(String ord_obs) {
        this.ord_obs = ord_obs;
    }

    public int getTotalOrdens() {
        return totalOrdens;
    }

    public void setTotalOrdens(int totalOrdens) {
        this.totalOrdens = totalOrdens;
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public String getOrd_tipoServ() {
        return ord_tipoServ;
    }

    public void setOrd_tipoServ(String ord_tipoServ) {
        this.ord_tipoServ = ord_tipoServ;
    }

    public String getTecnicos() {
        return tecnicos;
    }

    public void setTecnicos(String tecnicos) {
        this.tecnicos = tecnicos;
    }

    public List<Integer> getUserId() {
        return userId;
    }

    public void setUserId(List<Integer> userId) {
        this.userId = userId;
    }
    public ViewOrdemServico(){

    }
    public ViewOrdemServico(String ord_ordem){
        this.ord_ordem = ord_ordem;
    }
}
