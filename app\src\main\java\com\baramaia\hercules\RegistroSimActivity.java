package com.baramaia.hercules;

import android.Manifest;
import android.app.Dialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.drawable.ColorDrawable;
import android.media.ExifInterface;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.provider.MediaStore;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;

import com.baramaia.hercules.DAO.EquipamentoVisitaDao;
import com.baramaia.hercules.Data.AppDatabase;
import com.baramaia.hercules.models.Equipamento;
import com.baramaia.hercules.models.EquipamentoVisita;
import com.baramaia.hercules.models.Foto;
import com.baramaia.hercules.models.Ouvidoria;
import com.baramaia.hercules.network.ApiCalls;
import com.baramaia.hercules.network.ApiDados;
import com.baramaia.hercules.utilities.EquipamentosTemporarios;
import com.baramaia.hercules.utilities.SharedPrefHercules;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.github.leandroborgesferreira.loadingbutton.customViews.CircularProgressButton;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.materialswitch.MaterialSwitch;
import com.google.android.material.textfield.MaterialAutoCompleteTextView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class RegistroSimActivity extends AppCompatActivity {
    ApiDados apiDados = new ApiDados();
    private ApiCalls apiCalls;

    private MaterialButton btnProxima;
    private MaterialButton btnAdicionarEquipamento;
    private MaterialButton btnAdicionarEquipamentoInstalado;
    private LinearLayout linearRowarrowleft;
    private LinearLayout layEquipRetirado;
    private LinearLayout layEquipInstalado;
    private MaterialSwitch switchRetirado;
    private MaterialSwitch switchInstalado;
    private TextView txtRetiradoStatus;
    private TextView txtInstaladoStatus;
    private List<Equipamento> equipamentosList = new ArrayList<>();
    private List<EquipamentoVisita> equipamentosRetirados = new ArrayList<>();
    private List<EquipamentoVisita> equipamentosInstalados = new ArrayList<>();
    private boolean isFromInstalado = false;
    private boolean isPhotoTaken = false;

    private int visitaId;
    private int ordId;
    private int avulsoId;
    private int et;
    Date dataagora = new Date();

    private static final int REQUEST_IMAGE_CAPTURE = 1;
    private static final int REQUEST_PICK_IMAGE = 2;
    private static final int PERMISSION_REQUEST_CODE = 100;
    private Uri photoUri;
    private File photoFile;

    private int currentPhotoIndex = -1;
    private String currentPhotoPath;
    List<String> equipamentos = new ArrayList<>();

    private List<Bitmap> thumbnailsInstalados = new ArrayList<>();
    private List<Bitmap> thumbnailsRetirados = new ArrayList<>();
    private List<Bitmap> fullImagesInstalados = new ArrayList<>();
    private List<Bitmap> fullImagesRetirados = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_registro_assinalado_sim);

        // Configurar a StatusBar com texto e ícones brancos
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.custom_border_color));
        getWindow().getDecorView().setSystemUiVisibility(0); // Remover a flag View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR

        // Inicializar as listas de miniaturas e imagens completas
        for (int i = 0; i < 10; i++) {
            thumbnailsInstalados.add(null);
            thumbnailsRetirados.add(null);
            fullImagesInstalados.add(null);
            fullImagesRetirados.add(null);
        }

        AppDatabase db = AppDatabase.getDatabase(getApplicationContext());
        EquipamentoVisitaDao dao = db.equipamentoVisitaDao();

        new Thread(() -> {
            List<EquipamentoVisita> equipamentos = dao.getAll();
            runOnUiThread(() -> {
                // Atualize a interface com os equipamentos recuperados
                atualizarListaEquipamentos(equipamentos);
            });
        }).start();

        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);
        Intent intent = getIntent();
        visitaId = intent.getIntExtra("visita_id", -1);
        ordId = intent.getIntExtra("ordemId", 0);
        avulsoId = intent.getIntExtra("avulsoId", 0);
        et = intent.getIntExtra("et", -1);

        // Inicialização dos componentes
        inicializarComponentes();
        configurarListeners();
        getEquipamentosDisponiveis();

    }

    private void inicializarComponentes() {
        btnProxima = findViewById(R.id.btnProxima);
        btnAdicionarEquipamento = findViewById(R.id.btnAdicionarEquipamento);
        btnAdicionarEquipamentoInstalado = findViewById(R.id.btnAdicionarEquipamentoInstalado);
        linearRowarrowleft = findViewById(R.id.linearRowarrowleft);
        layEquipRetirado = findViewById(R.id.layEquipRetirado);
        layEquipInstalado = findViewById(R.id.layEquipInstalado);
        switchRetirado = findViewById(R.id.switchRetirado);
        switchInstalado = findViewById(R.id.switchInstalado);
        txtRetiradoStatus = findViewById(R.id.txtRetiradoStatus);
        txtInstaladoStatus = findViewById(R.id.txtInstaladoStatus);

        // Configurar estado inicial
        txtRetiradoStatus.setText("Não");
        txtInstaladoStatus.setText("Não");
        btnAdicionarEquipamento.setVisibility(View.GONE);
        btnAdicionarEquipamentoInstalado.setVisibility(View.GONE);
    }

    @Override
    public void onBackPressed() {
        // Não fazer nada para desabilitar o botão de voltar
        // Ou mostrar uma mensagem
        Toast.makeText(this, "Botão Voltar desabilitado. Favor conclua o procedimento!", Toast.LENGTH_SHORT).show();
    }

    private String pegaDataHora(Date dt) {
        String dataHora;

        //===============PREECHE DATA INICIAL==============================
        SimpleDateFormat formataData = new SimpleDateFormat("dd/MM/yyyy");
        Date data = new Date();
        if (dt != null) {
            data = dt;
        }
        String dataFormatada = formataData.format(data);
        //===============PREECHE DATA INICIAL==============================

        //===============PREECHE HORA INICIAL==============================
        String sHora;
        String sMinuto;
        if (data.getMinutes() < 10)
            sMinuto = "0" + String.valueOf(data.getMinutes());
        else
            sMinuto = String.valueOf(data.getMinutes());

        if (data.getHours() < 10)
            sHora = "0" + String.valueOf(data.getHours());
        else
            sHora = String.valueOf(data.getHours());
        String sHour = sHora + ":" + sMinuto;
        //===============PREECHE HORA INICIAL==============================

        dataHora = dataFormatada + " " + sHour;

        //================================================
        String dd = dataHora.substring(0, 2);
        String MM = dataHora.substring(3, 5);
        String aaaa = dataHora.substring(6, 10);
        String HH = dataHora.substring(11, 13);
        String mm = dataHora.substring(14, 16);
        dataHora = aaaa + "-" + MM + "-" + dd + "T" + HH + ":" + mm + ":00";
        //================================================

        return dataHora;
    }

    // Métodos para controle de loading do botão
    private void startButtonLoading() {
        btnProxima.setEnabled(false);
        btnProxima.setText("Finalizando...");
    }

    private void resetButtonState() {
        btnProxima.setEnabled(true);
        btnProxima.setText(getString(R.string.msg_finalizar_registro));
    }

    private void configurarListeners() {
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(RegistroSimActivity.this);
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);
        String userId = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_ID);
        // Listener para o switch Retirado
        switchRetirado.setOnCheckedChangeListener((buttonView, isChecked) -> {
            txtRetiradoStatus.setText(isChecked ? "Sim" : "Não");
            layEquipRetirado.setVisibility(isChecked ? View.VISIBLE : View.GONE);
            btnAdicionarEquipamento.setVisibility(isChecked ? View.VISIBLE : View.GONE);
            layEquipRetirado.removeAllViews();

            if (isChecked) {
                adicionarNovoEquipRetirado();
            }
        });

        // Listener para o switch Instalado
        switchInstalado.setOnCheckedChangeListener((buttonView, isChecked) -> {
            txtInstaladoStatus.setText(isChecked ? "Sim" : "Não");
            layEquipInstalado.setVisibility(isChecked ? View.VISIBLE : View.GONE);
            btnAdicionarEquipamentoInstalado.setVisibility(isChecked ? View.VISIBLE : View.GONE);
            layEquipInstalado.removeAllViews();
            if (isChecked) {
                adicionarNovoEquipInstalado();
            }
        });

        //linearRowarrowleft.setOnClickListener(view -> showConfirmDialog());

        // Listener para o botão próxima
        btnProxima.setOnClickListener(view -> {
            // Inicia o loading state
            startButtonLoading();

            if (equipamentosRetirados.isEmpty() && equipamentosInstalados.isEmpty()) {
                // Aguarda 1 segundo antes de prosseguir
                new Handler().postDelayed(() -> {
                    resetButtonState();
                    buscaOuvidorias(token,userId);
                    enviarTela();
                }, 1000);
            } else {
                // Executa a validação e envio em uma thread separada
                new Thread(() -> {
                    runOnUiThread(() -> {
                        enviarDadosParaProximaActivity();
                        // Aguarda 1 segundo antes de parar o spinner
                        new Handler().postDelayed(() -> {
                            resetButtonState();
                        }, 1000);
                    });
                }).start();
            }
        });
//        btnProxima.setOnClickListener(view -> {
//            if (equipamentosRetirados.isEmpty() && equipamentosInstalados.isEmpty()){
//                enviarTela();
//            }
//            enviarDadosParaProximaActivity();
//        });
        // Listener para o botão de adicionar equipamento retirado (simplificado)
        btnAdicionarEquipamento.setOnClickListener(view -> {
            adicionarNovoEquipRetirado();
        });

        // Listener para o botão de adicionar equipamento instalado (simplificado)
        btnAdicionarEquipamentoInstalado.setOnClickListener(view -> {
            adicionarNovoEquipInstalado();
        });
    }

    private void enviarDadosParaProximaActivity() {
        List<EquipamentoVisita> eqpRet = new ArrayList<>();
        List<EquipamentoVisita> eqpInst = new ArrayList<>();
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(RegistroSimActivity.this);
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);
        String userId = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_ID);

        // Validação para equipamentos retirados
        if (switchRetirado.isChecked() && txtRetiradoStatus.getText().toString().equals("Sim")) {
            if (layEquipRetirado.getChildCount() == 0) {
                Toast.makeText(this, "Adicione pelo menos um equipamento retirado", Toast.LENGTH_SHORT).show();
                return;
            }

            // Valida cada bloco de equipamento retirado
            for (int i = 0; i < layEquipRetirado.getChildCount(); i++) {
                View bloco = layEquipRetirado.getChildAt(i);
                MaterialAutoCompleteTextView spinner = bloco.findViewById(R.id.spinnerOptions);
                String equipamentoSelecionado = spinner.getText().toString();

                // Primeira validação: equipamento selecionado
                if (equipamentoSelecionado.equals("")) {
                    Toast.makeText(this, "Selecione um equipamento retirado", Toast.LENGTH_SHORT).show();
                    return;
                }

                TextInputEditText edtNumeroMedidor = bloco.findViewById(R.id.edtNumeroMedidorRetirado);
                TextInputEditText edtNumeroDisplay = bloco.findViewById(R.id.edtNumeroDisplayRetirado);
                String numeroMedidor = edtNumeroMedidor.getText().toString().trim();
                String numeroDisplay = edtNumeroDisplay.getText().toString().trim();

                // Segunda validação: número EDP
                if (numeroMedidor.isEmpty()) {
                    Toast.makeText(this, "Preencha o número EDP do equipamento retirado", Toast.LENGTH_SHORT).show();
                    return;
                }

                // Terceira validação: número display (se necessário)
                if (!isEquipamentoEspecial(equipamentoSelecionado) && numeroDisplay.isEmpty()) {
                    Toast.makeText(this, "Preencha o número do display do equipamento retirado", Toast.LENGTH_SHORT).show();
                    return;
                }

                // Quarta validação: foto
                EquipamentoVisita equipExistente = null;
                for (EquipamentoVisita eqp : equipamentosRetirados) {
                    if (eqp.getEqv_descricao().equals(equipamentoSelecionado) &&
                            eqp.getEqv_nmedidor().equals(numeroMedidor) &&
                            eqp.getEqv_ndisplay().equals(numeroDisplay)) {
                        equipExistente = eqp;
                        break;
                    }
                }

                if (equipExistente == null || equipExistente.getFoto() == null) {
                    Toast.makeText(this, "Tire a foto do equipamento retirado", Toast.LENGTH_SHORT).show();
                    return;
                }
            }

            // Processa equipamentos retirados
            for (int i = 0; i < layEquipRetirado.getChildCount(); i++) {
                View bloco = layEquipRetirado.getChildAt(i);
                MaterialAutoCompleteTextView spinner = bloco.findViewById(R.id.spinnerOptions);
                TextInputEditText edtNumeroMedidor = bloco.findViewById(R.id.edtNumeroMedidorRetirado);
                TextInputEditText edtNumeroDisplay = bloco.findViewById(R.id.edtNumeroDisplayRetirado);

                String equipamentoSelecionado = spinner.getText().toString();
                String numeroMedidor = edtNumeroMedidor.getText().toString().trim();
                String numeroDisplay = edtNumeroDisplay.getText().toString().trim();

                // Verifica se já existe um equipamento com os mesmos dados
                EquipamentoVisita equipamento = null;
                for (EquipamentoVisita eqp : equipamentosRetirados) {
                    if (eqp.getEqv_descricao().equals(equipamentoSelecionado) &&
                            eqp.getEqv_nmedidor().equals(numeroMedidor) &&
                            eqp.getEqv_ndisplay().equals(numeroDisplay)) {
                        equipamento = eqp;
                        break;
                    }
                }

                if (equipamento == null) {
                    equipamento = new EquipamentoVisita();
                    equipamento.setEqv_descricao(equipamentoSelecionado);
                    equipamento.setEqv_nmedidor(numeroMedidor);
                    equipamento.setEqv_nserie(numeroMedidor);
                    equipamento.setEqv_ndisplay(numeroDisplay);
                    equipamento.setVisitaId(visitaId);
                    equipamento.setInstalado(false);
                    equipamentosRetirados.add(equipamento);
                }
                eqpRet.add(equipamento);
            }
        }

        // Validação para equipamentos instalados
        if (switchInstalado.isChecked() && txtInstaladoStatus.getText().toString().equals("Sim")) {
            if (layEquipInstalado.getChildCount() == 0) {
                Toast.makeText(this, "Adicione pelo menos um equipamento instalado", Toast.LENGTH_SHORT).show();
                return;
            }

            // Valida cada bloco de equipamento instalado
            for (int i = 0; i < layEquipInstalado.getChildCount(); i++) {
                View bloco = layEquipInstalado.getChildAt(i);
                MaterialAutoCompleteTextView spinner = bloco.findViewById(R.id.spinnerOpcoesInst);
                TextInputEditText edtNumeroMedidor = bloco.findViewById(R.id.edtNumeroMedidorInstalado);
                TextInputEditText edtNumeroDisplay = bloco.findViewById(R.id.edtNumeroDisplayInstalado);

                String equipamentoSelecionado = spinner.getText().toString();
                String numeroMedidor = edtNumeroMedidor.getText().toString().trim();
                String numeroDisplay = edtNumeroDisplay.getText().toString().trim();

                // Validações individuais
                if (equipamentoSelecionado.equals("")) {
                    Toast.makeText(this, "Selecione um equipamento instalado", Toast.LENGTH_SHORT).show();
                    return;
                }

                if (numeroMedidor.isEmpty()) {
                    Toast.makeText(this, "Preencha o número EDP do equipamento instalado", Toast.LENGTH_SHORT).show();
                    return;
                }

                // Verifica display apenas para equipamentos que não são do tipo especial
                if (!isEquipamentoEspecial(equipamentoSelecionado) && numeroDisplay.isEmpty()) {
                    Toast.makeText(this, "Preencha o número do display do equipamento instalado", Toast.LENGTH_SHORT).show();
                    return;
                }

                // Verifica se tem foto
                EquipamentoVisita equipExistente = null;
                for (EquipamentoVisita eqp : equipamentosInstalados) {
                    if (eqp.getEqv_descricao().equals(equipamentoSelecionado) &&
                            eqp.getEqv_nmedidor().equals(numeroMedidor) &&
                            eqp.getEqv_ndisplay().equals(numeroDisplay)) {
                        equipExistente = eqp;
                        break;
                    }
                }

                if (equipExistente == null || equipExistente.getFoto() == null) {
                    Toast.makeText(this, "Tire a foto do equipamento instalado", Toast.LENGTH_SHORT).show();
                    return;
                }
            }

            // Processa equipamentos instalados
            for (int i = 0; i < layEquipInstalado.getChildCount(); i++) {
                View bloco = layEquipInstalado.getChildAt(i);
                MaterialAutoCompleteTextView spinner = bloco.findViewById(R.id.spinnerOpcoesInst);
                TextInputEditText edtNumeroMedidor = bloco.findViewById(R.id.edtNumeroMedidorInstalado);
                TextInputEditText edtNumeroDisplay = bloco.findViewById(R.id.edtNumeroDisplayInstalado);

                String equipamentoSelecionado = spinner.getText().toString();
                String numeroMedidor = edtNumeroMedidor.getText().toString().trim();
                String numeroDisplay = edtNumeroDisplay.getText().toString().trim();

                // Verifica se já existe um equipamento com os mesmos dados
                EquipamentoVisita equipamento = null;
                for (EquipamentoVisita eqp : equipamentosInstalados) {
                    if (eqp.getEqv_descricao().equals(equipamentoSelecionado) &&
                            eqp.getEqv_nmedidor().equals(numeroMedidor) &&
                            eqp.getEqv_ndisplay().equals(numeroDisplay)) {
                        equipamento = eqp;
                        break;
                    }
                }

                if (equipamento == null) {
                    equipamento = new EquipamentoVisita();
                    equipamento.setEqv_descricao(equipamentoSelecionado);
                    equipamento.setEqv_nmedidor(numeroMedidor);
                    equipamento.setEqv_nserie(numeroMedidor);
                    equipamento.setEqv_ndisplay(numeroDisplay);
                    equipamento.setVisitaId(visitaId);
                    equipamento.setInstalado(true);
                    equipamentosInstalados.add(equipamento);
                }
                eqpInst.add(equipamento);
            }
        }

        // Em vez de converter para JSON, armazene no singleton
        EquipamentosTemporarios.getInstance().setEquipamentosRetirados(eqpRet);
        EquipamentosTemporarios.getInstance().setEquipamentosInstalados(eqpInst);

        if (!eqpInst.isEmpty()) {
            for (EquipamentoVisita e : eqpInst
            ) {
                e.setInstalado(true);
            }
            enviarEquipamento(eqpInst, 1);
        }
        if (!eqpRet.isEmpty()) {
            enviarEquipamento(eqpRet, 0);
        }
        buscaOuvidorias(token,userId);
        enviarTela();
    }
    private void buscaOuvidorias(String token, String userId) {
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(getApplicationContext());
        int userid = Integer.parseInt(userId);

        Call<List<Ouvidoria>> callouvid = apiCalls.getOuvidorias(userid, token);
        callouvid.enqueue(new Callback<List<Ouvidoria>>() {
            @Override
            public void onResponse(Call<List<Ouvidoria>> callouvid, Response<List<Ouvidoria>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA, "1");
                } else {
                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA, "0");
                }
            }
            @Override
            public void onFailure(Call<List<Ouvidoria>> call, Throwable t) {
                Toast.makeText(RegistroSimActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
    }
    private void enviarTela() {
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(RegistroSimActivity.this);
        String exOuvidoria = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA);
        if (exOuvidoria.equals("1")) {
            Intent intentVisita = new Intent(getApplicationContext(), IniciarVisitaActivity.class);
            intentVisita.putExtra("ordemId", ordId);
            intentVisita.putExtra("et", et);

            Toast.makeText(RegistroSimActivity.this, "Visita salva com sucesso!", Toast.LENGTH_SHORT).show();
            startActivity(intentVisita);

        } else {
            Intent intentVisita = new Intent(getApplicationContext(), IniciarVisitaOrdemVerticalActivity.class);
            intentVisita.putExtra("ordemId", ordId);
            intentVisita.putExtra("et", et);
            Toast.makeText(RegistroSimActivity.this, "Visita salva com sucesso!", Toast.LENGTH_SHORT).show();
            startActivity(intentVisita);
        }
    }

    private void enviarEquipamento(List<EquipamentoVisita> equipamentos, int inst) {
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(RegistroSimActivity.this);
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);

        Call<List<EquipamentoVisita>> call = apiCalls.postEquipamentosVisita(equipamentos, token);
        call.enqueue(new Callback<List<EquipamentoVisita>>() {
            @Override
            public void onResponse(Call<List<EquipamentoVisita>> call, Response<List<EquipamentoVisita>> response) {
                if (response.isSuccessful() && response.body() != null) {

                    if (inst == 1) {
                        Toast.makeText(RegistroSimActivity.this, "Equipamentos Instalados Registrados!", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(RegistroSimActivity.this, "Equipamentos Retirados Registrados!", Toast.LENGTH_SHORT).show();
                    }

                } else {
                    Toast.makeText(RegistroSimActivity.this, "Não há lista de equipamentos", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<EquipamentoVisita>> call, Throwable t) {

                Toast.makeText(RegistroSimActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void buscarEquipamentos(MaterialAutoCompleteTextView spinner, Runnable onComplete) {
        // Mostrar skeleton
        View skeletonView = getLayoutInflater().inflate(R.layout.skeleton_equipamento, null);
        ShimmerFrameLayout shimmerFrameLayout = (ShimmerFrameLayout) skeletonView;
        shimmerFrameLayout.startShimmer();

        // Adicionar skeleton temporariamente
        ViewGroup parent = (ViewGroup) spinner.getParent();
        int spinnerIndex = parent.indexOfChild(spinner);
        parent.addView(skeletonView, spinnerIndex);
        spinner.setVisibility(View.GONE);

        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(RegistroSimActivity.this);
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);

        Call<List<Equipamento>> call = apiCalls.getEquipamentos(token);
        call.enqueue(new Callback<List<Equipamento>>() {
            @Override
            public void onResponse(Call<List<Equipamento>> call, Response<List<Equipamento>> response) {
                // Remover skeleton
                shimmerFrameLayout.stopShimmer();
                parent.removeView(skeletonView);
                spinner.setVisibility(View.VISIBLE);

                if (response.isSuccessful() && response.body() != null) {
                    equipamentosList = response.body();
                    ArrayAdapter<Equipamento> adapter = new ArrayAdapter<>(
                            RegistroSimActivity.this,
                            R.layout.item_spinner,
                            equipamentosList
                    );
                    adapter.setDropDownViewResource(R.layout.item_spinner_dropdown);
                    spinner.setAdapter(adapter);
                    onComplete.run();
                } else {
                    Toast.makeText(RegistroSimActivity.this, "Não há lista de equipamentos", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<Equipamento>> call, Throwable t) {
                // Remover skeleton em caso de erro
                shimmerFrameLayout.stopShimmer();
                parent.removeView(skeletonView);
                spinner.setVisibility(View.VISIBLE);

                Toast.makeText(RegistroSimActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void adicionarNovoEquipRetirado() {
        View blocoEquipamento = getLayoutInflater().inflate(R.layout.item_equipamento_retirado, null);

        MaterialAutoCompleteTextView spinner = blocoEquipamento.findViewById(R.id.spinnerOptions);
        TextInputEditText edtNumeroMedidor = blocoEquipamento.findViewById(R.id.edtNumeroMedidorRetirado);
        TextInputEditText edtNumeroDisplay = blocoEquipamento.findViewById(R.id.edtNumeroDisplayRetirado);
        TextInputLayout tilNumeroDisplay = blocoEquipamento.findViewById(R.id.tilNumeroDisplayRetirado);

        MaterialButton btnCamera = blocoEquipamento.findViewById(R.id.btnCamera);
        ImageButton btnRemover = blocoEquipamento.findViewById(R.id.btnRemover);

        // Configurar o adapter do spinner
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_dropdown_item_1line,
                equipamentos);
        spinner.setAdapter(adapter);

        // Listener para quando um item é selecionado
        spinner.setOnItemClickListener((parent, view, position, id) -> {
            String selectedEquip = parent.getItemAtPosition(position).toString();

            // Atualiza a lista de equipamentosRetirados
            if (layEquipRetirado.indexOfChild(blocoEquipamento) < equipamentosRetirados.size()) {
                EquipamentoVisita equipamento = equipamentosRetirados.get(layEquipRetirado.indexOfChild(blocoEquipamento));
                equipamento.setEqv_descricao(selectedEquip);
            }

            // Limpa os campos
            edtNumeroMedidor.setText("");
            edtNumeroDisplay.setText("");


            // Gerencia a visibilidade do campo Número Display
            switch (selectedEquip.toLowerCase()) {
                case "medidor convencional":
                case "caixa cp / cs":
                case "rádio cs":
                case "cpu cs":
                    tilNumeroDisplay.setVisibility(View.GONE);
                    edtNumeroDisplay.setText("");  // Limpa o campo quando oculto
                    break;
                default:
                    tilNumeroDisplay.setVisibility(View.VISIBLE);
                    break;
            }
        });

        // Configurar botão de câmera
        btnCamera.setOnClickListener(v -> {
            currentPhotoIndex = layEquipRetirado.indexOfChild(blocoEquipamento);
            isFromInstalado = false;
            showImagePickerDialog(blocoEquipamento);
        });

        // Configurar botão de remover
        btnRemover.setOnClickListener(v -> {
            layEquipRetirado.removeView(blocoEquipamento);
            if (layEquipRetirado.getChildCount() == 0) {
                switchRetirado.setChecked(false);
            }
        });

        layEquipRetirado.addView(blocoEquipamento);
    }

    private List<String> getEquipamentosDisponiveis() {

        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(RegistroSimActivity.this);
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);

        Call<List<Equipamento>> call = apiCalls.getEquipamentos(token);
        call.enqueue(new Callback<List<Equipamento>>() {
            @Override
            public void onResponse(Call<List<Equipamento>> call, Response<List<Equipamento>> response) {

                if (response.isSuccessful() && response.body() != null) {
                    equipamentosList = response.body();
                    for (Equipamento equip : equipamentosList) {
                        if (equip.getEqp_descricao() != null) {
                            equipamentos.add(equip.getEqp_descricao());
                        }
                    }
                } else {
                    Toast.makeText(RegistroSimActivity.this, "Não há lista de equipamentos", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<Equipamento>> call, Throwable t) {
                Toast.makeText(RegistroSimActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
        return equipamentos;
    }

    private void adicionarNovoEquipInstalado() {
        View blocoEquipamento = getLayoutInflater().inflate(R.layout.item_equipamento_instalado, null);

        MaterialAutoCompleteTextView spinner = blocoEquipamento.findViewById(R.id.spinnerOpcoesInst);
        TextInputEditText edtNumeroMedidor = blocoEquipamento.findViewById(R.id.edtNumeroMedidorInstalado);
        TextInputEditText edtNumeroDisplay = blocoEquipamento.findViewById(R.id.edtNumeroDisplayInstalado);
        TextInputLayout tilNumeroDisplay = blocoEquipamento.findViewById(R.id.tilNumeroDisplayInstalado);

        MaterialButton btnCamera = blocoEquipamento.findViewById(R.id.btnCamera);
        ImageButton btnRemover = blocoEquipamento.findViewById(R.id.btnRemover);

        // Configurar o adapter do spinner
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_dropdown_item_1line,
                equipamentos);
        spinner.setAdapter(adapter);

        // Listener para quando um item é selecionado
        spinner.setOnItemClickListener((parent, view, position, id) -> {
            String selectedEquip = parent.getItemAtPosition(position).toString();

            // Atualiza a lista de equipamentosInstalados
            if (layEquipInstalado.indexOfChild(blocoEquipamento) < equipamentosInstalados.size()) {
                EquipamentoVisita equipamento = equipamentosInstalados.get(layEquipInstalado.indexOfChild(blocoEquipamento));
                equipamento.setEqv_descricao(selectedEquip);
            }

            // Limpa os campos
            edtNumeroMedidor.setText("");
            edtNumeroDisplay.setText("");


            // Gerencia a visibilidade do campo Número Display
            switch (selectedEquip.toLowerCase()) {
                case "medidor convencional":
                case "caixa cp / cs":
                case "rádio cs":
                case "cpu cs":
                    tilNumeroDisplay.setVisibility(View.GONE);
                    edtNumeroDisplay.setText("");  // Limpa o campo quando oculto
                    break;
                default:
                    tilNumeroDisplay.setVisibility(View.VISIBLE);
                    break;
            }
        });

        // Configurar botão de câmera
        btnCamera.setOnClickListener(v -> {
            currentPhotoIndex = layEquipInstalado.indexOfChild(blocoEquipamento);
            isFromInstalado = true;
            showImagePickerDialog(blocoEquipamento);
        });

        // Configurar botão de remover
        btnRemover.setOnClickListener(v -> {
            layEquipInstalado.removeView(blocoEquipamento);
            if (layEquipInstalado.getChildCount() == 0) {
                switchInstalado.setChecked(false);
            }
        });

        layEquipInstalado.addView(blocoEquipamento);
    }

    private void showImagePickerDialog(View blocoEquipamento) {
        BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(this);
        View dialogView = getLayoutInflater().inflate(R.layout.dialog_escolha_foto, null);

        MaterialButton btnCamera = dialogView.findViewById(R.id.btnCamera);
        MaterialButton btnGaleria = dialogView.findViewById(R.id.btnGaleria);

        btnCamera.setOnClickListener(v -> {
            if (checkCameraPermission()) {
                dispatchTakePictureIntent();
            } else {
                requestCameraPermission();
            }
            bottomSheetDialog.dismiss();
        });

        btnGaleria.setOnClickListener(v -> {
            if (checkGalleryPermission()) {
                openGallery();
            } else {
                requestGalleryPermission();
            }
            bottomSheetDialog.dismiss();
        });

        bottomSheetDialog.setContentView(dialogView);
        bottomSheetDialog.show();
    }

    private boolean checkCameraPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED;
    }

    private boolean checkGalleryPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES)
                    == PackageManager.PERMISSION_GRANTED;
        } else {
            return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                    == PackageManager.PERMISSION_GRANTED;
        }
    }

    private void requestCameraPermission() {
        ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.CAMERA},
                PERMISSION_REQUEST_CODE);
    }

    private void requestGalleryPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.READ_MEDIA_IMAGES},
                    PERMISSION_REQUEST_CODE);
        } else {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
                    PERMISSION_REQUEST_CODE);
        }
    }

    private void openGallery() {
        Intent intent = new Intent(Intent.ACTION_PICK);
        intent.setType("image/*");
        startActivityForResult(intent, REQUEST_PICK_IMAGE);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                if (permissions[0].equals(Manifest.permission.CAMERA)) {
                    dispatchTakePictureIntent();
                } else {
                    openGallery();
                }
            } else {
                Toast.makeText(this, "Permissão negada", Toast.LENGTH_SHORT).show();
            }
        }
    }

//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//
//        if (resultCode == RESULT_OK) {
//            if (requestCode == REQUEST_IMAGE_CAPTURE) {
//                if (photoFile != null && currentPhotoIndex >= 0) {
//                    currentPhotoPath = photoFile.getAbsolutePath();
//                    processarFoto(currentPhotoIndex, isFromInstalado);
//                }
//            } else if (requestCode == REQUEST_PICK_IMAGE && data != null) {
//                try {
//                    Uri imageUri = data.getData();
//                    if (imageUri != null && currentPhotoIndex >= 0) {
//                        // Obter o caminho real do arquivo
//                        String[] projection = {MediaStore.Images.Media.DATA};
//                        Cursor cursor = getContentResolver().query(imageUri, projection, null, null, null);
//                        if (cursor != null && cursor.moveToFirst()) {
//                            int columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
//                            currentPhotoPath = cursor.getString(columnIndex);
//                            cursor.close();
//                            processarFoto(currentPhotoIndex, isFromInstalado);
//                        }
//                    }
//                } catch (Exception e) {
//                    Toast.makeText(this, "Erro ao carregar imagem da galeria", Toast.LENGTH_SHORT).show();
//                    e.printStackTrace();
//                }
//            }
//        }
//    }
//@Override
//protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//    super.onActivityResult(requestCode, resultCode, data);
//
//    if (resultCode == RESULT_OK) {
//        if (requestCode == REQUEST_IMAGE_CAPTURE) {
//            if (photoFile != null && currentPhotoIndex >= 0) {
//                Bitmap imageBitmap = BitmapFactory.decodeFile(photoFile.getAbsolutePath());
//                ByteArrayOutputStream stream = new ByteArrayOutputStream();
//                imageBitmap.compress(Bitmap.CompressFormat.JPEG, 50, stream);
//                byte[] byteArray = stream.toByteArray();
//                String base64Image = Base64.encodeToString(byteArray, Base64.DEFAULT);
//                LinearLayout currentLayout = isFromInstalado ? layEquipInstalado : layEquipRetirado;
//                if (currentPhotoIndex < currentLayout.getChildCount()) {
//                    View bloco = currentLayout.getChildAt(currentPhotoIndex);
//                    processarFoto(currentPhotoIndex, isFromInstalado);
//                    processarBlocoComFoto(bloco, imageBitmap);
//                }
//
//            }
//        } else if (requestCode == REQUEST_PICK_IMAGE && data != null) {
//            try {
//                Uri imageUri = data.getData();
//                if (imageUri != null && currentPhotoIndex >= 0) {
//                    Bitmap imageBitmap = MediaStore.Images.Media.getBitmap(getContentResolver(), imageUri);
//
//                    LinearLayout currentLayout = isFromInstalado ? layEquipInstalado : layEquipRetirado;
//                    if (currentPhotoIndex < currentLayout.getChildCount()) {
//                        View bloco = currentLayout.getChildAt(currentPhotoIndex);
//                        processarFoto(currentPhotoIndex, isFromInstalado);
//                        processarBlocoComFoto(bloco, imageBitmap);
//                    }
//                }
//            } catch (Exception e) {
//                Toast.makeText(this, "Erro ao carregar imagem da galeria", Toast.LENGTH_SHORT).show();
//                e.printStackTrace();
//            }
//        }
//    }
//}
@Override
protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);

    if (resultCode == RESULT_OK) {
        if (requestCode == REQUEST_IMAGE_CAPTURE) {
            if (photoFile != null && currentPhotoIndex >= 0) {
                // Processa a foto para mostrar a miniatura
                currentPhotoPath = photoFile.getAbsolutePath();
                processarFoto(currentPhotoIndex, isFromInstalado);

                // Processa os dados do equipamento
                Bitmap imageBitmap = BitmapFactory.decodeFile(photoFile.getAbsolutePath());
                LinearLayout currentLayout = isFromInstalado ? layEquipInstalado : layEquipRetirado;
                if (currentPhotoIndex < currentLayout.getChildCount()) {
                    View bloco = currentLayout.getChildAt(currentPhotoIndex);
                    processarBlocoComFoto(bloco, imageBitmap);
                }
            }
        } else if (requestCode == REQUEST_PICK_IMAGE && data != null) {
            try {
                Uri imageUri = data.getData();
                if (imageUri != null && currentPhotoIndex >= 0) {
                    // Processa a foto para mostrar a miniatura
                    String[] projection = {MediaStore.Images.Media.DATA};
                    Cursor cursor = getContentResolver().query(imageUri, projection, null, null, null);
                    if (cursor != null && cursor.moveToFirst()) {
                        int columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
                        currentPhotoPath = cursor.getString(columnIndex);
                        cursor.close();
                        processarFoto(currentPhotoIndex, isFromInstalado);
                    }

                    // Processa os dados do equipamento
                    Bitmap imageBitmap = MediaStore.Images.Media.getBitmap(getContentResolver(), imageUri);
                    LinearLayout currentLayout = isFromInstalado ? layEquipInstalado : layEquipRetirado;
                    if (currentPhotoIndex < currentLayout.getChildCount()) {
                        View bloco = currentLayout.getChildAt(currentPhotoIndex);
                        processarBlocoComFoto(bloco, imageBitmap);
                    }
                }
            } catch (Exception e) {
                Toast.makeText(this, "Erro ao carregar imagem da galeria", Toast.LENGTH_SHORT).show();
                e.printStackTrace();
            }
        }
    }
}
    private void processarBlocoComFoto(View bloco, Bitmap imageBitmap) {
        AppDatabase db = AppDatabase.getDatabase(this);
        EquipamentoVisitaDao dao = db.equipamentoVisitaDao();
        // Obtém os componentes do bloco
        MaterialAutoCompleteTextView spinner = bloco.findViewById(isFromInstalado ? R.id.spinnerOpcoesInst : R.id.spinnerOptions);
        TextInputEditText edtNumeroMedidor = bloco.findViewById(isFromInstalado
                ? R.id.edtNumeroMedidorInstalado
                : R.id.edtNumeroMedidorRetirado);
        TextInputEditText edtNumeroDisplay = bloco.findViewById(isFromInstalado
                ? R.id.edtNumeroDisplayInstalado
                : R.id.edtNumeroDisplayRetirado);

        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        imageBitmap.compress(Bitmap.CompressFormat.JPEG, 50, stream);
        byte[] byteArray = stream.toByteArray();
        String base64Image = Base64.encodeToString(byteArray, Base64.DEFAULT);

        // Cria o objeto Foto
        Foto foto = new Foto();
        foto.setFoto_nome("Foto_" + ordId + "_" + pegaDataHora(dataagora));
        foto.setFoto_extensao("PNG");
        foto.setFoto_dado(""); // Garantir que não seja null
        foto.setFoto_dado_base64(base64Image != null ? base64Image : "");
        foto.setFoto_tipo("Equipamento");

        // Obtem informações do equipamento
        String descricaoEquipamento = spinner.getText().toString();
        String numeroMedidor = edtNumeroMedidor.getText().toString();
        String numeroDisplay = edtNumeroDisplay.getText().toString();

        // Lista correspondente
        List<EquipamentoVisita> listaEquipamentos = isFromInstalado ? equipamentosInstalados : equipamentosRetirados;

        // Identifica o índice do bloco
        int index = isFromInstalado ? layEquipInstalado.indexOfChild(bloco) : layEquipRetirado.indexOfChild(bloco);

        // Atualiza o equipamento existente na lista
        if (index >= 0 && index < listaEquipamentos.size()) {
            EquipamentoVisita equipamento = listaEquipamentos.get(index);
            equipamento.setEqv_descricao(descricaoEquipamento);
            equipamento.setEqv_nmedidor(numeroMedidor);
            equipamento.setEqv_nserie(numeroMedidor);
            equipamento.setEqv_ndisplay(numeroDisplay);
            equipamento.setFoto(foto);
            equipamento.setVisitaId(visitaId);
            new Thread(() -> dao.insert(equipamento)).start();
            Toast.makeText(this, "Foto associada ao equipamento existente!", Toast.LENGTH_SHORT).show();

        } else {
            // Cria novo equipamento e adiciona à lista
            EquipamentoVisita novoEquipamento = new EquipamentoVisita();
            novoEquipamento.setEqv_descricao(descricaoEquipamento);
            novoEquipamento.setEqv_nmedidor(numeroMedidor);
            novoEquipamento.setEqv_nserie(numeroMedidor);
            novoEquipamento.setEqv_ndisplay(numeroDisplay);
            novoEquipamento.setVisitaId(visitaId);
            novoEquipamento.setFoto(foto);

            listaEquipamentos.add(novoEquipamento);
            new Thread(() -> dao.insert(novoEquipamento)).start();
            Toast.makeText(this, "Foto associada a um novo equipamento!", Toast.LENGTH_SHORT).show();
        }

        isPhotoTaken = true;
    }

    private void atualizarListaEquipamentos(List<EquipamentoVisita> equipamentos) {
        //Toast.makeText(this, "Dados recuperados", Toast.LENGTH_SHORT).show();
        // Aqui você atualiza a UI, por exemplo, um RecyclerView ou Lista
    }

    private void validarEIniciarCapturaDeFoto(View bloco) {
        // Obtém os componentes do bloco
        MaterialAutoCompleteTextView spinner = bloco.findViewById(isFromInstalado ? R.id.spinnerOpcoesInst : R.id.spinnerOptions);
        TextInputEditText edtNumeroMedidor = bloco.findViewById(isFromInstalado ? R.id.edtNumeroMedidorInstalado : R.id.edtNumeroMedidorRetirado);
        TextInputEditText edtNumeroDisplay = bloco.findViewById(isFromInstalado ? R.id.edtNumeroDisplayInstalado : R.id.edtNumeroDisplayRetirado);

        // Obtém os valores dos campos
        String equipamentoSelecionado = spinner.getText().toString();
        String numeroMedidor = edtNumeroMedidor.getText().toString().trim();
        String numeroDisplay = edtNumeroDisplay.getText().toString().trim();

        // Valida os campos obrigatórios
        if (equipamentoSelecionado.equals("Selecione")) {
            Toast.makeText(this, "Selecione um equipamento antes de tirar a foto.", Toast.LENGTH_SHORT).show();
            return;
        }

        if (numeroMedidor.isEmpty() && numeroDisplay.isEmpty()) {
            Toast.makeText(this, "Preencha os dados do equipamento.", Toast.LENGTH_SHORT).show();
            return;
        }
        dispatchTakePictureIntent();
    }

    private void showConfirmDialog() {
        Dialog dialog = new Dialog(this, R.style.DialogTheme);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_confirmar_registro);

        Window window = dialog.getWindow();
        if (window != null) {
            window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }

        Button btnSim = dialog.findViewById(R.id.btnSim);
        Button btnNo = dialog.findViewById(R.id.btnNo);

        btnSim.setOnClickListener(v -> {
            dialog.dismiss();
            Intent intentConfirma = new Intent(RegistroSimActivity.this, RegistroSimActivity.class);
            intentConfirma.putExtra("ordemId", ordId);
            intentConfirma.putExtra("et", et);

            startActivity(intentConfirma);
            finish();
        });

        btnNo.setOnClickListener(v -> {
            dialog.dismiss();
            Intent intentVisita = new Intent(RegistroSimActivity.this, RegistroNaoActivity.class);
            intentVisita.putExtra("ordemId", ordId);
            intentVisita.putExtra("et", et);
            startActivity(intentVisita);
            finish();
        });

        dialog.show();
    }

    // Método auxiliar para verificar se é um equipamento especial
    private boolean isEquipamentoEspecial(String equipamento) {
        return equipamento.equals("medidor convencional") ||
                equipamento.equals("caixa cp / cs") ||
                equipamento.equals("rádio cs") ||
                equipamento.equals("cpu cs");
    }

    private void dispatchTakePictureIntent() {
        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        if (takePictureIntent.resolveActivity(getPackageManager()) != null) {
            try {
                photoFile = createImageFile(); // Criar arquivo para salvar a foto
                if (photoFile != null) {
                    photoUri = FileProvider.getUriForFile(this, "com.baramaia.hercules", photoFile);
                    takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                    startActivityForResult(takePictureIntent, REQUEST_IMAGE_CAPTURE);
                }
            } catch (IOException ex) {
                ex.printStackTrace();
                Toast.makeText(this, "Erro ao criar arquivo de imagem", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private File createImageFile() throws IOException {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        return File.createTempFile(imageFileName, ".jpg", storageDir);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Liberar memória das miniaturas e imagens completas
        for (Bitmap thumbnail : thumbnailsInstalados) {
            if (thumbnail != null && !thumbnail.isRecycled()) {
                thumbnail.recycle();
            }
        }
        for (Bitmap thumbnail : thumbnailsRetirados) {
            if (thumbnail != null && !thumbnail.isRecycled()) {
                thumbnail.recycle();
            }
        }
        for (Bitmap fullImage : fullImagesInstalados) {
            if (fullImage != null && !fullImage.isRecycled()) {
                fullImage.recycle();
            }
        }
        for (Bitmap fullImage : fullImagesRetirados) {
            if (fullImage != null && !fullImage.isRecycled()) {
                fullImage.recycle();
            }
        }
        thumbnailsInstalados.clear();
        thumbnailsRetirados.clear();
        fullImagesInstalados.clear();
        fullImagesRetirados.clear();
    }

    private void processarFoto(int position, boolean isInstalado) {
        try {
            // Criar bitmap da foto
            Bitmap bitmap = BitmapFactory.decodeFile(currentPhotoPath);

            // Corrigir orientação da foto
            bitmap = corrigirOrientacaoFoto(bitmap, currentPhotoPath);

            // Criar miniatura
            Bitmap thumbnail = Bitmap.createScaledBitmap(bitmap, 200, 200, true);

            // Atualizar as listas
            if (isInstalado) {
                thumbnailsInstalados.set(position, thumbnail);
                fullImagesInstalados.set(position, bitmap);
            } else {
                thumbnailsRetirados.set(position, thumbnail);
                fullImagesRetirados.set(position, bitmap);
            }

            // Atualizar a UI na thread principal
            runOnUiThread(() -> {
                LinearLayout currentLayout = isInstalado ? layEquipInstalado : layEquipRetirado;
                if (currentLayout != null && position < currentLayout.getChildCount()) {
                    View blocoEquipamento = currentLayout.getChildAt(position);

                    ImageView thumbnailView = blocoEquipamento.findViewById(
                        isInstalado ? R.id.thumbnailFotoInstalado : R.id.thumbnailFotoRetirado
                    );

                    CardView cardView = blocoEquipamento.findViewById(
                        isInstalado ? R.id.cardFotoInstalado : R.id.cardFotoRetirado
                    );

                    if (thumbnailView != null && cardView != null) {
                        thumbnailView.setImageBitmap(thumbnail);
                        cardView.setVisibility(View.VISIBLE);

                        // Configurar click listener para abrir a imagem em tela cheia
                        cardView.setOnClickListener(v -> {
                            Dialog dialog = new Dialog(this, R.style.DialogTheme);
                            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
                            dialog.setContentView(R.layout.dialog_fullscreen_image);

                            Window window = dialog.getWindow();
                            if (window != null) {
                                window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
                                window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                            }

                            // Configurar clique fora da imagem para fechar
                            View dialogView = dialog.findViewById(android.R.id.content);
                            if (dialogView != null) {
                                dialogView.setOnClickListener(v1 -> dialog.dismiss());
                            }

                            ImageView fullImageView = dialog.findViewById(R.id.fullscreenImageView);
                            if (fullImageView != null) {
                                Bitmap fullImage = isInstalado ?
                                    fullImagesInstalados.get(position) :
                                    fullImagesRetirados.get(position);
                                fullImageView.setImageBitmap(fullImage);
                                fullImageView.setScaleType(ImageView.ScaleType.FIT_CENTER);
                                // Impedir que o clique na imagem feche o diálogo
                                fullImageView.setOnClickListener(null);
                            }

                            dialog.show();
                        });
                    }
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Erro ao processar foto", Toast.LENGTH_SHORT).show();
        }
    }

    private Bitmap corrigirOrientacaoFoto(Bitmap bitmap, String imagePath) {
        try {
            ExifInterface exif = new ExifInterface(imagePath);
            int orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);

            Matrix matrix = new Matrix();
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    matrix.postRotate(90);
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    matrix.postRotate(180);
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    matrix.postRotate(270);
                    break;
                case ExifInterface.ORIENTATION_FLIP_HORIZONTAL:
                    matrix.postScale(-1, 1);
                    break;
                case ExifInterface.ORIENTATION_FLIP_VERTICAL:
                    matrix.postScale(1, -1);
                    break;
                case ExifInterface.ORIENTATION_TRANSPOSE:
                    matrix.postRotate(90);
                    matrix.postScale(-1, 1);
                    break;
                case ExifInterface.ORIENTATION_TRANSVERSE:
                    matrix.postRotate(270);
                    matrix.postScale(-1, 1);
                    break;
                default:
                    return bitmap;
            }

            return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
        } catch (IOException e) {
            e.printStackTrace();
            return bitmap;
        }
    }
}