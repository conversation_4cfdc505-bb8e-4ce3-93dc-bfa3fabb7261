package com.baramaia.hercules.models;

import com.google.gson.annotations.SerializedName;

public class Empreiteira {
    @SerializedName("id")
    private int id;

    @SerializedName("emp_desc")
    private String nome;

    @SerializedName("medidoresRetirados")
    private int medidoresRetirados;

    @SerializedName("medidoresInstalados")
    private int medidoresInstalados;

    @SerializedName("qtdVisitas")
    private int qtdVisitas;

    public Empreiteira() {}

    public Empreiteira(int id, String nome) {
        this.id = id;
        this.nome = nome;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public int getMedidoresRetirados() {
        return medidoresRetirados;
    }

    public void setMedidoresRetirados(int medidoresRetirados) {
        this.medidoresRetirados = medidoresRetirados;
    }

    public int getMedidoresInstalados() {
        return medidoresInstalados;
    }

    public void setMedidoresInstalados(int medidoresInstalados) {
        this.medidoresInstalados = medidoresInstalados;
    }

    public int getQtdVisitas() {
        return qtdVisitas;
    }

    public void setQtdVisitas(int qtdVisitas) {
        this.qtdVisitas = qtdVisitas;
    }

    @Override
    public String toString() {
        return nome != null ? nome : "Empreiteira " + id;
    }
}