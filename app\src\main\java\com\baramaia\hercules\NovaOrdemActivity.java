package com.baramaia.hercules;

import android.os.Bundle;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.viewpager2.widget.ViewPager2;

import com.baramaia.hercules.adapters.TabPagerAdapter;
import com.baramaia.hercules.models.Avulso;
import com.baramaia.hercules.models.MotivoAvulso;
import com.baramaia.hercules.utilities.SharedPrefHercules;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

public class NovaOrdemActivity extends AppCompatActivity {

    private ViewPager2 viewPager;
    private TabLayout tabLayout;
    private ImageView imgBack;
    private TabPagerAdapter tabPagerAdapter;

    // Dados do Avulso
    private String nomeCliente = "";
    private String uc = "";
    private String display = "";
    private String logradouro = "";
    private String numero = "";
    private String complemento = "";
    private String municipio = "";
    private int municipioId = 0;
    private String nucleo = "";
    private int nucleoId = 0;
    private String cp = "";
    private String et = "";
    private String cs = "";
    private String modulo = "";
    private String pos1 = "";
    private String pos2 = "";
    private String pos3 = "";
    private String tanque = "";
    private String motivoDescricao = "";
    private int motivoId = 0;
    private String empreiteiraDescricao = "";
    private int empreiteiraId = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_nova_ordem);

        // Configurar a StatusBar com texto e ícones brancos
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.custom_border_color));
        getWindow().getDecorView().setSystemUiVisibility(0); // Remover a flag View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR

        // Inicializar views
        viewPager = findViewById(R.id.viewPager);
        tabLayout = findViewById(R.id.tabLayout);
        imgBack = findViewById(R.id.imgBack);

        // Configurar botão de voltar
        imgBack.setOnClickListener(view -> finish());

        // Configurar ViewPager e TabLayout
        setupViewPager();
    }

    private void setupViewPager() {
        tabPagerAdapter = new TabPagerAdapter(this);
        viewPager.setAdapter(tabPagerAdapter);

        // Desabilitar o swipe entre tabs para controlar a navegação apenas pelos botões
        viewPager.setUserInputEnabled(false);

        // Configurar TabLayout com ViewPager2 (2 tabs apenas)
        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            switch (position) {
                case 0:
                    tab.setText("Informações");
                    break;
                case 1:
                    tab.setText("Confirmar");
                    break;
            }
        }).attach();
    }

    // Método para navegar entre as tabs
    public void setCurrentItem(int position) {
        if (viewPager != null) {
            viewPager.setCurrentItem(position);
        }
    }

    // Getters e Setters para os dados do Avulso
    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getUc() {
        return uc;
    }

    public void setUc(String uc) {
        this.uc = uc;
    }

    public String getDisplayValue() {
        return display;
    }

    public void setDisplayValue(String display) {
        this.display = display;
    }

    public String getLogradouro() {
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getMunicipio() {
        return municipio;
    }

    public void setMunicipio(String municipio) {
        this.municipio = municipio;
    }

    public int getMunicipioId() {
        return municipioId;
    }

    public void setMunicipioId(int municipioId) {
        this.municipioId = municipioId;
    }

    public String getNucleo() {
        return nucleo;
    }

    public void setNucleo(String nucleo) {
        this.nucleo = nucleo;
    }

    public int getNucleoId() {
        return nucleoId;
    }

    public void setNucleoId(int nucleoId) {
        this.nucleoId = nucleoId;
    }

    public String getCp() {
        return cp;
    }

    public void setCp(String cp) {
        this.cp = cp;
    }

    public String getEt() {
        return et;
    }

    public void setEt(String et) {
        this.et = et;
    }

    public String getCs() {
        return cs;
    }

    public void setCs(String cs) {
        this.cs = cs;
    }

    public String getModulo() {
        return modulo;
    }

    public void setModulo(String modulo) {
        this.modulo = modulo;
    }

    public String getPos1() {
        return pos1;
    }

    public void setPos1(String pos1) {
        this.pos1 = pos1;
    }

    public String getPos2() {
        return pos2;
    }

    public void setPos2(String pos2) {
        this.pos2 = pos2;
    }

    public String getPos3() {
        return pos3;
    }

    public void setPos3(String pos3) {
        this.pos3 = pos3;
    }

    public String getTanque() {
        return tanque;
    }

    public void setTanque(String tanque) {
        this.tanque = tanque;
    }

    public String getMotivoDescricao() {
        return motivoDescricao;
    }

    public void setMotivoDescricao(String motivoDescricao) {
        this.motivoDescricao = motivoDescricao;
    }

    public int getMotivoId() {
        return motivoId;
    }

    public void setMotivoId(int motivoId) {
        this.motivoId = motivoId;
    }

    public String getEmpreiteiraDescricao() {
        return empreiteiraDescricao;
    }

    public void setEmpreiteiraDescricao(String empreiteiraDescricao) {
        this.empreiteiraDescricao = empreiteiraDescricao;
    }

    public int getEmpreiteiraId() {
        return empreiteiraId;
    }

    public void setEmpreiteiraId(int empreiteiraId) {
        this.empreiteiraId = empreiteiraId;
    }

    // Método para criar um objeto Avulso com dados reais do formulário
    public Avulso criarAvulso() {
        Avulso avulso = new Avulso();

        android.util.Log.e("AVULSO_DEBUG", "=== CRIANDO AVULSO COM DADOS REAIS ===");

        // Técnico
        SharedPrefHercules sharedPref = new SharedPrefHercules(this);
        String nomeUsuario = sharedPref.carregarPrefHercules(sharedPref.USUARIO_NOME);
        avulso.setTecnico(nomeUsuario != null ? nomeUsuario : "SISTEMA");

        // Campos obrigatórios com dados reais
        avulso.setDisplay(display != null && !display.isEmpty() ? display : "0000000");
        avulso.setModulo(Integer.parseInt(modulo.isEmpty() ? "0" : modulo));
        avulso.setMotivoId(motivoId);
        
        // Município (obrigatório) - usar dados reais
        avulso.setMunicipio(municipio != null ? municipio : "");
        avulso.setMunicipioId(municipioId);
        
        // Empreiteira (obrigatório) - usar dados reais
        avulso.setEmpreiteira(empreiteiraDescricao != null ? empreiteiraDescricao : "");
        avulso.setEmpreiteiraId(empreiteiraId);
        
        // Logradouro (obrigatório) - usar dados reais ou solicitar
        avulso.setLogradouro(logradouro != null && !logradouro.isEmpty() ? logradouro : "Não informado");
        
        // Outros campos com dados reais
        avulso.setNome(nomeCliente != null && !nomeCliente.isEmpty() ? nomeCliente : "Cliente");
        avulso.setUc(!uc.isEmpty() ? Integer.parseInt(uc) : 0);
        avulso.setNumero(numero != null ? numero : "");
        avulso.setComplemento(complemento != null ? complemento : "");
        avulso.setNucleo(nucleo != null ? nucleo : "");
        avulso.setNucleoId(nucleoId);
        
        // Campos numéricos com dados reais
        avulso.setCp(!cp.isEmpty() ? Integer.parseInt(cp) : 0);
        avulso.setCs(!cs.isEmpty() ? Integer.parseInt(cs) : 0);
        avulso.setEt(!et.isEmpty() ? Integer.parseInt(et) : 0);
        avulso.setTanque(!tanque.isEmpty() ? Integer.parseInt(tanque) : 0);
        
        // Posições
        avulso.setPos1(pos1 != null ? pos1 : "");
        avulso.setPos2(pos2 != null ? pos2 : "");
        avulso.setPos3(pos3 != null ? pos3 : "");
        
        // Motivo como null (apenas ID é necessário)
        avulso.setMotivo(null);

        android.util.Log.e("AVULSO_DEBUG", "Display: " + avulso.getDisplay());
        android.util.Log.e("AVULSO_DEBUG", "Módulo: " + avulso.getModulo());
        android.util.Log.e("AVULSO_DEBUG", "MotivoId: " + avulso.getMotivoId());
        android.util.Log.e("AVULSO_DEBUG", "Município: " + avulso.getMunicipio() + " (ID: " + avulso.getMunicipioId() + ")");
        android.util.Log.e("AVULSO_DEBUG", "Empreiteira: " + avulso.getEmpreiteira() + " (ID: " + avulso.getEmpreiteiraId() + ")");
        android.util.Log.e("AVULSO_DEBUG", "Logradouro: " + avulso.getLogradouro());
        android.util.Log.e("AVULSO_DEBUG", "Técnico: " + avulso.getTecnico());

        return avulso;
    }
}
