<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <data />
    <LinearLayout
        android:id="@+id/linearGroup43"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_8pxh"
        android:paddingVertical="@dimen/_8pxv">
        <TextView
            android:id="@+id/txtTitle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:ellipsize="end"
            android:singleLine="true"
            android:textSize="@dimen/_14pxh"
            android:textColor="@android:color/black"
            tools:ignore="SpUsage" />
    </LinearLayout>
</layout>