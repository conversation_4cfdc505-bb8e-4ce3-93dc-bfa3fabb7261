package com.baramaia.hercules.utilities;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.R;
import com.baramaia.hercules.RowOuvidoriasAdapter;
import com.baramaia.hercules.models.OrdemServico;

import java.util.List;

public class RowOrdensAdapter extends RecyclerView.Adapter<RowOrdensAdapter.ViewHolder>{
    private List<OrdemServico> ordemList;
    private RowOuvidoriasAdapter.OnItemClickListener listener;

    public interface OnItemClickListener {
        void onItemClick(int id);
    }
    public void setOnItemClickListener(RowOuvidoriasAdapter.OnItemClickListener listener) {
        this.listener = listener;
    }
    @NonNull
    @Override
    public RowOrdensAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_ordens_et, parent, false);
        return new RowOrdensAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RowOrdensAdapter.ViewHolder holder, int position) {

    }

    @Override
    public int getItemCount() {
        return ordemList.size();
    }
    public static class ViewHolder extends RecyclerView.ViewHolder {
        public TextView txtHeaderText;
        public TextView txtSubheadText;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            txtHeaderText = itemView.findViewById(R.id.txtHeaderText);
            txtSubheadText = itemView.findViewById(R.id.txtSubheadText);
        }
    }
}
