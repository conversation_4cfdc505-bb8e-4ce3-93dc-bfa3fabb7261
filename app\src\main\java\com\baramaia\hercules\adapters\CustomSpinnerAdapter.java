package com.baramaia.hercules.adapters;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.List;

/**
 * Adapter para o spinners que garante que o texto seja visível
 */
public class CustomSpinnerAdapter<T> extends ArrayAdapter<T> {

    public CustomSpinnerAdapter(@NonNull Context context, int resource, @NonNull List<T> objects) {
        super(context, resource, objects);
    }

    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        View view = super.getView(position, convertView, parent);

        // Garantir que o texto seja visível
        if (view instanceof TextView) {
            // Se for a posição 0 (placeholder), usar cor cinza claro
            if (position == 0) {
                ((TextView) view).setTextColor(Color.parseColor("#9E9E9E")); // Cinza médio
            } else {
                ((TextView) view).setTextColor(Color.BLACK);
            }
            ((TextView) view).setTextSize(16);
        }

        return view;
    }

    @Override
    public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        View view = super.getDropDownView(position, convertView, parent);

        // Garantir que o texto seja visível no dropdown
        if (view instanceof TextView) {
            // Forçar cor preta para todos os itens do dropdown
            ((TextView) view).setTextColor(Color.BLACK);
            // Aumentar o tamanho do texto para melhor visibilidade
            ((TextView) view).setTextSize(16);
            // Adicionar padding para melhor espaçamento
            ((TextView) view).setPadding(16, 16, 16, 16);
            // Definir background branco para melhor contraste
            view.setBackgroundColor(Color.WHITE);
        }

        return view;
    }
}
