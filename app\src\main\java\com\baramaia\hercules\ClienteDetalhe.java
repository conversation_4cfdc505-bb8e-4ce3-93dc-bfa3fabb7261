public class ClienteDetalhe {
    private String endereco;
    private String complemento;
    private String uc;
    private String et;
    private String cs;
    private String pos;
    private String medidor;
    private String display;

    public ClienteDetalhe(String endereco, String complemento, String uc,
                          String et, String cs, String pos,
                          String medidor, String display) {
        this.endereco = endereco;
        this.complemento = complemento;
        this.uc = uc;
        this.et = et;
        this.cs = cs;
        this.pos = pos;
        this.medidor = medidor;
        this.display = display;
    }

    // Getters
    public String getEndereco() {
        return endereco;
    }

    public String getComplemento() {
        return complemento;
    }

    public String getUc() {
        return uc;
    }

    public String getEt() {
        return et;
    }

    public String getCs() {
        return cs;
    }

    public String getPos() {
        return pos;
    }

    public String getMedidor() {
        return medidor;
    }

    public String getDisplay() {
        return display;
    }
}