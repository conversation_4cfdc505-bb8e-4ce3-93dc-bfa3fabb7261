package com.baramaia.hercules.utilities;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

/**
 * Classe utilitária para exibir Toasts de forma segura em diferentes contextos.
 */
public class ToastUtils {
    private static final String TAG = "HERCULES_TOAST";
    private static Toast currentToast;

    /**
     * Exibe um Toast de forma segura, cancelando qualquer Toast anterior.
     *
     * @param context O contexto da aplicação
     * @param message A mensagem a ser exibida
     * @param duration A duração do Toast (Toast.LENGTH_SHORT ou Toast.LENGTH_LONG)
     */
    public static void showToast(@Nullable Context context, @NonNull String message, int duration) {
        if (context == null) {
            Log.e(TAG, "Context is null, can't show toast: " + message);
            return;
        }

        // Logar a mensagem para depuração
        Log.d(TAG, "Toast message: " + message);

        // Garantir que o Toast seja exibido na thread principal
        new Handler(Looper.getMainLooper()).post(() -> {
            // Cancelar qualquer Toast anterior
            if (currentToast != null) {
                currentToast.cancel();
            }

            // Criar e exibir o novo Toast
            currentToast = Toast.makeText(context.getApplicationContext(), message, duration);
            currentToast.show();
        });
    }

    /**
     * Exibe um Toast de forma segura a partir de um Fragment, verificando se o Fragment
     * ainda está anexado à Activity.
     *
     * @param fragment O Fragment a partir do qual o Toast será exibido
     * @param message A mensagem a ser exibida
     * @param duration A duração do Toast (Toast.LENGTH_SHORT ou Toast.LENGTH_LONG)
     */
    public static void showToast(@Nullable Fragment fragment, @NonNull String message, int duration) {
        if (fragment == null || !fragment.isAdded()) return;

        showToast(fragment.requireContext(), message, duration);
    }

    /**
     * Exibe um Toast curto de forma segura.
     *
     * @param context O contexto da aplicação
     * @param message A mensagem a ser exibida
     */
    public static void showShortToast(@Nullable Context context, @NonNull String message) {
        showToast(context, message, Toast.LENGTH_SHORT);
    }

    /**
     * Exibe um Toast longo de forma segura.
     *
     * @param context O contexto da aplicação
     * @param message A mensagem a ser exibida
     */
    public static void showLongToast(@Nullable Context context, @NonNull String message) {
        showToast(context, message, Toast.LENGTH_LONG);
    }

    /**
     * Exibe um Toast curto de forma segura a partir de um Fragment.
     *
     * @param fragment O Fragment a partir do qual o Toast será exibido
     * @param message A mensagem a ser exibida
     */
    public static void showShortToast(@Nullable Fragment fragment, @NonNull String message) {
        showToast(fragment, message, Toast.LENGTH_SHORT);
    }

    /**
     * Exibe um Toast longo de forma segura a partir de um Fragment.
     *
     * @param fragment O Fragment a partir do qual o Toast será exibido
     * @param message A mensagem a ser exibida
     */
    public static void showLongToast(@Nullable Fragment fragment, @NonNull String message) {
        showToast(fragment, message, Toast.LENGTH_LONG);
    }

    /**
     * Loga uma mensagem de erro detalhada e exibe um Toast com a mensagem resumida.
     *
     * @param fragment O Fragment a partir do qual o Toast será exibido
     * @param tag Tag para o log
     * @param shortMessage Mensagem resumida para o Toast
     * @param detailedMessage Mensagem detalhada para o log
     * @param throwable Exception associada ao erro (opcional)
     */
    public static void logAndShowError(@Nullable Fragment fragment, @NonNull String tag,
                                      @NonNull String shortMessage, @NonNull String detailedMessage,
                                      @Nullable Throwable throwable) {
        // Logar a mensagem detalhada
        Log.e(tag, detailedMessage);

        // Logar a exception, se houver
        if (throwable != null) {
            Log.e(tag, "Exception: " + throwable.getMessage(), throwable);
        }

        // Exibir Toast com a mensagem resumida
        showShortToast(fragment, shortMessage);
    }
}
