<?xml version="1.0" encoding="UTF-8"?>


<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/linearColumnarrowLeftOne"
    style="@style/groupStyleblue_gray_50"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_gray">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbarToolbar"
            style="@style/Widget.Material3.Toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/background_gray">

            <LinearLayout
                android:id="@+id/linearRowarrowleft"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/imageArrowleftOne"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/img_arrow_left"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/txtHeadline"
                    style="@style/TextAppearance.Material3.TitleLarge"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/lbl_ouvidoria" />
            </LinearLayout>
        </com.google.android.material.appbar.MaterialToolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- Container Principal -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <!-- Container do Conteúdo -->
        <LinearLayout
            android:id="@+id/contentLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Container do Skeleton -->
            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmerLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <!-- Layout do Skeleton -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Skeleton para o Título -->
                    <View
                        android:layout_width="200dp"
                        android:layout_height="24dp"
                        android:background="@color/shimmer_color" />

                    <!-- Skeleton para o Conteúdo -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="100dp"
                        android:layout_marginTop="16dp"
                        android:background="@color/shimmer_color" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="100dp"
                        android:layout_marginTop="16dp"
                        android:background="@color/shimmer_color" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="100dp"
                        android:layout_marginTop="16dp"
                        android:background="@color/shimmer_color" />
                </LinearLayout>

            </com.facebook.shimmer.ShimmerFrameLayout>

            <!-- Conteúdo Real (inicialmente invisível) -->
            <LinearLayout
                android:id="@+id/realContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- Card de Informações -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    app:cardElevation="0dp"
                    app:cardCornerRadius="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- NA Counter -->
                        <TextView
                            android:id="@+id/txtNa"
                            style="@style/TextAppearance.Material3.TitleMedium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="?android:attr/textColorPrimary"
                            tools:text="NA 123456" />

                        <!-- Container de informações -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:orientation="horizontal">

                            <!-- Informações -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/txtMunicipio"
                                    style="@style/TextAppearance.Material3.BodyLarge"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="?android:attr/textColorSecondary"
                                    tools:text="Guarulhos" />

                                <TextView
                                    android:id="@+id/txtVilaunio"
                                    style="@style/TextAppearance.Material3.BodyMedium"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:textColor="?android:attr/textColorSecondary"
                                    tools:text="Vila União" />

                                <TextView
                                    android:id="@+id/txtCp"
                                    style="@style/TextAppearance.Material3.BodyMedium"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="8dp"
                                    android:textColor="?android:attr/textColorSecondary"
                                    tools:text="CP 789" />
                            </LinearLayout>

                            <!-- Botões -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btnLocation"
                                    style="@style/Widget.Material3.Button.IconButton.Filled"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="8dp"
                                    app:icon="@drawable/ic_location"
                                    app:iconTint="@color/white" />

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btnIconbutton"
                                    style="@style/Widget.Material3.Button.IconButton.Filled"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:icon="@drawable/img_group_5"
                                    app:iconTint="@color/white" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <!-- Observação -->
                <TextView
                    android:id="@+id/textObs"
                    style="@style/TextAppearance.Material3.TitleMedium"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="Observação" />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:enabled="false"
                    app:boxStrokeWidth="0.5dp"
                    app:boxBackgroundColor="@color/white"
                    app:hintEnabled="false"
                    app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Small">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/txtObservacao"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="top"
                        android:minLines="4"
                        android:enabled="false"
                        android:textIsSelectable="true"
                        android:focusable="false"
                        android:clickable="false"
                        android:background="@color/white"
                        android:textColor="@color/black_900"
                        android:padding="12dp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Lista -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewLista"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:layout_marginTop="16dp"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Botão Finalizar -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginVertical="16dp">

        <com.github.leandroborgesferreira.loadingbutton.customViews.CircularProgressButton
            android:id="@+id/btnRegistrar"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:backgroundTint="@color/blue_gray_800"
            android:gravity="center"
            android:padding="16dp"
            android:text="@string/msg_registrar_visita"
            android:textAllCaps="false"
            android:textColor="@color/white"
            app:cornerRadius="8dp"
            app:spinning_bar_color="@color/white"
            app:spinning_bar_padding="6dp"
            app:spinning_bar_width="4dp" />
    </FrameLayout>
</LinearLayout>
