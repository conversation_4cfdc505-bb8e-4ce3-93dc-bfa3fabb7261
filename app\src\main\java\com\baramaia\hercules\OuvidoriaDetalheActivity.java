package com.baramaia.hercules;

import android.app.Dialog;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.baramaia.hercules.models.ClienteOuvidoria;
import com.baramaia.hercules.models.Ouvidoria;
import com.baramaia.hercules.network.ApiCalls;
import com.baramaia.hercules.network.ApiDados;
import com.baramaia.hercules.utilities.SharedPrefHercules;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.github.chrisbanes.photoview.PhotoView;
import com.google.android.material.button.MaterialButton;

import java.io.IOException;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class OuvidoriaDetalheActivity extends AppCompatActivity {

    ApiDados apiDados = new ApiDados();
    private ApiCalls apiCalls;
    private TextView txtNa;
    private TextView txtMunicipio;
    private TextView txtCp;
    private TextView txtObservacao;
    private LinearLayout lnOuvidoria;
    private Button btnRegistrar;
    private MaterialButton btnIconbutton;
    private MaterialButton btnLocation;
    private RecyclerView recyclerViewLista;
    private ListaAdapter adapter;
    private byte[] imageBytes = null;
    //private OuvidoriaMob ouvidoria;
    private Ouvidoria ouvidoria;
    private ShimmerFrameLayout shimmerLayout;
    private LinearLayout realContent;
    private int ordId;
    private int et;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_ouvidoria_detalhe);

        Intent intent = getIntent();
        ordId = intent.getIntExtra("ORDEM_ID", -1);

        initializeViews();
        setupClickListeners();
        startLoading();
        loadData();
    }

    private void initializeViews() {
        txtNa = findViewById(R.id.txtNa);
        txtMunicipio = findViewById(R.id.txtMunicipio);
        txtCp = findViewById(R.id.txtCp);
        txtObservacao = findViewById(R.id.txtObservacao);
        btnLocation = findViewById(R.id.btnLocation);
        lnOuvidoria = findViewById(R.id.linearRowarrowleft);
        btnRegistrar = findViewById(R.id.btnRegistrar);
        btnIconbutton = findViewById(R.id.btnIconbutton);
        recyclerViewLista = findViewById(R.id.recyclerViewLista);
        shimmerLayout = findViewById(R.id.shimmerLayout);
        realContent = findViewById(R.id.realContent);
    }

    private void setupClickListeners() {
        lnOuvidoria.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getApplicationContext(), IniciarVisitaActivity.class);
                startActivity(intent);
            }
        });

        btnRegistrar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showConfirmDialog();
            }
        });
        btnIconbutton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                abrirImagem(imageBytes);
            }
        });
    }

    private void startLoading() {
        shimmerLayout.setVisibility(View.VISIBLE);
        shimmerLayout.startShimmer();
        realContent.setVisibility(View.GONE);
    }

    private void stopLoading() {
        shimmerLayout.stopShimmer();
        shimmerLayout.setVisibility(View.GONE);
        realContent.setVisibility(View.VISIBLE);
    }

    private void loadData() {
        ouvidoria = new Ouvidoria();
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(OuvidoriaDetalheActivity.this);
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);

        int ouvId = getIntent().getIntExtra("OUVIDORIA_ID", -1);
        if (ouvId != -1) {
            carregarDetalhesOuvidoria(ouvId, token);
        }
    }

    private void carregarDetalhesOuvidoria(int id, String token) {
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        Call<Ouvidoria> callouvid = apiCalls.getOuvidoriaId(id, token);
        callouvid.enqueue(new Callback<Ouvidoria>() {
            @Override
            public void onResponse(Call<Ouvidoria> callouvid, Response<Ouvidoria> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ouvidoria = response.body();
                    carregarArquivo(ouvidoria.getOuv_na(), token);
                    if (ouvidoria.getOuv_gps() != null) {
                        btnLocation.setVisibility(View.VISIBLE);
                        btnLocation.setOnClickListener(view -> {
                            copiarParaAreaDeTransferencia(ouvidoria.getOuv_gps());
                        });
                    }
                    ouvidoria.setOuv_ordId(ordId);
                    if (ouvidoria.getOuv_gps() == null) {
                        btnLocation.setVisibility(View.GONE);
                    }
                    if (ouvidoria.getId() == 0) {
                        Toast.makeText(OuvidoriaDetalheActivity.this, "Não há ouvidorias!", Toast.LENGTH_SHORT).show();
                    } else {
                        txtNa.setText("NA " + ouvidoria.getOuv_na());
                        txtMunicipio.setText(ouvidoria.getOuv_municipio() + ", " + ouvidoria.getOuv_nucleo());
                        txtObservacao.setText(ouvidoria.getOuv_observacao());

                        adapter = new ListaAdapter(ouvidoria.getClientes(), new ListaAdapter.OnItemClickListener() {
                            @Override
                            public void onItemClick(ClienteOuvidoria cliente) {
                                ClienteDetailsBottomSheet bottomSheet = ClienteDetailsBottomSheet.newInstance(
                                        cliente.getOuv_titular(),
                                        cliente.getOuv_logradouro(),
                                        cliente.getOuv_complemento(),
                                        String.valueOf(cliente.getOuv_uc()),
                                        String.valueOf(cliente.getOuv_et()),
                                        String.valueOf(cliente.getOuv_cs()),
                                        String.valueOf(cliente.getOuv_pos1()),
                                        String.valueOf(cliente.getOuv_modulo()),
                                        String.valueOf(cliente.getOuv_display())
                                );
                                bottomSheet.show(getSupportFragmentManager(), "ClienteDetailsBottomSheet");
                            }
                        });

                        recyclerViewLista.setAdapter(adapter);
                        adapter.notifyDataSetChanged();
                    }
                    stopLoading();
                } else {
                    handleError("Erro ao carregar dados");
                }
            }

            @Override
            public void onFailure(Call<Ouvidoria> call, Throwable t) {
                handleError("Erro de conexão");
            }
        });
    }

    private void carregarArquivo(int na, String token) {
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        Call<ResponseBody> callouvid = apiCalls.getArquivoNa(na, token);
        callouvid.enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> callouvid, Response<ResponseBody> response) {
                if (response.isSuccessful() && response.body() != null) {
                    try {
                        btnIconbutton.setVisibility(View.VISIBLE);
                        imageBytes = response.body().bytes();
                    } catch (IOException e) {
                        e.printStackTrace();
                        Toast.makeText(OuvidoriaDetalheActivity.this,
                                "Erro ao processar arquivo: " + e.getMessage(),
                                Toast.LENGTH_SHORT).show();
                    }
                } else {
                    btnIconbutton.setVisibility(View.GONE);
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                Toast.makeText(OuvidoriaDetalheActivity.this, "Erro ao buscar arquivo", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void abrirImagem(byte[] imageBytes) {
        try {
            final Dialog dialog = new Dialog(OuvidoriaDetalheActivity.this);
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);

            // Criar PhotoView
            PhotoView photoView = new PhotoView(OuvidoriaDetalheActivity.this);
            photoView.setLayoutParams(new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.MATCH_PARENT
            ));

            // Configurar o PhotoView
            photoView.setScaleType(ImageView.ScaleType.FIT_CENTER);

            // Usar Glide para carregar os bytes no PhotoView
            Glide.with(OuvidoriaDetalheActivity.this)
                    .asBitmap()
                    .load(imageBytes)
                    .into(photoView);

            // Configurar o dialog
            dialog.setContentView(photoView);
            if (dialog.getWindow() != null) {
                dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.parseColor("#33000000")));
                dialog.getWindow().setLayout(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        LinearLayout.LayoutParams.MATCH_PARENT
                );
            }

            // Configurar clique para fechar
            photoView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });

            dialog.show();

        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(OuvidoriaDetalheActivity.this,
                    "Erro ao abrir imagem: " + e.getMessage(),
                    Toast.LENGTH_SHORT).show();
        }
    }

    private void copiarParaAreaDeTransferencia(String texto) {
        ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("Texto Copiado", texto);
        clipboard.setPrimaryClip(clip);

        // Exibir uma mensagem para o usuário
        //Toast.makeText(this, "Texto copiado para a área de transferência", Toast.LENGTH_SHORT).show();
    }

    private void showConfirmDialog() {
        Dialog dialog = new Dialog(this, R.style.DialogTheme);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_confirmar_registro);

        Window window = dialog.getWindow();
        if (window != null) {
            window.setLayout(WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT);
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            
            WindowManager.LayoutParams params = window.getAttributes();
            params.gravity = android.view.Gravity.CENTER;
            window.setAttributes(params);
        }

        Button btnSim = dialog.findViewById(R.id.btnSim);
        Button btnNo = dialog.findViewById(R.id.btnNo);

        btnSim.setOnClickListener(v -> {
            dialog.dismiss();
            Intent intentVisitaEfetivada = new Intent(OuvidoriaDetalheActivity.this, DescricaoActivity.class);
            intentVisitaEfetivada.putExtra("ouvidoriaId", ouvidoria.getId());
            intentVisitaEfetivada.putExtra("ordemId", ordId);
            startActivity(intentVisitaEfetivada);
            finish();
        });

        btnNo.setOnClickListener(v -> {
            dialog.dismiss();
            Intent intentVisitaNaoEfetivada = new Intent(OuvidoriaDetalheActivity.this, RegistroNaoActivity.class);
            intentVisitaNaoEfetivada.putExtra("ouvidoriaId", ouvidoria.getId());
            intentVisitaNaoEfetivada.putExtra("ordemId", ordId);
            startActivity(intentVisitaNaoEfetivada);
            finish();
        });

        dialog.show();
    }

    private void handleError(String message) {
        stopLoading();
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    private void updateUI(Ouvidoria ouvidoria) {
        // Seu código atual de atualização da UI
    }
}