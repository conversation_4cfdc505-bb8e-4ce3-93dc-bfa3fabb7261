package com.baramaia.hercules.fragments;

import android.os.Bundle;
import android.text.InputFilter;
import android.text.Spanned;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import com.github.rygelouv.androidloadingbuttonlib.LoadingButton;
import android.widget.EditText;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.baramaia.hercules.NovaOrdemActivity;
import com.baramaia.hercules.R;
import com.baramaia.hercules.adapters.MotivosSpinnerAdapter;
import com.baramaia.hercules.models.Empreiteira;
import com.baramaia.hercules.models.MotivoAvulso;
import com.baramaia.hercules.models.Municipio;
import com.baramaia.hercules.models.Nucleo;
import com.baramaia.hercules.network.ApiCalls;
import com.baramaia.hercules.network.ApiDados;
import com.baramaia.hercules.network.LoggingInterceptor;
import com.baramaia.hercules.utilities.SharedPrefHercules;
import com.google.android.material.textfield.MaterialAutoCompleteTextView;

import java.util.ArrayList;
import java.util.List;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class TabInfoBasicasFragment extends Fragment {

    private static final String TAG = "TabInfoBasicasFragment";

    // Views
    private MaterialAutoCompleteTextView spinnerMotivo;
    private MaterialAutoCompleteTextView spinnerEmpreiteira;
    private MaterialAutoCompleteTextView spinnerMunicipio;
    private EditText etCp, etCs, etModulo, etDisplay;
    private LoadingButton btnProximoTab1;

    private SharedPrefHercules sharedPrefHercules;
    private ApiCalls apiCalls;

    // Adapters e listas
    private List<MotivoAvulso> listaMotivos = new ArrayList<>();
    private List<Empreiteira> listaEmpreiteiras = new ArrayList<>();
    private List<Municipio> listaMunicipios = new ArrayList<>();
    private ArrayAdapter<MotivoAvulso> motivoAdapter;
    private ArrayAdapter<Empreiteira> empreiteiraAdapter;
    private ArrayAdapter<Municipio> municipioAdapter;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_tab_info_basicas, container, false);

        // Inicializar SharedPreferences e API
        sharedPrefHercules = new SharedPrefHercules(requireContext());
        ApiDados apiDados = new ApiDados();

        // Configurar OkHttpClient com o interceptor de log
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(new LoggingInterceptor())
                .build();

        // Configurar Retrofit com o cliente OkHttp
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        // Inicializar views
        spinnerMotivo = view.findViewById(R.id.spinnerMotivo);
        spinnerEmpreiteira = view.findViewById(R.id.spinnerEmpreiteira);
        spinnerMunicipio = view.findViewById(R.id.spinnerMunicipio);
        etCp = view.findViewById(R.id.etCp);
        etCs = view.findViewById(R.id.etCs);
        etModulo = view.findViewById(R.id.etModulo);
        etDisplay = view.findViewById(R.id.etDisplay);
        btnProximoTab1 = view.findViewById(R.id.btnProximoTab1);

        // Configurar filtros de entrada
        configurarFiltros();
        
        // Configurar listeners para mostrar dicas
        configurarDicasCamposTecnicos();

        // Configurar adapters
        configurarSpinnerMotivo();
        configurarSpinnerEmpreiteira();
        configurarSpinnerMunicipio();

        // Carregar dados das APIs
        carregarMotivos();
        carregarEmpreiteiras();
        carregarMunicipios();

        // Carregar dados salvos se existirem
        carregarDadosSalvos();

        btnProximoTab1.setOnClickListener(v -> {
            btnProximoTab1.startLoading("Carregando...");
            if (validarCampos()) {
                salvarDados();
                if (getActivity() instanceof NovaOrdemActivity) {
                    ((NovaOrdemActivity) getActivity()).setCurrentItem(1);
                }
            }
            btnProximoTab1.stopLoading("Próximo");
        });
        
        return view;
    }


    private void configurarFiltros() {
        // Filtro para CP (máximo 4 dígitos)
        InputFilter[] filtersCp = new InputFilter[1];
        filtersCp[0] = new InputFilter.LengthFilter(4);
        etCp.setFilters(filtersCp);

        // Filtro para CS (máximo 3 dígitos)
        InputFilter[] filtersCs = new InputFilter[1];
        filtersCs[0] = new InputFilter.LengthFilter(3);
        etCs.setFilters(filtersCs);

        // Filtro para Módulo (máximo 8 dígitos)
        InputFilter[] filtersModulo = new InputFilter[1];
        filtersModulo[0] = new InputFilter.LengthFilter(8);
        etModulo.setFilters(filtersModulo);

        // Filtro para Display (máximo 7 dígitos)
        InputFilter[] filtersDisplay = new InputFilter[1];
        filtersDisplay[0] = new InputFilter.LengthFilter(7);
        etDisplay.setFilters(filtersDisplay);
    }

    private void configurarSpinnerMotivo() {
        // Criar adapter para motivos
        motivoAdapter = new ArrayAdapter<>(requireContext(), android.R.layout.simple_dropdown_item_1line, listaMotivos);
        spinnerMotivo.setAdapter(motivoAdapter);
        
        spinnerMotivo.setOnItemClickListener((parent, view, position, id) -> {
            MotivoAvulso motivoSelecionado = listaMotivos.get(position);
            if (getActivity() instanceof NovaOrdemActivity) {
                NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
                activity.setMotivoId(motivoSelecionado.getId());
                activity.setMotivoDescricao(motivoSelecionado.getDescricao());
            }
        });
    }

    private void configurarSpinnerEmpreiteira() {
        // Criar adapter para empreiteiras
        empreiteiraAdapter = new ArrayAdapter<>(requireContext(), android.R.layout.simple_dropdown_item_1line, listaEmpreiteiras);
        spinnerEmpreiteira.setAdapter(empreiteiraAdapter);
    }

    private void configurarSpinnerMunicipio() {
        // Criar adapter para municípios
        municipioAdapter = new ArrayAdapter<>(requireContext(), android.R.layout.simple_dropdown_item_1line, listaMunicipios);
        spinnerMunicipio.setAdapter(municipioAdapter);
        
        spinnerMunicipio.setOnItemClickListener((parent, view, position, id) -> {
            Municipio municipioSelecionado = listaMunicipios.get(position);
            if (getActivity() instanceof NovaOrdemActivity) {
                NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
                activity.setMunicipioId(municipioSelecionado.getId());
                activity.setMunicipio(municipioSelecionado.getNome());
            }
        });
    }

    private void carregarMotivos() {
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);
        if (token != null && !token.startsWith("Bearer ")) {
            token = "Bearer " + token;
        }

        Call<List<MotivoAvulso>> call = apiCalls.getMotivosAvulso(token);
        call.enqueue(new Callback<List<MotivoAvulso>>() {
            @Override
            public void onResponse(Call<List<MotivoAvulso>> call, Response<List<MotivoAvulso>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<MotivoAvulso> motivosRecebidos = response.body();

                    // Formatar descrições
                    for (MotivoAvulso motivo : motivosRecebidos) {
                        String descricaoOriginal = motivo.getDescricao();
                        if (descricaoOriginal != null && !descricaoOriginal.isEmpty()) {
                            String descricaoFormatada = formatarNome(descricaoOriginal);
                            motivo.setDescricao(descricaoFormatada);
                        }
                    }

                    listaMotivos.clear();
                    listaMotivos.addAll(motivosRecebidos);
                    motivoAdapter.notifyDataSetChanged();

                    // Restaurar seleção salva
                    restaurarSelecaoMotivo();
                } else {
                    Toast.makeText(requireContext(), "Erro ao carregar motivos: " + response.code(), Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<MotivoAvulso>> call, Throwable t) {
                Toast.makeText(requireContext(), "Falha ao carregar motivos: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void carregarEmpreiteiras() {
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);
        if (token != null && !token.startsWith("Bearer ")) {
            token = "Bearer " + token;
        }

        Call<List<Empreiteira>> call = apiCalls.getEmpreiteiras(token);
        call.enqueue(new Callback<List<Empreiteira>>() {
            @Override
            public void onResponse(Call<List<Empreiteira>> call, Response<List<Empreiteira>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    listaEmpreiteiras.clear();
                    listaEmpreiteiras.addAll(response.body());
                    empreiteiraAdapter.notifyDataSetChanged();
                } else {
                    Toast.makeText(requireContext(), "Erro ao carregar empreiteiras: " + response.code(), Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<Empreiteira>> call, Throwable t) {
                Toast.makeText(requireContext(), "Falha ao carregar empreiteiras: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void carregarMunicipios() {
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);
        if (token != null && !token.startsWith("Bearer ")) {
            token = "Bearer " + token;
        }

        Call<List<Municipio>> call = apiCalls.getMunicipios(token);
        call.enqueue(new Callback<List<Municipio>>() {
            @Override
            public void onResponse(Call<List<Municipio>> call, Response<List<Municipio>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    listaMunicipios.clear();
                    listaMunicipios.addAll(response.body());
                    municipioAdapter.notifyDataSetChanged();

                    // Restaurar seleção salva
                    restaurarSelecaoMunicipio();
                } else {
                    Toast.makeText(requireContext(), "Erro ao carregar municípios: " + response.code(), Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<Municipio>> call, Throwable t) {
                Toast.makeText(requireContext(), "Falha ao carregar municípios: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }



    /**
     * Formata um nome para ter a primeira letra de cada palavra maiúscula e as demais minúsculas
     * @param nome Nome a ser formatado
     * @return Nome formatado
     */
    private String formatarNome(String nome) {
        if (nome == null || nome.isEmpty()) {
            return nome;
        }

        // Converter para minúsculas primeiro
        nome = nome.toLowerCase();

        // Dividir em palavras
        String[] palavras = nome.split(" ");
        StringBuilder resultado = new StringBuilder();

        for (String palavra : palavras) {
            if (!palavra.isEmpty()) {
                // Primeira letra maiúscula, resto minúscula
                resultado.append(Character.toUpperCase(palavra.charAt(0)))
                        .append(palavra.substring(1))
                        .append(" ");
            }
        }

        return resultado.toString().trim();
    }

    private boolean validarCampos() {
        if (spinnerMotivo.getText().toString().isEmpty()) {
            Toast.makeText(requireContext(), "Necessário selecionar um motivo da visita!", Toast.LENGTH_SHORT).show();
            return false;
        }
        
        if (etModulo.getText().toString().trim().isEmpty()) {
            Toast.makeText(requireContext(), "Necessário preencher o número do módulo!", Toast.LENGTH_SHORT).show();
            etModulo.requestFocus();
            return false;
        }
        
        if (etDisplay.getText().toString().trim().isEmpty()) {
            Toast.makeText(requireContext(), "Necessário preencher o número do display!", Toast.LENGTH_SHORT).show();
            etDisplay.requestFocus();
            return false;
        }

        // Validação adicional para campos técnicos
        if (!validarCamposTecnicos()) {
            return false;
        }
        
        return true;
    }

    private boolean validarCamposTecnicos() {
        String display = etDisplay.getText().toString().trim();
        String cp = etCp.getText().toString().trim();
        String cs = etCs.getText().toString().trim();
        String modulo = etModulo.getText().toString().trim();

        // Verificar se Display tem pelo menos 3 dígitos
        if (display.length() < 3) {
            Toast.makeText(requireContext(), "Display deve ter pelo menos 3 dígitos!", Toast.LENGTH_SHORT).show();
            etDisplay.requestFocus();
            return false;
        }

        // Verificar se CP e CS são válidos quando preenchidos
        if (!cp.isEmpty() && cp.length() < 2) {
            Toast.makeText(requireContext(), "CP deve ter pelo menos 2 dígitos quando preenchido!", Toast.LENGTH_SHORT).show();
            etCp.requestFocus();
            return false;
        }

        if (!cs.isEmpty() && cs.length() < 2) {
            Toast.makeText(requireContext(), "CS deve ter pelo menos 2 dígitos quando preenchido!", Toast.LENGTH_SHORT).show();
            etCs.requestFocus();
            return false;
        }

        // Verificar se Módulo tem pelo menos 3 dígitos
        if (modulo.length() < 3) {
            Toast.makeText(requireContext(), "Módulo deve ter pelo menos 3 dígitos!", Toast.LENGTH_SHORT).show();
            etModulo.requestFocus();
            return false;
        }

        return true;
    }

    private void configurarDicasCamposTecnicos() {
        // Adicionar listeners para mostrar dicas sobre duplicação
        View.OnFocusChangeListener focusListener = (v, hasFocus) -> {
            if (hasFocus) {
                Toast.makeText(requireContext(), 
                    "Lembre-se: Display + CP + CS + Módulo devem formar uma combinação única", 
                    Toast.LENGTH_SHORT).show();
            }
        };
        
        etDisplay.setOnFocusChangeListener(focusListener);
        etCp.setOnFocusChangeListener(focusListener);
        etCs.setOnFocusChangeListener(focusListener);
        etModulo.setOnFocusChangeListener(focusListener);
    }

    private void carregarDadosSalvos() {
        if (getActivity() instanceof NovaOrdemActivity) {
            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
            
            // Carregar campos técnicos
            if (activity.getCp() != null && !activity.getCp().isEmpty()) {
                etCp.setText(activity.getCp());
            }
            if (activity.getCs() != null && !activity.getCs().isEmpty()) {
                etCs.setText(activity.getCs());
            }
            if (activity.getModulo() != null && !activity.getModulo().isEmpty()) {
                etModulo.setText(activity.getModulo());
            }
            if (activity.getDisplayValue() != null && !activity.getDisplayValue().isEmpty()) {
                etDisplay.setText(activity.getDisplayValue());
            }
            
            // Carregar município e núcleo salvos será feito após carregar as listas das APIs
            // nos métodos restaurarSelecaoMunicipio() e restaurarSelecaoNucleo()
        }
    }

    private void restaurarSelecaoMotivo() {
        if (getActivity() instanceof NovaOrdemActivity) {
            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
            int motivoId = activity.getMotivoId();
            if (motivoId > 0) {
                for (MotivoAvulso motivo : listaMotivos) {
                    if (motivo.getId() == motivoId) {
                        spinnerMotivo.setText(motivo.getDescricao(), false);
                        break;
                    }
                }
            }
        }
    }

    private void restaurarSelecaoMunicipio() {
        if (getActivity() instanceof NovaOrdemActivity) {
            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
            int municipioId = activity.getMunicipioId();
            if (municipioId > 0) {
                for (Municipio municipio : listaMunicipios) {
                    if (municipio.getId() == municipioId) {
                        spinnerMunicipio.setText(municipio.getNome(), false);
                        break;
                    }
                }
            }
        }
    }

    private void salvarDados() {
        if (getActivity() instanceof NovaOrdemActivity) {
            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();

            // Salvar motivo selecionado
            String motivoTexto = spinnerMotivo.getText().toString();
            android.util.Log.d("MOTIVO_DEBUG", "Texto do spinner motivo: '" + motivoTexto + "'");
            android.util.Log.d("MOTIVO_DEBUG", "Lista de motivos tem " + listaMotivos.size() + " itens");
            
            boolean motivoEncontrado = false;
            for (MotivoAvulso motivo : listaMotivos) {
                android.util.Log.d("MOTIVO_DEBUG", "Comparando com: '" + motivo.getDescricao() + "' (ID: " + motivo.getId() + ")");
                if (motivo.getDescricao().equals(motivoTexto)) {
                    activity.setMotivoId(motivo.getId());
                    activity.setMotivoDescricao(motivo.getDescricao());
                    android.util.Log.d("MOTIVO_DEBUG", "✓ Motivo encontrado e salvo: " + motivo.getDescricao() + " (ID: " + motivo.getId() + ")");
                    motivoEncontrado = true;
                    break;
                }
            }
            
            if (!motivoEncontrado) {
                android.util.Log.e("MOTIVO_ERROR", "✗ Motivo não encontrado na lista! Texto: '" + motivoTexto + "'");
            }

            // Salvar município selecionado
            String municipioTexto = spinnerMunicipio.getText().toString();
            boolean municipioEncontrado = false;
            for (Municipio municipio : listaMunicipios) {
                if (municipio.getNome().equals(municipioTexto)) {
                    activity.setMunicipioId(municipio.getId());
                    activity.setMunicipio(municipio.getNome());
                    android.util.Log.e("MUNICIPIO_DEBUG", "✓ Município salvo: " + municipio.getNome() + " (ID: " + municipio.getId() + ")");
                    municipioEncontrado = true;
                    break;
                }
            }
            
            if (!municipioEncontrado && !municipioTexto.isEmpty()) {
                android.util.Log.e("MUNICIPIO_ERROR", "✗ Município não encontrado: '" + municipioTexto + "'");
            }

            // Salvar empreiteira selecionada
            String empreiteiraTexto = spinnerEmpreiteira.getText().toString();
            boolean empreiteiraEncontrada = false;
            for (Empreiteira empreiteira : listaEmpreiteiras) {
                if (empreiteira.getNome().equals(empreiteiraTexto)) {
                    activity.setEmpreiteiraId(empreiteira.getId());
                    activity.setEmpreiteiraDescricao(empreiteira.getNome());
                    android.util.Log.e("EMPREITEIRA_DEBUG", "✓ Empreiteira salva: " + empreiteira.getNome() + " (ID: " + empreiteira.getId() + ")");
                    empreiteiraEncontrada = true;
                    break;
                }
            }
            
            if (!empreiteiraEncontrada && !empreiteiraTexto.isEmpty()) {
                android.util.Log.e("EMPREITEIRA_ERROR", "✗ Empreiteira não encontrada: '" + empreiteiraTexto + "'");
            }

            // Limpar dados de núcleo (não usado mais)
            activity.setNucleoId(0);
            activity.setNucleo("");

            // Salvar campos técnicos
            activity.setCp(etCp.getText().toString().trim());
            activity.setCs(etCs.getText().toString().trim());
            activity.setModulo(etModulo.getText().toString().trim());
            activity.setDisplayValue(etDisplay.getText().toString().trim());
            
            // Limpar campos não utilizados
            activity.setEt("");
            activity.setPos1("");
            activity.setPos2("");
            activity.setPos3("");
            activity.setTanque("");
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        salvarDados();
    }
}
