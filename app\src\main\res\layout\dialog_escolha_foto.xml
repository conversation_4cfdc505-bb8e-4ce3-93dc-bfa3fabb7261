<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="Escolha uma opção"
        android:textAlignment="center"
        android:textSize="18sp"
        android:textStyle="bold" />
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnCamera"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginBottom="8dp"
        android:insetLeft="0dp"
        android:insetTop="0dp"
        android:insetRight="0dp"
        android:insetBottom="0dp"
        android:text="Tirar Foto"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:backgroundTint="@color/custom_border_color"
        app:cornerRadius="8dp"
        app:icon="@drawable/img_iconizer_camera"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:iconSize="20dp" />
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnGaleria"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:insetLeft="0dp"
        android:insetTop="0dp"
        android:insetRight="0dp"
        android:insetBottom="0dp"
        android:text="Escolher da Galeria"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:backgroundTint="@color/custom_border_color"
        app:cornerRadius="8dp"
        app:icon="@drawable/ic_photo_library"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:iconSize="20dp" />
</LinearLayout>