<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    tools:ignore="MissingClass">

    <androidx.cardview.widget.CardView
        android:layout_width="280dp"
        android:layout_height="180dp"
        app:cardCornerRadius="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <View
                android:layout_width="240dp"
                android:layout_height="16dp"
                android:background="@color/shimmer_color" />

            <View
                android:layout_width="200dp"
                android:layout_height="14dp"
                android:layout_marginTop="12dp"
                android:background="@color/shimmer_color" />

            <View
                android:layout_width="180dp"
                android:layout_height="14dp"
                android:layout_marginTop="8dp"
                android:background="@color/shimmer_color" />

            <View
                android:layout_width="160dp"
                android:layout_height="14dp"
                android:layout_marginTop="8dp"
                android:background="@color/shimmer_color" />

            <View
                android:layout_width="140dp"
                android:layout_height="14dp"
                android:layout_marginTop="8dp"
                android:background="@color/shimmer_color" />
        </LinearLayout>

    </androidx.cardview.widget.CardView>

</com.facebook.shimmer.ShimmerFrameLayout> 