<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:foreground="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp"
    android:paddingVertical="16dp"
    android:layout_marginBottom="1dp">

    <!-- Titular -->
    <TextView
        android:id="@+id/txtTitular"
        style="@style/TextAppearance.Material3.BodyLarge"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="?android:attr/textColorPrimary"
        tools:text="JOÃO DA SILVA" />

    <!-- Seta -->
    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:src="@drawable/ic_chevron_right"
        app:tint="?android:attr/textColorSecondary" />

</LinearLayout>