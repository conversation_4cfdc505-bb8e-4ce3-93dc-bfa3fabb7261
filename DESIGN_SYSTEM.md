# 🎨 Hercules App - Design System

## Padrões de Layout

### 📱 <PERSON><PERSON>

```xml
<LinearLayout
    android:id="@+id/headerLayout"
    style="@style/groupStyleblue_gray_50"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="16dp"
    android:paddingTop="16dp"
    android:paddingEnd="16dp"
    android:paddingBottom="16dp">

    <!-- Botão voltar (opcional) -->
    <ImageView
        android:id="@+id/imgBack"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:contentDescription="Voltar"
        android:padding="4dp"
        android:src="@drawable/img_arrow_left" />

    <!-- Título -->
    <TextView
        style="@style/txtRobotoregular22"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:singleLine="true" />
</LinearLayout>
```

### 📝 Seções de Conteúdo

```xml
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <TextView
        style="@style/txtRobotoregular20"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
</LinearLayout>
```

## 🎨 Cores Padrão

| Elemento             | Cor                          | Uso                                 |
| -------------------- | ---------------------------- | ----------------------------------- |
| **Primária**         | `@color/custom_border_color` | Botões, FABs, elementos de destaque |
| **Background**       | `@color/blue_gray_50`        | Fundo das telas                     |
| **Texto Principal**  | `@color/blue_gray_800`       | Textos principais                   |
| **Texto Secundário** | `@color/gray_600`            | Textos secundários                  |

## 🔘 Botões Padrão

### Botão Primário (com Loading integrado)

Observação: usar `LoadingButton` (rygelouv). Em layouts, garanta no root: `xmlns:custom="http://schemas.android.com/apk/res-auto"`.

```xml
<com.github.rygelouv.androidloadingbuttonlib.LoadingButton
    android:id="@+id/btnPrimary"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    custom:background="@drawable/button_blue_gray_rounded"
    custom:text="Ação"
    custom:textColor="@android:color/white"
    custom:textSize="16sp"
    custom:progressColor="@android:color/white" />
```

Alternativas de fundo (conforme tela):

- `@drawable/button_blue_gray_rounded` (radius 12dp)
- `@drawable/rectangle_bg_blue_gray_800_radius_8` (radius 8dp)

### FAB Padrão

```xml
<com.google.android.material.floatingactionbutton.FloatingActionButton
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:backgroundTint="@color/custom_border_color"
    app:tint="@android:color/white" />
```

## 📏 Espaçamentos Padrão

| Elemento                | Valor  | Uso                            |
| ----------------------- | ------ | ------------------------------ |
| **Padding Padrão**      | `16dp` | Margens internas de containers |
| **Padding Pequeno**     | `8dp`  | Espaçamentos menores           |
| **Margin Entre Seções** | `16dp` | Espaço entre seções            |
| **Corner Radius**       | `8dp`  | Bordas arredondadas            |

## 🔤 Tipografia Padrão

| Elemento             | Estilo                      | Uso                      |
| -------------------- | --------------------------- | ------------------------ |
| **Título Principal** | `@style/txtRobotoregular22` | Headers, títulos de tela |
| **Título Seção**     | `@style/txtRobotoregular20` | Títulos de seções        |
| **Texto Corpo**      | `@style/txtRobotoregular18` | Texto principal          |
| **Texto Pequeno**    | `@style/txtRobotoregular16` | Texto secundário         |

## 📱 Layouts de Referência

- ✅ **Nova Ordem** - Padrão de referência
- ✅ **Login** - Botão atualizado
- ✅ **Iniciar Visitas** - Headers padronizados
- ✅ **Iniciar Visitas Vertical** - Já seguindo padrão

### 🏷️ Badges/Contadores

```xml
<TextView
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/rectangle_bg_blue_gray_800_radius_10"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:paddingTop="4dp"
    android:paddingBottom="4dp"
    android:textColor="@color/white"
    android:textSize="12sp" />
```

### 📱 Seções com Contadores

```xml
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <TextView
        style="@style/txtRobotoregular20"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1" />

    <!-- Badge contador opcional -->
    <TextView
        android:background="@drawable/rectangle_bg_blue_gray_800_radius_10"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone" />
</LinearLayout>
```

### 🔄 Estados de Loading

- Preferir `LoadingButton` no lugar de `CircularProgressButton` e de `MaterialButton` com ícone manual.
- Código:

```java
btnPrimary.startLoading("Carregando...");
// ... chamada de rede ...
btnPrimary.stopLoading(getString(R.string.msg_proximo));
```

- Boas práticas de layout para botão fixo no rodapé:
  - `NestedScrollView`: `paddingBottom="80dp"` e `clipToPadding="false"`
  - Botão fora do scroll, em `FrameLayout` com `elevation` e `background` branco

## 🚀 Próximas Melhorias

1. **Cards/Items de Lista** - Padronizar estilos dos cards
2. **Formulários** - Consistência em TextInputLayouts
3. **Estados de Loading** - Skeleton screens padronizados
4. **Dialogs** - Modais com design consistente
5. **Bottom Sheets** - Padrão para sheets

## ✅ Melhorias Implementadas

### Tela "Iniciar Visitas"

- ✅ FAB para Nova Ordem
- ✅ Badges/contadores nas seções
- ✅ Divisores sutis entre seções
- ✅ Padding adequado para FAB
- ✅ Espaçamentos consistentes

### 🃏 Cards Padrão

```xml
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    android:background="@drawable/rectangle_bg_white_a700_radius_12"
    android:orientation="vertical"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="16dp"
    android:paddingBottom="16dp"
    android:elevation="2dp">

    <!-- Conteúdo do card -->
</LinearLayout>
```

### 📝 Formulários Padrão

```xml
<com.google.android.material.textfield.TextInputLayout
    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:boxStrokeColor="@color/custom_border_color"
    app:hintTextColor="@color/custom_border_color"
    app:cornerRadius="8dp">

    <com.google.android.material.textfield.TextInputEditText
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/blue_gray_800" />
</com.google.android.material.textfield.TextInputLayout>
```

## ✅ Melhorias Recentes

### Tela "Notas de Serviço Detalhe"

- ✅ Header padronizado seguindo Nova Ordem
- ✅ Cards com corner radius e elevation
- ✅ Tipografia consistente (Roboto Regular)
- ✅ Cores padronizadas (custom_border_color)
- ✅ Botão principal seguindo padrão
- ✅ TextInputLayout com cores consistentes

### 🏷️ Status e Feedback Visual

```xml
<!-- Badge de Status -->
<TextView
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/rectangle_bg_blue_gray_800_radius_10"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:paddingTop="4dp"
    android:paddingBottom="4dp"
    android:textColor="@color/white"
    android:textSize="12sp" />

<!-- Divisor Sutil -->
<View
    android:layout_width="match_parent"
    android:layout_height="1dp"
    android:layout_marginTop="12dp"
    android:layout_marginBottom="12dp"
    android:background="@color/gray_300_01"
    android:alpha="0.5" />
```

### 📱 ScrollView com Botão Fixo

```xml
<androidx.core.widget.NestedScrollView
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_weight="1"
    android:fillViewport="true"
    android:paddingBottom="80dp"
    android:clipToPadding="false">

    <!-- Conteúdo -->
</androidx.core.widget.NestedScrollView>
```

## ✅ Melhorias Mais Recentes

### Tela "Notas de Serviço Detalhe" - Versão 2.0

- ✅ Divisores visuais entre seções
- ✅ Organização hierárquica das informações técnicas
- ✅ Hint textual para botão GPS
- ✅ Feedback visual ao copiar GPS
- ✅ Padding adequado no ScrollView para botão fixo
- ✅ Seções bem definidas com títulos
- ✅ Layout limpo e focado no conteúdo essencial

### 💬 Dialogs Padrão

```xml
<LinearLayout
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_rounded"
    android:maxWidth="320dp"
    android:minWidth="300dp"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Header com ícone -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_check_circle"
            android:tint="@color/custom_border_color" />

        <TextView
            style="@style/txtRobotoregular22"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:textColor="@color/blue_gray_800" />
    </LinearLayout>

    <!-- Divisor -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:background="@color/gray_300_01"
        android:alpha="0.3" />

    <!-- Mensagem -->
    <TextView
        style="@style/txtRobotoregular16"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/gray_600"
        android:gravity="center" />

    <!-- Botões -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="end"
        android:orientation="horizontal">

        <!-- Botão Secundário (Filled - Cinza) -->
        <com.google.android.material.button.MaterialButton
            style="@style/Widget.Material3.Button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:textAllCaps="false"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            app:backgroundTint="@color/gray_600"
            android:textColor="@color/white"
            app:cornerRadius="8dp" />

        <!-- Botão Primário (Filled - Azul) -->
        <com.google.android.material.button.MaterialButton
            style="@style/Widget.Material3.Button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAllCaps="false"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            app:backgroundTint="@color/custom_border_color"
            android:textColor="@color/white"
            app:cornerRadius="8dp" />
    </LinearLayout>
</LinearLayout>
```

## ✅ Melhorias Mais Recentes

### Dialog "Confirmar Registro"

- ✅ Header com ícone e título alinhados
- ✅ Tipografia consistente (Roboto Regular)
- ✅ Cores padronizadas (custom_border_color)
- ✅ Botões consistentes com telas anteriores (filled)
- ✅ Botão secundário: fundo cinza, texto branco
- ✅ Botão primário: fundo azul, texto branco
- ✅ Padding dos botões: 16dp (igual às telas)
- ✅ Divisor sutil para separar seções
- ✅ Espaçamentos consistentes (16dp, 24dp)
- ✅ Corner radius padronizado (8dp para botões, 16dp para dialog)

## ✅ Melhorias Mais Recentes

### Tela "Descrição da Atividade"

- ✅ Header padronizado seguindo Nova Ordem
- ✅ Cards com corner radius e elevation consistentes
- ✅ TextInputLayouts com cores padronizadas
- ✅ Tipografia consistente (Roboto Regular)
- ✅ Botões de foto com cores padronizadas
- ✅ Botão principal seguindo padrão (largura total)
- ✅ ScrollView com padding adequado para botão fixo
- ✅ Espaçamentos consistentes (16dp)
- ✅ Seções bem organizadas com cards separados

## Loading States (atualizado)

Padrão oficial: `LoadingButton` com spinner integrado.

Exemplos:

```xml
<com.github.rygelouv.androidloadingbuttonlib.LoadingButton
    android:layout_width="match_parent"
    android:layout_height="56dp"
    custom:background="@drawable/button_blue_gray_rounded"
    custom:text="Entrar"
    custom:textColor="@android:color/white"
    custom:textSize="16sp"
    custom:progressColor="@android:color/white" />
```

```java
btnPrimary.startLoading("Carregando...");
// ...
btnPrimary.stopLoading(getString(R.string.lbl_entrar));
```
