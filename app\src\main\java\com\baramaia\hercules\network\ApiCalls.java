package com.baramaia.hercules.network;

import com.baramaia.hercules.models.Avulso;
import com.baramaia.hercules.models.Empreiteira;
import com.baramaia.hercules.models.Equipamento;
import com.baramaia.hercules.models.EquipamentoVisita;
import com.baramaia.hercules.models.Foto;
import com.baramaia.hercules.models.Motivo;
import com.baramaia.hercules.models.MotivoAvulso;
import com.baramaia.hercules.models.Municipio;
import com.baramaia.hercules.models.Nucleo;
import com.baramaia.hercules.models.OrdemEt;
import com.baramaia.hercules.models.OrdemServico;
import com.baramaia.hercules.models.Ouvidoria;
import com.baramaia.hercules.models.UserLogin;
import com.baramaia.hercules.models.Visita;
import com.baramaia.hercules.models.VisitaApi;

import java.util.List;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.Path;

public interface ApiCalls {
    @GET("/api/Arquivos/ouvidoria/{na}")
    Call<ResponseBody> getArquivoNa(@Path("na") int na, @Header("Authorization") String token);

    @GET("/api/Equipamentos")
    Call<List<Equipamento>> getEquipamentos(@Header("Authorization") String authHeader);

    @POST("/api/EquipamentosVisita")
    Call<List<EquipamentoVisita>> postEquipamentosVisita(@Body List<EquipamentoVisita> equipamentos, @Header("Authorization") String authHeader);

    @POST("/api/Fotos")
    Call<List<Foto>> postFotos(@Body List<Foto> fotos, @Header("Authorization") String authHeader);

    @POST("/api/Login/Login")
    Call<UserLogin> login(@Body UserLogin userLogin);

    @GET("/api/Motivos")
    Call<List<Motivo>> getMotivos(@Header("Authorization") String authHeader);

    @GET("/api/OrdensServico/mobile/userid/{userid}")
    Call<List<OrdemEt>> getOrdens(@Path("userid") int userid, @Header("Authorization") String authHeader);
    @GET("/api/OrdensServico/et/{et}/{ordemid}")
    Call<List<OrdemServico>> getOrdensEt(@Path("et") int et,@Path("ordemid") int ordemid, @Header("Authorization") String authHeader);

    @GET("/api/Ouvidorias/userid/{userid}")
    Call<List<Ouvidoria>> getOuvidorias(@Path("userid") int userid, @Header("Authorization") String authHeader);
    @GET("/api/Ouvidorias/{id}")
    Call<Ouvidoria> getOuvidoriaId(@Path("id") int id, @Header("Authorization") String authHeader);

    @POST("/api/Visitas")
    Call<VisitaApi> postVisita(@Body VisitaApi visita, @Header("Authorization") String authHeader);

    // Endpoint para lista de nucleos
    @GET("/api/Nucleos")
    Call<List<Nucleo>> getNucleos(@Header("Authorization") String authHeader);

    // Endpoint para lista de nucleos por id de municipio
    @GET("/api/Nucleos/municipio/{id}")
    Call<List<Nucleo>> getNucleosByMunicipioId(@Path("id") int id, @Header("Authorization") String authHeader);

    // Endpoint para lista de municipios
    @GET("/api/Municipios")
    Call<List<Municipio>> getMunicipios(@Header("Authorization") String authHeader);

    // Endpoint para lista de empreiteiras
    @GET("/api/Empreiteiras")
    Call<List<Empreiteira>> getEmpreiteiras(@Header("Authorization") String authHeader);

    // Endpoints para Avulsos
    @POST("/api/Avulsos")
    Call<Object> postAvulso(@Body Avulso avulso, @Header("Authorization") String authHeader);

    // Endpoint para obter motivos de avulsos
    @GET("/api/MotivosAvulso")
    Call<List<MotivoAvulso>> getMotivosAvulso(@Header("Authorization") String authHeader);
}
