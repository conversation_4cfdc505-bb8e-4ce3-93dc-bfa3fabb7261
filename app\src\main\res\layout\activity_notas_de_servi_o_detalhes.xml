<?xml version="1.0" encoding="UTF-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    style="@style/groupStyleblue_gray_50"
    android:orientation="vertical">

    <!-- Header seguin<PERSON> pad<PERSON><PERSON> da Nova Ordem -->
    <LinearLayout
        android:id="@+id/headerLayout"
        style="@style/groupStyleblue_gray_50"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingTop="16dp"
        android:paddingEnd="16dp"
        android:paddingBottom="16dp">

        <ImageView
            android:id="@+id/imageArrowleftOne"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:contentDescription="Voltar"
            android:padding="4dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/img_arrow_left" />

        <LinearLayout
            android:id="@+id/linearColumnarrowleft"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txtNsTitle"
                style="@style/txtRobotoregular22"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:singleLine="true"
                tools:text="NS 2024110006" />

            <TextView
                android:id="@+id/txtIdTitle"
                style="@style/txtRobotoregular16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/gray_600"
                tools:text="ID 557637" />
        </LinearLayout>
    </LinearLayout>

    <!-- Conteúdo Scrollável com padding para botão fixo -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:paddingBottom="80dp"
        android:clipToPadding="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Card Informações do Cliente seguindo padrão -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/rectangle_bg_white_a700_radius_12"
                android:orientation="vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:elevation="2dp">

                <!-- Nome do Cliente -->
                <TextView
                    android:id="@+id/txtNome"
                    style="@style/txtRobotoregular20"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/blue_gray_800"
                    tools:text="Marcia Moraes Magalhães" />

                <!-- Endereço Completo -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/txtLogradouro"
                        style="@style/txtRobotoregular16"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/gray_600"
                        tools:text="Rua Prefeito Almeirão" />

                    <TextView
                        android:id="@+id/txtComplemento"
                        style="@style/txtRobotoregular16"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:textColor="@color/gray_600"
                        tools:text="Fundos" />
                </LinearLayout>

                <!-- Divisor sutil -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="12dp"
                    android:background="@color/gray_300_01"
                    android:alpha="0.5" />

                <!-- Informações Técnicas Organizadas -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Título da seção técnica -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Informações Técnicas"
                        style="@style/txtRobotoregular16"
                        android:textColor="@color/blue_gray_800"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <!-- Grid de informações -->
                    <GridLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:columnCount="2">

                        <!-- UC e ET -->
                        <TextView
                            android:id="@+id/txtUc"
                            style="@style/txtRobotoregular14"
                            android:layout_width="0dp"
                            android:layout_columnWeight="1"
                            android:textColor="@color/gray_600"
                            android:drawablePadding="4dp"
                            tools:text="UC: 150150150" />

                        <TextView
                            android:id="@+id/txtEt"
                            style="@style/txtRobotoregular14"
                            android:layout_width="0dp"
                            android:layout_columnWeight="1"
                            android:textColor="@color/gray_600"
                            android:drawablePadding="4dp"
                            tools:text="ET: 667059" />

                        <!-- CS e Posições -->
                        <TextView
                            android:id="@+id/txtCs"
                            style="@style/txtRobotoregular14"
                            android:layout_width="0dp"
                            android:layout_columnWeight="1"
                            android:layout_marginTop="8dp"
                            android:textColor="@color/gray_600"
                            android:drawablePadding="4dp"
                            tools:text="CS: 37" />

                        <TextView
                            android:id="@+id/txtPosicoes"
                            style="@style/txtRobotoregular14"
                            android:layout_width="0dp"
                            android:layout_columnWeight="1"
                            android:layout_marginTop="8dp"
                            android:textColor="@color/gray_600"
                            android:drawablePadding="4dp"
                            tools:text="Posições: 1a 2b 3c" />

                        <!-- Medidor e Display -->
                        <TextView
                            android:id="@+id/txtModulo"
                            style="@style/txtRobotoregular14"
                            android:layout_width="0dp"
                            android:layout_columnWeight="1"
                            android:layout_marginTop="8dp"
                            android:textColor="@color/gray_600"
                            android:drawablePadding="4dp"
                            tools:text="Medidor: 142536" />

                        <TextView
                            android:id="@+id/txtDisplay"
                            style="@style/txtRobotoregular14"
                            android:layout_width="0dp"
                            android:layout_columnWeight="1"
                            android:layout_marginTop="8dp"
                            android:textColor="@color/gray_600"
                            android:drawablePadding="4dp"
                            tools:text="Display: 142536" />
                    </GridLayout>
                </LinearLayout>

                <!-- Botão GPS com melhor UX -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="end"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txtGpsHint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="8dp"
                        android:text="Copiar GPS"
                        android:textSize="12sp"
                        android:textColor="@color/gray_600"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnGps"
                        style="@style/Widget.Material3.Button.IconButton.Filled"
                        android:layout_width="76dp"
                        android:layout_height="48dp"
                        app:backgroundTint="@color/custom_border_color"
                        app:icon="@drawable/ic_location"
                        app:iconGravity="textStart"
                        app:iconTint="@color/white"
                        app:cornerRadius="8dp" />
                </LinearLayout>
            </LinearLayout>

            <!-- Card Descrição seguindo padrão -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/rectangle_bg_white_a700_radius_12"
                android:orientation="vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:elevation="2dp">

                <!-- Título da seção -->
                <TextView
                    android:id="@+id/textDescricao"
                    style="@style/txtRobotoregular20"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/blue_gray_800"
                    android:text="Descrição" />

                <!-- Serviço -->
                <TextView
                    android:id="@+id/txtServico"
                    style="@style/txtRobotoregular16"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textColor="@color/blue_gray_800"
                    tools:text="CS 104 - RETIRADA DE MEDIDOR" />

                <!-- Motivo -->
                <TextView
                    android:id="@+id/txtMotivo"
                    style="@style/txtRobotoregular16"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/gray_600"
                    tools:text="7.02 CS ERRO DE LEITURA" />

                <!-- Campo de Observação seguindo padrão -->
                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    app:boxBackgroundColor="@color/white"
                    app:boxStrokeColor="@color/custom_border_color"
                    app:hintTextColor="@color/custom_border_color"
                    app:boxStrokeWidth="1dp"
                    app:hintEnabled="false"
                    app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Small">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/txtObs"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:clickable="false"
                        android:focusable="false"
                        android:gravity="top"
                        android:minLines="4"
                        android:padding="12dp"
                        android:textColor="@color/blue_gray_800"
                        android:textIsSelectable="true"
                        android:visibility="gone"
                        tools:text="Observações adicionais..." />

                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Container do Botão Fixo seguindo padrão -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="8dp"
        android:background="@color/white">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnRegistrar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="16dp"
            android:text="@string/msg_proximo"
            android:textAllCaps="false"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            app:backgroundTint="@color/custom_border_color"
            android:textColor="@color/white"
            app:cornerRadius="8dp" />
    </FrameLayout>
</LinearLayout>








