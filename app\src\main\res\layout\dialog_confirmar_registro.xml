<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_rounded"
    android:maxWidth="320dp"
    android:minWidth="300dp"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Header com ícone e título -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <!-- Ícone de confirmação -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_check_circle"
            android:tint="@color/custom_border_color"
            android:contentDescription="Confirmar" />

        <!-- <PERSON><PERSON><PERSON><PERSON> se<PERSON> padr<PERSON> do Design System -->
        <TextView
            android:id="@+id/txtTitle"
            style="@style/txtRobotoregular22"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:text="Confirmar Registro"
            android:textColor="@color/blue_gray_800" />
    </LinearLayout>

    <!-- Divisor sutil -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:background="@color/gray_300_01"
        android:alpha="0.3" />

    <!-- Mensagem seguindo padrão do Design System -->
    <TextView
        android:id="@+id/txtMessage"
        style="@style/txtRobotoregular16"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="O serviço foi executado?"
        android:textColor="@color/gray_600"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <!-- Botões -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="end"
        android:orientation="horizontal">

        <!-- Botão Não com cor anterior -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnNo"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Não"
            android:textAllCaps="false"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:textColor="@color/custom_border_color"
            app:strokeColor="@color/custom_border_color"
            app:strokeWidth="1dp"
            app:cornerRadius="4dp" />

        <!-- Botão Sim com radius menor -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnSim"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Sim"
            android:textAllCaps="false"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            app:backgroundTint="@color/custom_border_color"
            android:textColor="@color/white"
            app:cornerRadius="4dp" />

    </LinearLayout>
</LinearLayout>
