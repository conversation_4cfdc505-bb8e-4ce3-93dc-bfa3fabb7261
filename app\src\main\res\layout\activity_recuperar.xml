<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/linearColumn"
    style="@style/groupStyleblue_gray_50"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/linearColumnvectortwo"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="start"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/frameStackcimalogin"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_327pxv"
            android:layout_gravity="top|center">

            <ImageView
                android:id="@+id/imageCimaloginOne"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_327pxh"
                android:layout_gravity="center"
                android:scaleType="fitXY"
                android:src="@drawable/img_cima_login"
                tools:ignore="ContentDescription" />

            <LinearLayout
                android:id="@+id/linearRowdiamantebran"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top|end"
                android:layout_marginTop="@dimen/_84pxv"
                android:layout_marginEnd="@dimen/_35pxh"
                android:gravity="end"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/imageDiamantebranco"
                    android:layout_width="@dimen/_57pxh"
                    android:layout_height="@dimen/_48pxh"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="@dimen/_4pxv"
                    android:layout_marginBottom="@dimen/_1pxv"
                    android:scaleType="fitXY"
                    android:src="@drawable/img_diamante_branco"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/txtHrcules"
                    style="@style/txtRobotomedium46"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10pxh"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:singleLine="true"
                    android:text="@string/lbl_h_rcules"
                    tools:text="@string/lbl_h_rcules" />
            </LinearLayout>
        </FrameLayout>

        <TextView
            android:id="@+id/txtRecuperar"
            style="@style/txtRubikromanmedium24"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_61pxh"
            android:layout_marginTop="@dimen/_48pxv"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="@string/lbl_recuperar"
            tools:text="@string/lbl_recuperar" />

        <View
            android:id="@+id/lineLineeightOne"
            android:layout_width="@dimen/_82pxh"
            android:layout_height="@dimen/_1pxv"
            android:layout_marginStart="@dimen/_61pxh"
            android:layout_marginTop="@dimen/_5pxv"
            android:background="@color/blue_gray_800" />

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginEnd="40dp"
            android:layout_marginTop="20dp"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
            app:endIconMode="custom"
            app:endIconDrawable="@drawable/img_lock">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etEnteruserOne"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/msg_entre_com_o_usuario"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="158dp">

            <com.github.leandroborgesferreira.loadingbutton.customViews.CircularProgressButton
                android:id="@+id/btnEnviar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="35dp"
                android:layout_marginEnd="47dp"
                android:text="@string/lbl_enviar"
                android:textAllCaps="false"
                android:backgroundTint="@color/blue_gray_800"
                android:textColor="@color/white"
                android:gravity="center"
                android:layout_gravity="center"
                app:spinning_bar_width="4dp"
                app:spinning_bar_color="@color/white"
                app:spinning_bar_padding="6dp"
                app:cornerRadius="8dp" />
        </FrameLayout>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/frameBottombar"
        style="@style/groupStylewhite_A700"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal" />

</LinearLayout>
