package com.baramaia.hercules.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.R;
import com.facebook.shimmer.ShimmerFrameLayout;

public class SkeletonAdapter extends RecyclerView.Adapter<SkeletonAdapter.SkeletonViewHolder> {

    private final boolean isOuvidoria;

    public SkeletonAdapter(boolean isOuvidoria) {
        this.isOuvidoria = isOuvidoria;
    }

    @Override
    public SkeletonViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(isOuvidoria ? R.layout.item_skeleton_ouvidoria : R.layout.item_skeleton_ordem,
                        parent, false);
        return new SkeletonViewHolder(view);
    }

    @Override
    public void onBindViewHolder(SkeletonViewHolder holder, int position) {
        holder.shimmerFrameLayout.startShimmer();
    }

    @Override
    public int getItemCount() {
        return isOuvidoria ? 2 : 5; // 2 items para ouvidoria, 5 para ordens
    }

    class SkeletonViewHolder extends RecyclerView.ViewHolder {
        ShimmerFrameLayout shimmerFrameLayout;

        public SkeletonViewHolder(View itemView) {
            super(itemView);
            shimmerFrameLayout = (ShimmerFrameLayout) itemView;
        }
    }
} 