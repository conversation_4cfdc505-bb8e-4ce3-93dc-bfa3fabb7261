package com.baramaia.hercules;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.adapters.SkeletonAdapter;
import com.baramaia.hercules.models.OrdemEt;
import com.baramaia.hercules.models.Ouvidoria;
import com.baramaia.hercules.network.ApiCalls;
import com.baramaia.hercules.network.ApiDados;
import com.baramaia.hercules.utilities.SharedPrefHercules;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class IniciarVisitaActivity extends AppCompatActivity {

    private RecyclerView rvOuvidorias;
    private RecyclerView rvOrdensEt;
    private List<Ouvidoria> ouvidoriaList;
    private List<OrdemEt> ordensList;
    ApiDados apiDados = new ApiDados();
    private ApiCalls apiCalls;
    private SkeletonAdapter skeletonAdapter;
    private boolean isLoadingOuvidorias = true;
    private boolean isLoadingOrdens = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_iniciar_visitas);
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        // Inicializando os RecyclerViews
        rvOuvidorias = findViewById(R.id.recyclerOuvidorias);
        rvOrdensEt = findViewById(R.id.rvOrdensEt);

        // Configurar sempre como horizontal
        LinearLayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false);
        LinearLayoutManager layoutManagerOrd = new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false);

        rvOuvidorias.setLayoutManager(layoutManager);
        rvOrdensEt.setLayoutManager(layoutManagerOrd);

        // Habilitar o scroll horizontal
        rvOuvidorias.setHasFixedSize(true);
        rvOrdensEt.setHasFixedSize(true);

        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(IniciarVisitaActivity.this);
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);
        int userid = Integer.parseInt(sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_ID));

        Intent intent = getIntent();
        if (intent.hasExtra("msg")) {
            String mensagem = intent.getStringExtra("msg");
            Toast.makeText(IniciarVisitaActivity.this, mensagem, Toast.LENGTH_SHORT).show();
        }


        // Configurar skeleton adapters
        SkeletonAdapter skeletonOuvidorias = new SkeletonAdapter(true);
        SkeletonAdapter skeletonOrdens = new SkeletonAdapter(false);
        rvOuvidorias.setAdapter(skeletonOuvidorias);
        rvOrdensEt.setAdapter(skeletonOrdens);

        buscaOuvidorias(userid, token);
        buscaOrdens(userid, token);
    }

    private void buscaOrdens(int userid, String token) {
        isLoadingOrdens = true;
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        Call<List<OrdemEt>> callouvid = apiCalls.getOrdens(userid, token);
        callouvid.enqueue(new Callback<List<OrdemEt>>() {
            @Override
            public void onResponse(Call<List<OrdemEt>> callouvid, Response<List<OrdemEt>> response) {
                isLoadingOrdens = false;
                if (response.isSuccessful() && response.body() != null) {
                    ordensList = response.body();

                    if (ordensList.isEmpty()) {
                        Toast.makeText(IniciarVisitaActivity.this, "Não há ordens!", Toast.LENGTH_SHORT).show();
                    } else {
                        // Configurar o RecyclerView
                        LinearLayoutManager layoutManager = new LinearLayoutManager(
                                IniciarVisitaActivity.this,
                                LinearLayoutManager.HORIZONTAL,
                                false
                        );

                        // Configurar para mostrar todos os itens
                        layoutManager.setInitialPrefetchItemCount(ordensList.size());
                        rvOrdensEt.setLayoutManager(layoutManager);

                        // Adicionar espaçamento entre itens
                        if (rvOrdensEt.getItemDecorationCount() == 0) {
                            rvOrdensEt.addItemDecoration(new RecyclerView.ItemDecoration() {
                                @Override
                                public void getItemOffsets(@NonNull android.graphics.Rect outRect, @NonNull View view,
                                                           @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                                    if (parent.getChildAdapterPosition(view) < parent.getAdapter().getItemCount() - 1) {
                                        outRect.right = getResources().getDimensionPixelSize(R.dimen._6pxh);
                                    }
                                    outRect.left = getResources().getDimensionPixelSize(R.dimen._6pxh);
                                }
                            });
                        }

                        OrdensEtAdapter ordensEtAdapter = new OrdensEtAdapter(ordensList);
                        rvOrdensEt.setAdapter(ordensEtAdapter);

                        // Forçar o RecyclerView a medir e layoutar novamente
                        rvOrdensEt.post(new Runnable() {
                            @Override
                            public void run() {
                                rvOrdensEt.scrollToPosition(0);
                                rvOrdensEt.invalidate();
                            }
                        });
                    }
                } else {
                    Toast.makeText(IniciarVisitaActivity.this, "Não há ordens de serviço designadas", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<OrdemEt>> call, Throwable t) {
                isLoadingOrdens = false;
                Toast.makeText(IniciarVisitaActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void buscaOuvidorias(int userid, String token) {
        isLoadingOuvidorias = true;
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        Call<List<Ouvidoria>> callouvid = apiCalls.getOuvidorias(userid, token);
        callouvid.enqueue(new Callback<List<Ouvidoria>>() {
            @Override
            public void onResponse(Call<List<Ouvidoria>> callouvid, Response<List<Ouvidoria>> response) {
                isLoadingOuvidorias = false;
                if (response.isSuccessful() && response.body() != null) {
                    ouvidoriaList = response.body();

                    if (ouvidoriaList.isEmpty()) {
                        Toast.makeText(IniciarVisitaActivity.this, "Não há ouvidorias!", Toast.LENGTH_SHORT).show();
                    } else {
                        // Configurar o RecyclerView
                        LinearLayoutManager layoutManager = new LinearLayoutManager(
                                IniciarVisitaActivity.this,
                                LinearLayoutManager.HORIZONTAL,
                                false
                        );

                        // Configurar para mostrar todos os itens
                        layoutManager.setInitialPrefetchItemCount(ouvidoriaList.size());
                        rvOuvidorias.setLayoutManager(layoutManager);

                        // Adicionar espaçamento entre itens
                        if (rvOuvidorias.getItemDecorationCount() == 0) {
                            rvOuvidorias.addItemDecoration(new RecyclerView.ItemDecoration() {
                                @Override
                                public void getItemOffsets(@NonNull android.graphics.Rect outRect, @NonNull View view,
                                                           @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                                    if (parent.getChildAdapterPosition(view) < parent.getAdapter().getItemCount() - 1) {
                                        outRect.right = getResources().getDimensionPixelSize(R.dimen._6pxh);
                                    }
                                    outRect.left = getResources().getDimensionPixelSize(R.dimen._6pxh);
                                }
                            });
                        }

                        RowOuvidoriasAdapter adapter = new RowOuvidoriasAdapter(ouvidoriaList);
                        rvOuvidorias.setAdapter(adapter);

                        // Forçar o RecyclerView a medir e layoutar novamente
                        rvOuvidorias.post(new Runnable() {
                            @Override
                            public void run() {
                                rvOuvidorias.scrollToPosition(0);
                                rvOuvidorias.invalidate();
                            }
                        });

                        // Definindo o listener de clique
                        adapter.setOnItemClickListener((ouvId, ouvOrdId) -> {
                            Intent intent = new Intent(IniciarVisitaActivity.this, OuvidoriaDetalheActivity.class);
                            intent.putExtra("OUVIDORIA_ID", ouvId);
                            intent.putExtra("ORDEM_ID", ouvOrdId);  // Enviando o ouv_ordId
                            startActivity(intent);
                        });
                    }
                } else {
                    //Toast.makeText(IniciarVisitaActivity.this, "Erro encontrar ouvidoria", Toast.LENGTH_SHORT).show();
                    Toast.makeText(IniciarVisitaActivity.this, "Não há ouvidoria", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<Ouvidoria>> call, Throwable t) {
                isLoadingOuvidorias = false;
                Toast.makeText(IniciarVisitaActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
    }
    @Override
    public void onBackPressed() {
        // Adicione a lógica desejada antes de sair da Activity
        //Toast.makeText(this, "Botão voltar pressionado!", Toast.LENGTH_SHORT).show();
        super.onBackPressed();
        Intent intent = new Intent(IniciarVisitaActivity.this, LoginActivity.class);
        startActivity(intent);
        // Se quiser impedir que a Activity feche, basta não chamar o super:
        // super.onBackPressed(); // Não chame este método se quiser bloquear a ação padrão
    }
}