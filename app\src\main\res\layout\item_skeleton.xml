<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="150dp"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <View
            android:layout_width="180dp"
            android:layout_height="20dp"
            android:background="@color/shimmer_color" />

        <View
            android:layout_width="240dp"
            android:layout_height="15dp"
            android:layout_marginTop="16dp"
            android:background="@color/shimmer_color" />

        <View
            android:layout_width="200dp"
            android:layout_height="15dp"
            android:layout_marginTop="8dp"
            android:background="@color/shimmer_color" />
    </LinearLayout>

</androidx.cardview.widget.CardView> 