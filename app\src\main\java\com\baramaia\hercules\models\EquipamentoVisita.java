package com.baramaia.hercules.models;

import androidx.annotation.Nullable;
import androidx.room.Embedded;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity
public class EquipamentoVisita {
    @PrimaryKey(autoGenerate = true)
    int id;
    @Nullable
    String eqv_descricao;
    @Nullable
    String eqv_nmedidor;
    @Nullable
    String eqv_nserie;
    @Nullable
    String eqv_ndisplay;

    int visitaId;
    boolean instalado;

    @Embedded
    @Nullable
    Foto foto;

    // Construtor padrão que inicializa campos com valores seguros
    public EquipamentoVisita() {
        this.eqv_descricao = "";
        this.eqv_nmedidor = "";
        this.eqv_nserie = "";
        this.eqv_ndisplay = "";
        this.visitaId = 0;
        this.instalado = false;
        this.foto = null;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getEqv_descricao() {
        return eqv_descricao;
    }

    public void setEqv_descricao(String eqv_descricao) {
        this.eqv_descricao = eqv_descricao;
    }

    public String getEqv_nmedidor() {
        return eqv_nmedidor;
    }

    public void setEqv_nmedidor(String eqv_nmedidor) {
        this.eqv_nmedidor = eqv_nmedidor;
    }

    public String getEqv_nserie() {
        return eqv_nserie;
    }

    public void setEqv_nserie(String eqv_nserie) {
        this.eqv_nserie = eqv_nserie;
    }

    public String getEqv_ndisplay() {
        return eqv_ndisplay;
    }

    public void setEqv_ndisplay(String eqv_ndisplay) {
        this.eqv_ndisplay = eqv_ndisplay;
    }
    public Foto getFoto() {
        return foto;
    }

    public void setFoto(Foto foto) {
        this.foto = foto;
    }

    public int getVisitaId() {
        return visitaId;
    }

    public void setVisitaId(int visitaId) {
        this.visitaId = visitaId;
    }

    public boolean isInstalado() {
        return instalado;
    }

    public void setInstalado(boolean instalado) {
        this.instalado = instalado;
    }
}