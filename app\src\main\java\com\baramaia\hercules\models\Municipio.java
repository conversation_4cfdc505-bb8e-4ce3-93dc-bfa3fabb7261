package com.baramaia.hercules.models;

import com.google.gson.annotations.SerializedName;

public class Municipio {
    @SerializedName("id")
    private int id;

    @SerializedName("mun_descricao")
    private String nome;

    public Municipio() {
    }

    public Municipio(int id, String nome) {
        this.id = id;
        this.nome = nome;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    @Override
    public String toString() {
        return nome;
    }
}
