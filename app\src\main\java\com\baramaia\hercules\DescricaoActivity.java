package com.baramaia.hercules;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.drawable.ColorDrawable;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.provider.MediaStore;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.DAO.VisitaDao;
import com.baramaia.hercules.Data.AppDatabase;
import com.baramaia.hercules.models.EquipamentoVisita;
import com.baramaia.hercules.models.Foto;
import com.baramaia.hercules.models.OrdemServico;
import com.baramaia.hercules.models.Ouvidoria;
import com.baramaia.hercules.models.Visita;
import com.baramaia.hercules.models.VisitaApi;
import com.baramaia.hercules.network.ApiCalls;
import com.baramaia.hercules.network.ApiDados;
import com.baramaia.hercules.utilities.EquipamentosTemporarios;
import com.baramaia.hercules.utilities.SharedPrefHercules;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.button.MaterialButton;
import com.github.rygelouv.androidloadingbuttonlib.LoadingButton;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class DescricaoActivity extends AppCompatActivity {
    ApiDados apiDados = new ApiDados();
    private ApiCalls apiCalls;
    private LoadingButton btnFinalizar;
    private MaterialButton btnFachada;
    private MaterialButton btnMedidor;
    private ImageView imageArrowleftOne;
    private static final int REQUEST_IMAGE_CAPTURE = 1;
    private static final int REQUEST_PICK_IMAGE = 2;
    private int currentPhotoIndex = -1;
    private ImageView checkFachada;
    private ImageView checkMedidor;
    private ImageView checkServico;
    private ImageView thumbnailFachada;
    private ImageView thumbnailMedidor;
    private List<Bitmap> thumbnails = new ArrayList<>();
    private List<Bitmap> fullImages = new ArrayList<>();
    private List<Bitmap> fotosAdicionais = new ArrayList<>();
    private FotosAdicionaisAdapter fotosAdicionaisAdapter;
    private RecyclerView recyclerFotosAdicionais;
    private TextView txtContadorFotos;
    private MaterialButton btnAdicionarFoto;
    private Dialog fullscreenDialog;
    private TextView txtEssaatividade;
    private EditText edtDescricao;

    // Checkboxes para problemas
    private CheckBox cbCadastroIncorreto;
    private CheckBox cbCadastroProvisorio;
    private CheckBox cbCsErroComunicacao;
    private CheckBox cbFalhaContator;
    private CheckBox cbMedidorAntigo;
    private CheckBox cbMedidorNaoExibido;
    private CheckBox cbMedidorNaoResponde;
    private CheckBox cbModuloDivergente;
    private CheckBox cbPosicaoIncorreta;
    private CheckBox cbTliAnilhamento;
    private CheckBox cbTliDesligado;
    private CheckBox cbTliAguarde;
    private CheckBox cbOutros;
    private Spinner spinner;
    private boolean[] photosTaken = new boolean[2]; // Apenas Fachada e Medidor
    private List<OrdemServico> ordensList = new ArrayList<>();
    private List<Integer> selecionados;
    private List<Foto> fotosList = new ArrayList<>();

    private int ordId;
    private int et;
    private int avulsoId;
    Date dataagora = new Date();
    private FusedLocationProviderClient fusedLocationClient;
    private String coordenadas;
    private RecyclerView row_ns;
    private DescricaoNsAdapter nsAdapter;
    private LinearLayout containerNsSection;
    private TextView txtContadorNs;
    private TextView txtEmptyNs;
    private Uri photoUri;
    private File photoFile;

    private CardView cardFachada;
    private CardView cardMedidor;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_descricao_da_atividade);

        carregarVisitaSalva();
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);

        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(DescricaoActivity.this);
        String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);

        Intent intent = getIntent();
        if (intent != null) {
            Gson gson = new Gson();

            // Recebe os dados enviados

            ordId = intent.getIntExtra("ordemId", 0);
            et = intent.getIntExtra("et", 0);
            avulsoId = intent.getIntExtra("avulsoId", 0);

            // Converte os JSON de volta para as listas de objetos
            Type listType = new TypeToken<List<EquipamentoVisita>>() {
            }.getType();

        } else {
            Log.e("DescricaoActivity", "Intent nula ou extras não encontrados!");
        }

        thumbnails.add(null); // Fachada
        thumbnails.add(null); // Medidor

        fullImages.add(null); // Fachada
        fullImages.add(null); // Medidor

        inicializarComponentes();
        configurarListeners();
        buscarNs(token);


        // Limpe os dados temporários após usar
        EquipamentosTemporarios.getInstance().clear();
    }

    private void inicializarComponentes() {
        btnFinalizar = findViewById(R.id.btnFinalizar);
        btnFachada = findViewById(R.id.btnFachada);
        btnMedidor = findViewById(R.id.btnMedidor);
        imageArrowleftOne = findViewById(R.id.imageArrowleftOne);
        edtDescricao = findViewById(R.id.edtDescricao);
        txtEssaatividade = findViewById(R.id.txtEssaatividade);

        // Inicializar checkboxes
        cbCadastroIncorreto = findViewById(R.id.cbCadastroIncorreto);
        cbCadastroProvisorio = findViewById(R.id.cbCadastroProvisorio);
        cbCsErroComunicacao = findViewById(R.id.cbCsErroComunicacao);
        cbFalhaContator = findViewById(R.id.cbFalhaContator);
        cbMedidorAntigo = findViewById(R.id.cbMedidorAntigo);
        cbMedidorNaoExibido = findViewById(R.id.cbMedidorNaoExibido);
        cbMedidorNaoResponde = findViewById(R.id.cbMedidorNaoResponde);
        cbModuloDivergente = findViewById(R.id.cbModuloDivergente);
        cbPosicaoIncorreta = findViewById(R.id.cbPosicaoIncorreta);
        cbTliAnilhamento = findViewById(R.id.cbTliAnilhamento);
        cbTliDesligado = findViewById(R.id.cbTliDesligado);
        cbTliAguarde = findViewById(R.id.cbTliAguarde);
        cbOutros = findViewById(R.id.cbOutros);

        // Inicializar os CardViews
        cardFachada = findViewById(R.id.cardFachada);
        cardMedidor = findViewById(R.id.cardMedidor);

        // Inicializar componentes da seção NS
        containerNsSection = findViewById(R.id.containerNsSection);
        txtContadorNs = findViewById(R.id.txtContadorNs);
        txtEmptyNs = findViewById(R.id.txtEmptyNs);
        row_ns = findViewById(R.id.row_ns);
        
        if (row_ns != null) {
            row_ns.setLayoutManager(new LinearLayoutManager(this));
            // Remove qualquer ItemDecoration existente
            while (row_ns.getItemDecorationCount() > 0) {
                row_ns.removeItemDecorationAt(0);
            }
            // Adiciona um espaçamento melhorado entre os itens
            row_ns.addItemDecoration(new RecyclerView.ItemDecoration() {
                @Override
                public void getItemOffsets(@NonNull android.graphics.Rect outRect, @NonNull View view,
                                           @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                    outRect.top = 4;
                    outRect.bottom = 4;
                }
            });
        }

        // Inicializar as miniaturas
        thumbnailFachada = findViewById(R.id.thumbnailFachada);
        thumbnailMedidor = findViewById(R.id.thumbnailMedidor);
        
        // Inicializar componentes do novo sistema de fotos
        txtContadorFotos = findViewById(R.id.txtContadorFotos);
        btnAdicionarFoto = findViewById(R.id.btnAdicionarFoto);
        recyclerFotosAdicionais = findViewById(R.id.recyclerFotosAdicionais);
        
        // Configurar RecyclerView das fotos adicionais
        configurarRecyclerFotosAdicionais();

        // Configurar click listeners para as miniaturas
        if (thumbnailFachada != null) {
            thumbnailFachada.setOnClickListener(v -> showFullscreenImage(0));
        }
        if (thumbnailMedidor != null) {
            thumbnailMedidor.setOnClickListener(v -> showFullscreenImage(1));
        }
    }

    private void configurarListeners() {
        btnFachada.setOnClickListener(v -> {
            currentPhotoIndex = 0;
            showImagePickerDialog();
        });

        btnMedidor.setOnClickListener(v -> {
            currentPhotoIndex = 1;
            showImagePickerDialog();
        });

        btnAdicionarFoto.setOnClickListener(v -> {
            currentPhotoIndex = -1; // Indica foto adicional
            showImagePickerDialog();
        });

        btnFinalizar.setOnClickListener(View -> {
            if (validarDescricaoAtividades() && validarFotosObrigatorias()) {
                mostrarLoadingBotao();
                fusedLocationClient = LocationServices.getFusedLocationProviderClient(this);
                getGPS();
            }
        });
        imageArrowleftOne.setOnClickListener(View -> {
//            Intent intentRegistro = new Intent(getApplicationContext(), RegistroSimActivity.class);
//            startActivity(intentRegistro);
            enviarTela();
        });

        // Configurar listeners dos checkboxes
        configurarCheckboxListeners();

        // Configurar o ScrollView customizado
        configurarScrollViewProblemas();
    }

    private void configurarCheckboxListeners() {
        cbCadastroIncorreto.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("N° de fachada # estava invertido com N° #");
            } else {
                removerTextoDescricao("N° de fachada # estava invertido com N° #");
            }
        });

        cbCadastroProvisorio.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("Cadastro realizado no n° de fachada # na UC #");
            } else {
                removerTextoDescricao("Cadastro realizado no n° de fachada # na UC #");
            }
        });

        cbCsErroComunicacao.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("CS configurado para ID #\nEquipamento trocado: #");
            } else {
                removerTextoDescricao("CS configurado para ID #\nEquipamento trocado: #");
            }
        });

        cbFalhaContator.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("Módulo apresentou falha por motivo de #\nEquipe solucionou o problema com procedimento de #");
            } else {
                removerTextoDescricao("Módulo apresentou falha por motivo de #\nEquipe solucionou o problema com procedimento de #");
            }
        });

        cbMedidorAntigo.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("Medidor antigo # retirado\nMódulo # instalado");
            } else {
                removerTextoDescricao("Medidor antigo # retirado\nMódulo # instalado");
            }
        });

        cbMedidorNaoExibido.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("Módulo apresentou falha por motivo de #\nEquipe solucionou o problema com procedimento de #");
            } else {
                removerTextoDescricao("Módulo apresentou falha por motivo de #\nEquipe solucionou o problema com procedimento de #");
            }
        });

        cbMedidorNaoResponde.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("Módulo apresentou falha por motivo de #\nEquipe solucionou o problema com procedimento de #");
            } else {
                removerTextoDescricao("Módulo apresentou falha por motivo de #\nEquipe solucionou o problema com procedimento de #");
            }
        });

        cbModuloDivergente.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("Módulo em campo era #");
            } else {
                removerTextoDescricao("Módulo em campo era #");
            }
        });

        cbPosicaoIncorreta.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("Modulo # cadastrado no CS # e pos. # estava invertido com módulo # do CS # e pos. #");
            } else {
                removerTextoDescricao("Modulo # cadastrado no CS # e pos. # estava invertido com módulo # do CS # e pos. #");
            }
        });

        cbTliAnilhamento.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("Anilhamento em campo era #");
            } else {
                removerTextoDescricao("Anilhamento em campo era #");
            }
        });

        cbTliDesligado.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("Módulo apresentou falha por motivo de #\nEquipe solucionou o problema com procedimento de #");
            } else {
                removerTextoDescricao("Módulo apresentou falha por motivo de #\nEquipe solucionou o problema com procedimento de #");
            }
        });

        cbTliAguarde.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                adicionarTextoDescricao("Módulo apresentou falha por motivo de #\nEquipe solucionou o problema com procedimento de #");
            } else {
                removerTextoDescricao("Módulo apresentou falha por motivo de #\nEquipe solucionou o problema com procedimento de #");
            }
        });

        cbOutros.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // Para "OUTROS" não adiciona texto automático, deixa o usuário digitar
        });
    }

    private void configurarScrollViewProblemas() {
        CustomScrollView scrollViewProblemas = findViewById(R.id.scrollViewProblemas);
        if (scrollViewProblemas != null) {
            // Força o ScrollView a ter prioridade sobre o NestedScrollView pai
            scrollViewProblemas.setOnTouchListener((v, event) -> {
                // Impede que o pai intercepte os eventos de touch
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            });
        }
    }
    
    private boolean validarDescricaoAtividades() {
        String descricao = edtDescricao.getText().toString().trim();
        
        // Verifica se o campo está vazio
        if (descricao.isEmpty()) {
            mostrarErroDescricao("Por favor, descreva as atividades realizadas.", "Campo obrigatório");
            return false;
        }
        
        // Verifica se ainda contém cerquilhas (#) - indicando que o usuário não preencheu os placeholders
        if (descricao.contains("#")) {
            // Conta quantas cerquilhas ainda existem
            int countCerquilhas = descricao.length() - descricao.replace("#", "").length();
            String mensagem = "Ainda existem " + countCerquilhas + " campo(s) '#' para preencher com informações específicas.";
            
            mostrarErroDescricao(mensagem, "Substitua todos os '#' pelas informações corretas");
            return false;
        }
        
        // Verifica se a descrição é muito curta (menos de 10 caracteres)
        if (descricao.length() < 10) {
            mostrarErroDescricao("A descrição deve ter pelo menos 10 caracteres.", "Descrição muito curta");
            return false;
        }
        
        // Limpa qualquer erro anterior
        edtDescricao.setError(null);
        return true;
    }
    
    private boolean validarFotosObrigatorias() {
        // Verifica se as 2 fotos obrigatórias foram tiradas
        if (!photosTaken[0] || !photosTaken[1]) {
            String mensagem = "É necessário tirar as fotos obrigatórias:\n";
            if (!photosTaken[0]) mensagem += "• Fachada\n";
            if (!photosTaken[1]) mensagem += "• Medidor\n";
            
            Toast.makeText(this, mensagem.trim(), Toast.LENGTH_LONG).show();
            return false;
        }
        return true;
    }
    
    private void mostrarErroDescricao(String mensagemToast, String mensagemCampo) {
        Toast.makeText(this, mensagemToast, Toast.LENGTH_LONG).show();
        edtDescricao.requestFocus();
        edtDescricao.setError(mensagemCampo);
        
        // Scroll para o campo de descrição para garantir que seja visível
        edtDescricao.post(() -> {
            edtDescricao.requestFocus();
            // Seleciona o texto para facilitar a edição
            edtDescricao.selectAll();
        });
    }
    
    private void atualizarVisibilidadeSecaoNs(int quantidadeItens) {
        if (containerNsSection == null) return;
        
        if (quantidadeItens > 0) {
            // Mostra a seção com dados
            containerNsSection.setVisibility(View.VISIBLE);
            row_ns.setVisibility(View.VISIBLE);
            txtEmptyNs.setVisibility(View.GONE);
            
            // Atualiza o contador
            txtContadorNs.setText(String.valueOf(quantidadeItens));
            
        } else {
            // Oculta a seção quando não há dados
            containerNsSection.setVisibility(View.GONE);
        }
    }
    
    private void configurarRecyclerFotosAdicionais() {
        if (recyclerFotosAdicionais != null) {
            androidx.recyclerview.widget.LinearLayoutManager layoutManager = 
                new androidx.recyclerview.widget.LinearLayoutManager(this, androidx.recyclerview.widget.LinearLayoutManager.HORIZONTAL, false);
            recyclerFotosAdicionais.setLayoutManager(layoutManager);
            
            fotosAdicionaisAdapter = new FotosAdicionaisAdapter(fotosAdicionais, position -> {
                // Remove a foto da lista
                fotosAdicionais.remove(position);
                fotosAdicionaisAdapter.notifyItemRemoved(position);
                fotosAdicionaisAdapter.notifyItemRangeChanged(position, fotosAdicionais.size());
                
                // Atualiza a visibilidade e contador
                atualizarContadorFotos();
                atualizarVisibilidadeFotosAdicionais();
            });
            
            recyclerFotosAdicionais.setAdapter(fotosAdicionaisAdapter);
        }
    }
    
    private void atualizarContadorFotos() {
        if (txtContadorFotos != null) {
            int fotosObrigatorias = 0;
            if (photosTaken[0]) fotosObrigatorias++; // Fachada
            if (photosTaken[1]) fotosObrigatorias++; // Medidor
            
            int totalFotos = fotosObrigatorias + fotosAdicionais.size();
            txtContadorFotos.setText(fotosObrigatorias + "/2" + (fotosAdicionais.size() > 0 ? " (+" + fotosAdicionais.size() + ")" : ""));
        }
    }
    
    private void atualizarVisibilidadeFotosAdicionais() {
        if (recyclerFotosAdicionais != null) {
            recyclerFotosAdicionais.setVisibility(fotosAdicionais.isEmpty() ? View.GONE : View.VISIBLE);
        }
    }
    
    private void processarFotoObrigatoria(Foto foto) {
        if (currentPhotoIndex >= 0 && currentPhotoIndex < photosTaken.length) {
            photosTaken[currentPhotoIndex] = true;
            
            // Atualiza a miniatura correspondente
            if (currentPhotoIndex == 0 && cardFachada != null && thumbnailFachada != null) {
                // Fachada
                cardFachada.setVisibility(View.VISIBLE);
                // Aqui você pode definir a imagem da miniatura se necessário
                Toast.makeText(this, "Foto da fachada adicionada", Toast.LENGTH_SHORT).show();
            } else if (currentPhotoIndex == 1 && cardMedidor != null && thumbnailMedidor != null) {
                // Medidor
                cardMedidor.setVisibility(View.VISIBLE);
                // Aqui você pode definir a imagem da miniatura se necessário
                Toast.makeText(this, "Foto do medidor adicionada", Toast.LENGTH_SHORT).show();
            }
            
            atualizarContadorFotos();
        }
    }


    private void adicionarTextoDescricao(String texto) {
        String textoAtual = edtDescricao.getText().toString().trim();
        if (!textoAtual.contains(texto)) {
            if (!textoAtual.isEmpty()) {
                textoAtual += "\n\n";
            }
            textoAtual += texto;
            edtDescricao.setText(textoAtual);
        }
    }

    private void removerTextoDescricao(String texto) {
        String textoAtual = edtDescricao.getText().toString();
        textoAtual = textoAtual.replace(texto, "");
        textoAtual = textoAtual.replace("\n\n\n", "\n\n"); // Remove quebras extras
        textoAtual = textoAtual.trim();
        edtDescricao.setText(textoAtual);
    }

    private void enviarTela() {
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(DescricaoActivity.this);
        String exOuvidoria = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA);
        if (exOuvidoria.equals("1")) {
            Intent intentVisita = new Intent(getApplicationContext(), IniciarVisitaActivity.class);
            intentVisita.putExtra("ordemId", ordId);
            intentVisita.putExtra("et", et);

            startActivity(intentVisita);

        } else {
            Intent intentVisita = new Intent(getApplicationContext(), IniciarVisitaOrdemVerticalActivity.class);
            intentVisita.putExtra("ordemId", ordId);
            intentVisita.putExtra("et", et);

            startActivity(intentVisita);
        }
    }

    private void showImagePickerDialog() {
        BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(this);
        View dialogView = getLayoutInflater().inflate(R.layout.dialog_escolha_foto, null);

        MaterialButton btnCamera = dialogView.findViewById(R.id.btnCamera);
        MaterialButton btnGaleria = dialogView.findViewById(R.id.btnGaleria);

        btnCamera.setOnClickListener(v -> {
            dispatchTakePictureIntent();
            bottomSheetDialog.dismiss();
        });


        btnGaleria.setOnClickListener(v -> {
            openGallery();
            bottomSheetDialog.dismiss();

        });

        bottomSheetDialog.setContentView(dialogView);
        bottomSheetDialog.show();
    }

    private void openGallery() {
        Intent intent = new Intent(Intent.ACTION_PICK);
        intent.setType("image/*");
        startActivityForResult(intent, REQUEST_PICK_IMAGE);
    }

    private void dispatchTakePictureIntent() {
        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        if (takePictureIntent.resolveActivity(getPackageManager()) != null) {
            try {
                photoFile = createImageFile(); // Criar arquivo para salvar a foto
                if (photoFile != null) {
                    photoUri = FileProvider.getUriForFile(this, "com.baramaia.hercules", photoFile);
                    takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                    startActivityForResult(takePictureIntent, REQUEST_IMAGE_CAPTURE);
                }
            } catch (IOException ex) {
                ex.printStackTrace();
                Toast.makeText(this, "Erro ao criar arquivo de imagem", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private File createImageFile() throws IOException {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        return File.createTempFile(imageFileName, ".jpg", storageDir);
    }

    // Função para corrigir a orientação da imagem
    private Bitmap getCorrectlyOrientedImage(String photoPath) {
        try {
            // Primeiro decodifica apenas os limites da imagem
            BitmapFactory.Options bounds = new BitmapFactory.Options();
            bounds.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(photoPath, bounds);

            // Calcula o tamanho ideal para carregar a imagem
            BitmapFactory.Options opts = new BitmapFactory.Options();
            int photoWidth = bounds.outWidth;
            int photoHeight = bounds.outHeight;
            int scaleFactor = Math.min(photoWidth / 1024, photoHeight / 1024);
            if (scaleFactor > 0) {
                opts.inSampleSize = scaleFactor;
            }

            // Carrega a imagem com o tamanho reduzido
            Bitmap bitmap = BitmapFactory.decodeFile(photoPath, opts);

            // Obtém a orientação da imagem dos metadados EXIF
            android.media.ExifInterface exif = new android.media.ExifInterface(photoPath);
            int orientation = exif.getAttributeInt(
                    android.media.ExifInterface.TAG_ORIENTATION,
                    android.media.ExifInterface.ORIENTATION_UNDEFINED);

            // Rotaciona a imagem se necessário
            Bitmap rotatedBitmap = null;
            switch (orientation) {
                case android.media.ExifInterface.ORIENTATION_ROTATE_90:
                    rotatedBitmap = rotateImage(bitmap, 90);
                    break;
                case android.media.ExifInterface.ORIENTATION_ROTATE_180:
                    rotatedBitmap = rotateImage(bitmap, 180);
                    break;
                case android.media.ExifInterface.ORIENTATION_ROTATE_270:
                    rotatedBitmap = rotateImage(bitmap, 270);
                    break;
                default:
                    rotatedBitmap = bitmap;
            }

            return rotatedBitmap;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private Bitmap rotateImage(Bitmap source, float angle) {
        Matrix matrix = new Matrix();
        matrix.postRotate(angle);
        return Bitmap.createBitmap(source, 0, 0, source.getWidth(), source.getHeight(),
                matrix, true);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == RESULT_OK) {
            Foto foto = null;

            if (requestCode == REQUEST_IMAGE_CAPTURE && photoFile != null) {
                // Corrige a orientação da imagem antes de processar
                Bitmap imageBitmap = getCorrectlyOrientedImage(photoFile.getAbsolutePath());
                if (imageBitmap != null) {
                    foto = processarFoto(imageBitmap);
                }
            } else if (requestCode == REQUEST_PICK_IMAGE && data != null && data.getData() != null) {
                try {
                    Uri imageUri = data.getData();
                    String[] filePathColumn = {MediaStore.Images.Media.DATA};
                    Cursor cursor = getContentResolver().query(imageUri, filePathColumn, null, null, null);
                    if (cursor != null) {
                        cursor.moveToFirst();
                        int columnIndex = cursor.getColumnIndex(filePathColumn[0]);
                        String picturePath = cursor.getString(columnIndex);
                        cursor.close();

                        // Corrige a orientação da imagem da galeria também
                        Bitmap selectedImage = getCorrectlyOrientedImage(picturePath);
                        if (selectedImage != null) {
                            foto = processarFoto(selectedImage);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (foto != null) {
                if (currentPhotoIndex == -1) {
                    // Foto adicional
                    Bitmap imageBitmap = null;
                    if (requestCode == REQUEST_IMAGE_CAPTURE && photoFile != null) {
                        imageBitmap = getCorrectlyOrientedImage(photoFile.getAbsolutePath());
                    } else if (requestCode == REQUEST_PICK_IMAGE && data != null && data.getData() != null) {
                        try {
                            Uri imageUri = data.getData();
                            String[] filePathColumn = {MediaStore.Images.Media.DATA};
                            Cursor cursor = getContentResolver().query(imageUri, filePathColumn, null, null, null);
                            if (cursor != null) {
                                cursor.moveToFirst();
                                int columnIndex = cursor.getColumnIndex(filePathColumn[0]);
                                String picturePath = cursor.getString(columnIndex);
                                cursor.close();
                                imageBitmap = getCorrectlyOrientedImage(picturePath);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    
                    if (imageBitmap != null) {
                        fotosAdicionais.add(imageBitmap);
                        fotosAdicionaisAdapter.notifyItemInserted(fotosAdicionais.size() - 1);
                        atualizarVisibilidadeFotosAdicionais();
                        atualizarContadorFotos();
                        Toast.makeText(this, "Foto adicional adicionada", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    // Foto obrigatória (Fachada ou Medidor)
                    processarFotoObrigatoria(foto);
                }
                
                fotosList.add(foto);
                salvarVisitaNoBanco();
            }
        } else {
            Toast.makeText(this, "Seleção de foto cancelada", Toast.LENGTH_SHORT).show();
        }
    }

    private void salvarVisitaNoBanco() {
        String problemas = obterProblemasSelecionados();
        String descricao = edtDescricao.getText().toString().trim();
        
        // Log para debug
        Log.d("VisitaDebug", "=== COLETANDO DADOS PARA SALVAR ===");
        Log.d("VisitaDebug", "Descrição coletada: '" + descricao + "'");
        Log.d("VisitaDebug", "Problemas coletados: '" + problemas + "'");
        Log.d("VisitaDebug", "=== FIM COLETA DADOS ===");

        new Thread(() -> {
            VisitaDao visitaDao = AppDatabase.getDatabase(getApplicationContext()).visitaDao();

            // Recupera a visita salva ou cria uma nova
            Visita visita = visitaDao.getVisitaByOrdId(ordId);
            if (visita == null) {
                visita = new Visita();
                visita.setVis_ordId(ordId);
                visita.setVis_data(pegaDataHora(dataagora) != null ? pegaDataHora(dataagora) : "");
                visita.setVis_efetivado(1);
                visita.setVis_eqpVisitaId(0);
                // Garantir que todos os campos String não sejam null
                visita.setVis_descricao("");
                visita.setVis_problemas("");
                visita.setVis_observacao("");
                visita.setVis_gps("");
                // Garantir que todas as listas não sejam null (agora são strings JSON)
                visita.setFotos(new ArrayList<>());
                visita.setEqpInstalados(new ArrayList<>());
                visita.setEqpRetirados(new ArrayList<>());
                visita.setOrdens(new ArrayList<>());
                visita.setMotivo(null); // Este pode ser null
                visita.setVis_status(false);
                visita.setAvulsoId(0);
            }
            if (avulsoId > 0) {
                visita.setAvulsoId(avulsoId);
            }

            // Null safety checks - use empty string instead of null for required fields
            visita.setVis_descricao(descricao.isEmpty() ? "" : descricao);
            visita.setVis_problemas(problemas.isEmpty() ? "" : problemas);
            visita.setVis_gps(coordenadas != null ? coordenadas : "");
            visita.setFotos(fotosList != null ? fotosList : new ArrayList<>());
            visita.setOrdens(selecionados != null ? selecionados : new ArrayList<>());

            // Verificar e corrigir campos null nas fotos
            if (visita.getFotos() != null) {
                for (int i = 0; i < visita.getFotos().size(); i++) {
                    Foto foto = visita.getFotos().get(i);
                    if (foto != null) {
                        // Garantir que nenhum campo da foto seja null
                        if (foto.getFoto_nome() == null) foto.setFoto_nome("");
                        if (foto.getFoto_extensao() == null) foto.setFoto_extensao("");
                        if (foto.getFoto_dado() == null) foto.setFoto_dado("");
                        if (foto.getFoto_tipo() == null) foto.setFoto_tipo("");
                        if (foto.getFoto_dado_base64() == null) foto.setFoto_dado_base64("");

                        Log.d("VisitaDebug", "Foto " + i + ": nome='" + foto.getFoto_nome() + "', extensao='" + foto.getFoto_extensao() + "', tipo='" + foto.getFoto_tipo() + "'");
                    }
                }
            }

            // Log detalhado para debug
            Log.d("VisitaDebug", "=== DADOS DA VISITA ANTES DE INSERIR ===");
            Log.d("VisitaDebug", "ID: " + visita.getId());
            Log.d("VisitaDebug", "vis_ordId: " + visita.getVis_ordId());
            Log.d("VisitaDebug", "vis_data: '" + visita.getVis_data() + "'");
            Log.d("VisitaDebug", "vis_efetivado: " + visita.getVis_efetivado());
            Log.d("VisitaDebug", "vis_eqpVisitaId: " + visita.getVis_eqpVisitaId());
            Log.d("VisitaDebug", "vis_descricao: '" + visita.getVis_descricao() + "'");
            Log.d("VisitaDebug", "vis_problemas: '" + visita.getVis_problemas() + "'");
            Log.d("VisitaDebug", "vis_observacao: '" + visita.getVis_observacao() + "'");
            Log.d("VisitaDebug", "vis_gps: '" + visita.getVis_gps() + "'");
            Log.d("VisitaDebug", "fotos: " + (visita.getFotos() != null ? visita.getFotos().size() + " items" : "null"));
            Log.d("VisitaDebug", "eqpInstalados: " + (visita.getEqpInstalados() != null ? visita.getEqpInstalados().size() + " items" : "null"));
            Log.d("VisitaDebug", "eqpRetirados: " + (visita.getEqpRetirados() != null ? visita.getEqpRetirados().size() + " items" : "null"));
            Log.d("VisitaDebug", "motivo: " + (visita.getMotivo() != null ? "not null" : "null"));
            Log.d("VisitaDebug", "ordens: " + (visita.getOrdens() != null ? visita.getOrdens().size() + " items" : "null"));
            Log.d("VisitaDebug", "vis_status: " + visita.getVis_status());
            Log.d("VisitaDebug", "avulsoId: " + visita.getAvulsoId());
            Log.d("VisitaDebug", "=== FIM DOS DADOS ===");

            try {
                visitaDao.insert(visita); // Salva ou atualiza no banco
                Log.d("VisitaDebug", "Inserção realizada com sucesso!");
            } catch (Exception e) {
                Log.e("VisitaDebug", "Erro ao inserir visita: " + e.getMessage(), e);
                runOnUiThread(() -> Toast.makeText(DescricaoActivity.this, "Erro ao salvar: " + e.getMessage(), Toast.LENGTH_LONG).show());
            }

        }).start();
    }

    private void carregarVisitaSalva() {
        new Thread(() -> {
            VisitaDao visitaDao = AppDatabase.getDatabase(getApplicationContext()).visitaDao();
            Visita visita = visitaDao.getVisitaByOrdId(ordId);

            if (visita != null) {
                runOnUiThread(() -> {
                    // Carregar problemas selecionados (se houver lógica para restaurar checkboxes)
                    edtDescricao.setText(visita.getVis_descricao() != null ? visita.getVis_descricao() : "");
                    if (visita.getFotos() != null) {
                        fotosList.addAll(visita.getFotos());
                        if (!visita.getFotos().isEmpty()) {
                            // Atualiza o photosTaken com base na quantidade de fotos já tiradas
                            for (int i = 0; i < visita.getFotos().size() && i < photosTaken.length; i++) {
                                photosTaken[i] = true;
                            }
                            updatePhotoStatus();
                        }
                    }
                });
            }
        }).start();
    }

    private Foto processarFoto(Bitmap imageBitmap) {
        if (currentPhotoIndex >= 0 && currentPhotoIndex < photosTaken.length) {
            photosTaken[currentPhotoIndex] = true;

            // Salvar a imagem original para visualização em tela cheia
            fullImages.set(currentPhotoIndex, imageBitmap.copy(imageBitmap.getConfig(), true));

            // Criar e salvar a miniatura
            Bitmap thumbnail = Bitmap.createScaledBitmap(imageBitmap, 200, 200, true);
            thumbnails.set(currentPhotoIndex, thumbnail);

            // Atualizar a miniatura na tela
            runOnUiThread(() -> {
                ImageView targetThumbnail = null;
                switch (currentPhotoIndex) {
                    case 0:
                        targetThumbnail = thumbnailFachada;
                        break;
                    case 1:
                        targetThumbnail = thumbnailMedidor;
                        break;
                }

                if (targetThumbnail != null) {
                    targetThumbnail.setImageBitmap(thumbnail);
                    updatePhotoStatus();
                }
            });
        }

        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        imageBitmap.compress(Bitmap.CompressFormat.JPEG, 50, stream);
        byte[] byteArray = stream.toByteArray();
        String base64Image = Base64.encodeToString(byteArray, Base64.DEFAULT);

        Foto foto = new Foto();
        foto.setFoto_nome("Foto_" + ordId + "_" + pegaDataHora(dataagora));
        foto.setFoto_extensao("PNG");
        foto.setFoto_dado(""); // Garantir que não seja null
        foto.setFoto_dado_base64(base64Image != null ? base64Image : "");
        foto.setFoto_tipo("Visita");

        // Log para verificar os dados da foto
        Log.d("VisitaDebug", "Foto criada: nome='" + foto.getFoto_nome() + "', extensao='" + foto.getFoto_extensao() + "', tipo='" + foto.getFoto_tipo() + "', dado='" + foto.getFoto_dado() + "'");

        return foto;
    }

    private String pegaDataHora(Date dt) {
        String dataHora;

        //===============PREECHE DATA INICIAL==============================
        SimpleDateFormat formataData = new SimpleDateFormat("dd/MM/yyyy");
        Date data = new Date();
        if (dt != null) {
            data = dt;
        }
        String dataFormatada = formataData.format(data);
        //===============PREECHE DATA INICIAL==============================

        //===============PREECHE HORA INICIAL==============================
        String sHora;
        String sMinuto;
        if (data.getMinutes() < 10)
            sMinuto = "0" + String.valueOf(data.getMinutes());
        else
            sMinuto = String.valueOf(data.getMinutes());

        if (data.getHours() < 10)
            sHora = "0" + String.valueOf(data.getHours());
        else
            sHora = String.valueOf(data.getHours());
        String sHour = sHora + ":" + sMinuto;
        //===============PREECHE HORA INICIAL==============================

        dataHora = dataFormatada + " " + sHour;

        //================================================
        String dd = dataHora.substring(0, 2);
        String MM = dataHora.substring(3, 5);
        String aaaa = dataHora.substring(6, 10);
        String HH = dataHora.substring(11, 13);
        String mm = dataHora.substring(14, 16);
        dataHora = aaaa + "-" + MM + "-" + dd + "T" + HH + ":" + mm + ":00";
        //================================================

        return dataHora;
    }

    @SuppressLint("MissingPermission")
    private void getGPS() {
        fusedLocationClient.getLastLocation().addOnCompleteListener(new OnCompleteListener<Location>() {
            @Override
            public void onComplete(@NonNull Task<Location> task) {
                Location location = task.getResult();

                if (location != null) {
                    try {
                        Geocoder geocoder = new Geocoder(DescricaoActivity.this, Locale.getDefault());
                        List<Address> addresses = geocoder.getFromLocation(location.getLatitude(), location.getLongitude(), 1);

                        coordenadas = addresses.get(0).getLatitude() + "," + addresses.get(0).getLongitude();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                } else {
                    coordenadas = "Localização não encontrada";
                }

                // Após obter as coordenadas, chama salvarVisitaNaoEfetivada()
                enviarVisitaEfetivada();
            }
        });
    }

    public void testarObjetoComoJson(Visita v) {
        // Exemplo de objeto que você deseja enviar


        // Converter objeto para JSON usando Gson
        Gson gson = new GsonBuilder().serializeNulls().create();
        String jsonString = gson.toJson(v);

        // Exibir ou usar a string JSON
        System.out.println("JSON gerado: " + jsonString);

        // Agora você pode enviar este JSON para sua Call se necessário
    }

    private void updatePhotoStatus() {
        // Atualizar visibilidade dos CardViews
        if (cardFachada != null) {
            cardFachada.setVisibility(photosTaken[0] ? View.VISIBLE : View.GONE);
        }
        if (cardMedidor != null) {
            cardMedidor.setVisibility(photosTaken[1] ? View.VISIBLE : View.GONE);
        }

    }

    private void mostrarLoadingBotao() {
        btnFinalizar.startLoading("Validando...");
    }
    
    private void esconderLoadingBotao() {
        btnFinalizar.stopLoading(getString(R.string.msg_proximo));
    }
    
    private void resetButtonState() {
        btnFinalizar.stopLoading(getString(R.string.msg_proximo));
    }

    private boolean verificarProblemasSelecionados() {
        return (cbCadastroIncorreto != null && cbCadastroIncorreto.isChecked()) ||
                (cbCadastroProvisorio != null && cbCadastroProvisorio.isChecked()) ||
                (cbCsErroComunicacao != null && cbCsErroComunicacao.isChecked()) ||
                (cbFalhaContator != null && cbFalhaContator.isChecked()) ||
                (cbMedidorAntigo != null && cbMedidorAntigo.isChecked()) ||
                (cbMedidorNaoExibido != null && cbMedidorNaoExibido.isChecked()) ||
                (cbMedidorNaoResponde != null && cbMedidorNaoResponde.isChecked()) ||
                (cbModuloDivergente != null && cbModuloDivergente.isChecked()) ||
                (cbPosicaoIncorreta != null && cbPosicaoIncorreta.isChecked()) ||
                (cbTliAnilhamento != null && cbTliAnilhamento.isChecked()) ||
                (cbTliDesligado != null && cbTliDesligado.isChecked()) ||
                (cbTliAguarde != null && cbTliAguarde.isChecked()) ||
                (cbOutros != null && cbOutros.isChecked());
    }

    private String obterProblemasSelecionados() {
        StringBuilder problemas = new StringBuilder();
        
        // Log para debug
        Log.d("VisitaDebug", "=== VERIFICANDO CHECKBOXES ===");
        
        if (cbCadastroIncorreto != null && cbCadastroIncorreto.isChecked()) {
            problemas.append("CADASTRO INCORRETO OU INVERTIDO; ");
            Log.d("VisitaDebug", "Checkbox selecionado: CADASTRO INCORRETO OU INVERTIDO");
        }
        if (cbCadastroProvisorio != null && cbCadastroProvisorio.isChecked()) {
            problemas.append("CADASTRO PROVISÓRIO; ");
            Log.d("VisitaDebug", "Checkbox selecionado: CADASTRO PROVISÓRIO");
        }
        if (cbCsErroComunicacao != null && cbCsErroComunicacao.isChecked()) {
            problemas.append("CS ERRO DE COMUNICAÇÃO OU CONFIGURAÇÃO; ");
            Log.d("VisitaDebug", "Checkbox selecionado: CS ERRO DE COMUNICAÇÃO OU CONFIGURAÇÃO");
        }
        if (cbFalhaContator != null && cbFalhaContator.isChecked()) {
            problemas.append("FALHA DE ACIONAMENTO DO CONTATOR; ");
            Log.d("VisitaDebug", "Checkbox selecionado: FALHA DE ACIONAMENTO DO CONTATOR");
        }
        if (cbMedidorAntigo != null && cbMedidorAntigo.isChecked()) {
            problemas.append("MEDIDOR ANTIGO NO LOCAL; ");
            Log.d("VisitaDebug", "Checkbox selecionado: MEDIDOR ANTIGO NO LOCAL");
        }
        if (cbMedidorNaoExibido != null && cbMedidorNaoExibido.isChecked()) {
            problemas.append("MEDIDOR NÃO É EXIBIDO NA COLETORA OU APP; ");
            Log.d("VisitaDebug", "Checkbox selecionado: MEDIDOR NÃO É EXIBIDO NA COLETORA OU APP");
        }
        if (cbMedidorNaoResponde != null && cbMedidorNaoResponde.isChecked()) {
            problemas.append("MEDIDOR NÃO RESPONDE COMANDO DE CORTE; ");
            Log.d("VisitaDebug", "Checkbox selecionado: MEDIDOR NÃO RESPONDE COMANDO DE CORTE");
        }
        if (cbModuloDivergente != null && cbModuloDivergente.isChecked()) {
            problemas.append("MÓDULO OU DISPLAY DIVERGENTE DO ÁRTERI; ");
            Log.d("VisitaDebug", "Checkbox selecionado: MÓDULO OU DISPLAY DIVERGENTE DO ÁRTERI");
        }
        if (cbPosicaoIncorreta != null && cbPosicaoIncorreta.isChecked()) {
            problemas.append("POSIÇÃO INCORRETA OU INVERTIDA; ");
            Log.d("VisitaDebug", "Checkbox selecionado: POSIÇÃO INCORRETA OU INVERTIDA");
        }
        if (cbTliAnilhamento != null && cbTliAnilhamento.isChecked()) {
            problemas.append("TLI ANILHAMENTO INCORRETO OU AUSENTE; ");
            Log.d("VisitaDebug", "Checkbox selecionado: TLI ANILHAMENTO INCORRETO OU AUSENTE");
        }
        if (cbTliDesligado != null && cbTliDesligado.isChecked()) {
            problemas.append("TLI DESLIGADO; ");
            Log.d("VisitaDebug", "Checkbox selecionado: TLI DESLIGADO");
        }
        if (cbTliAguarde != null && cbTliAguarde.isChecked()) {
            problemas.append("TLI EXIBINDO AGUARDE; ");
            Log.d("VisitaDebug", "Checkbox selecionado: TLI EXIBINDO AGUARDE");
        }
        if (cbOutros != null && cbOutros.isChecked()) {
            problemas.append("OUTROS; ");
            Log.d("VisitaDebug", "Checkbox selecionado: OUTROS");
        }
        
        String resultado = problemas.toString().trim();
        Log.d("VisitaDebug", "Problemas coletados: '" + resultado + "'");
        Log.d("VisitaDebug", "=== FIM VERIFICAÇÃO CHECKBOXES ===");
        
        return resultado;
    }

    // Função para exibir a imagem em tela cheia
    private void showFullscreenImage(int index) {
        if (fullImages.get(index) == null) return;

        Dialog fullscreenDialog = new Dialog(this, R.style.DialogTheme);
        fullscreenDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        fullscreenDialog.setContentView(R.layout.dialog_fullscreen_image);

        Window window = fullscreenDialog.getWindow();
        if (window != null) {
            window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }

        // Configurar clique fora da imagem para fechar
        View dialogView = fullscreenDialog.findViewById(android.R.id.content);
        if (dialogView != null) {
            dialogView.setOnClickListener(v -> fullscreenDialog.dismiss());
        }

        ImageView fullscreenImageView = fullscreenDialog.findViewById(R.id.fullscreenImageView);
        if (fullscreenImageView != null) {
            fullscreenImageView.setImageBitmap(fullImages.get(index));
            fullscreenImageView.setScaleType(ImageView.ScaleType.FIT_CENTER);
            // Impedir que o clique na imagem feche o diálogo
            fullscreenImageView.setOnClickListener(null);
        }

        fullscreenDialog.show();
    }

    private void buscaOuvidorias(String token, String userId) {
        apiDados = new ApiDados();
        apiCalls = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiCalls.class);
        SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(getApplicationContext());
        int userid = Integer.parseInt(userId);

        Call<List<Ouvidoria>> callouvid = apiCalls.getOuvidorias(userid, token);
        callouvid.enqueue(new Callback<List<Ouvidoria>>() {
            @Override
            public void onResponse(Call<List<Ouvidoria>> callouvid, Response<List<Ouvidoria>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA, "1");
                } else {
                    sharedPrefHercules.salvarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA, "0");
                }
            }

            @Override
            public void onFailure(Call<List<Ouvidoria>> call, Throwable t) {
                Toast.makeText(DescricaoActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void buscarNs(String token) {
        Call<List<OrdemServico>> callns = apiCalls.getOrdensEt(et, ordId, token);
        callns.enqueue(new Callback<List<OrdemServico>>() {
            @Override
            public void onResponse(Call<List<OrdemServico>> callns, Response<List<OrdemServico>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ordensList.clear();
                    ordensList.addAll(response.body());
                    if (ordensList != null && !ordensList.isEmpty()) {
                        // Atualiza a seção com os dados
                        atualizarVisibilidadeSecaoNs(ordensList.size());
                        
                        if (row_ns != null) {
                            if (nsAdapter == null) {
                                nsAdapter = new DescricaoNsAdapter(ordensList);
                                row_ns.setAdapter(nsAdapter);
                            } else {
                                nsAdapter.notifyDataSetChanged();
                            }
                        }
                    } else {
                        // Oculta a seção quando não há dados
                        atualizarVisibilidadeSecaoNs(0);
                    }
                } else {
                    // Oculta a seção quando há erro na requisição
                    atualizarVisibilidadeSecaoNs(0);
                }
            }

            @Override
            public void onFailure(Call<List<OrdemServico>> callns, Throwable t) {
                Toast.makeText(DescricaoActivity.this, "Erro de conexão", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void enviarVisitaEfetivada() {
        // Inicia o loading - desabilita o botão, muda o texto e adiciona spinner
        runOnUiThread(() -> btnFinalizar.startLoading("Enviando..."));

        new Thread(() -> {
            String descricao = edtDescricao.getText().toString().trim();
            boolean algumProblemaSelecionado = verificarProblemasSelecionados();

            if (!algumProblemaSelecionado) {
                runOnUiThread(() -> {
                    resetButtonState();
                    Toast.makeText(this, "Por favor, selecione pelo menos um problema encontrado", Toast.LENGTH_SHORT).show();
                });
                return;
            }
            if (descricao.isEmpty()) {
                runOnUiThread(() -> {
                    resetButtonState();
                    Toast.makeText(this, "Por favor, descreva a atividade feita", Toast.LENGTH_SHORT).show();
                });
                return;
            }
            // Verifica se as 2 fotos obrigatórias foram tiradas
            if (!photosTaken[0] || !photosTaken[1]) {
                runOnUiThread(() -> {
                    resetButtonState();
                    String mensagem = "É necessário tirar as fotos obrigatórias: ";
                    if (!photosTaken[0]) mensagem += "Fachada ";
                    if (!photosTaken[1]) mensagem += "Medidor ";
                    Toast.makeText(this, mensagem, Toast.LENGTH_SHORT).show();
                });
                return;
            }

            VisitaDao visitaDao = AppDatabase.getDatabase(getApplicationContext()).visitaDao();
            Visita visita = visitaDao.getVisitaByOrdId(ordId);

            if (visita == null) {
                Log.e("VisitaDebug", "ERRO: Visita não encontrada no banco para ordId: " + ordId);
                runOnUiThread(() -> {
                    resetButtonState();
                    Toast.makeText(this, "Erro: Nenhuma visita salva! Tire as fotos primeiro.", Toast.LENGTH_LONG).show();
                });
                return;
            }

            Log.d("VisitaDebug", "Visita encontrada no banco, preparando para envio...");
            visita.setId(0);
            visita.setVis_eqpVisitaId(1);
            visita.setVis_observacao("");
            visita.setEqpInstalados(new ArrayList<>());
            visita.setEqpRetirados(new ArrayList<>());
            visita.setMotivo(null);
            visita.setOrdens(new ArrayList<>());
            visita.setAvulsoId(avulsoId);
            visita.setVis_gps(coordenadas != null ? coordenadas : "");
            if (nsAdapter != null) {
                selecionados = nsAdapter.getSelectedIds();
                visita.setOrdens(selecionados != null ? selecionados : new ArrayList<>());
            }

            if (visita.getVis_problemas().isEmpty()) {
                runOnUiThread(() -> Toast.makeText(this, "Por favor, digite os problemas encontrados", Toast.LENGTH_SHORT).show());
            }
            if (visita.getVis_descricao().isEmpty()) {
                runOnUiThread(() -> Toast.makeText(this, "Por favor, descreva a atividade feita", Toast.LENGTH_SHORT).show());
            }
            // Verifica se tem pelo menos as 2 fotos obrigatórias
            if (visita.getFotos().size() < 2) {
                runOnUiThread(() -> Toast.makeText(this, "Tire pelo menos as 2 fotos obrigatórias (Fachada e Medidor) antes de finalizar", Toast.LENGTH_SHORT).show());
                return;
            }

            // Envio para API
            SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(DescricaoActivity.this);
            String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);
            String exOuvidoria = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_OUVIDORIA);

            // Converter Visita (banco) para VisitaApi (API)
            VisitaApi visitaApi = new VisitaApi(visita);

            // Log da visita antes de enviar para API
            Log.d("VisitaDebug", "=== ENVIANDO VISITA PARA API ===");
            Log.d("VisitaDebug", "Visita ID: " + visitaApi.getId());
            Log.d("VisitaDebug", "Visita ordId: " + visitaApi.getVis_ordId());
            Log.d("VisitaDebug", "Visita problemas: '" + visitaApi.getVis_problemas() + "'");
            Log.d("VisitaDebug", "Visita descricao: '" + visitaApi.getVis_descricao() + "'");
            Log.d("VisitaDebug", "Visita fotos: " + visitaApi.getFotos().size() + " items");
            Log.d("VisitaDebug", "Visita eqpInstalados: " + visitaApi.getEqpInstalados().size() + " items");
            Log.d("VisitaDebug", "Visita eqpRetirados: " + visitaApi.getEqpRetirados().size() + " items");
            Log.d("VisitaDebug", "Token: " + (token != null ? "presente" : "null"));

            Call<VisitaApi> callvisita = apiCalls.postVisita(visitaApi, token);
            callvisita.enqueue(new Callback<VisitaApi>() {
                @Override
                public void onResponse(Call<VisitaApi> callvisita, Response<VisitaApi> response) {
                    Log.d("VisitaDebug", "Resposta da API - Código: " + response.code());
                    if (response.errorBody() != null) {
                        try {
                            String errorBody = response.errorBody().string();
                            Log.e("VisitaDebug", "Erro da API: " + errorBody);
                        } catch (Exception e) {
                            Log.e("VisitaDebug", "Erro ao ler errorBody: " + e.getMessage());
                        }
                    }

                    if (response.isSuccessful() && response.body() != null) {
                        runOnUiThread(() -> {
                            // Aguarda 1 segundo antes de prosseguir
                            new Handler().postDelayed(() -> {
                                resetButtonState();
                                Toast.makeText(DescricaoActivity.this, "Favor inclua os equipamentos caso exista!", Toast.LENGTH_SHORT).show();

                                Intent intentVisita = new Intent(getApplicationContext(), RegistroSimActivity.class);
                                intentVisita.putExtra("visita_id", response.body().getId());
                                intentVisita.putExtra("ordemId", ordId);
                                intentVisita.putExtra("avulsoId", avulsoId);
                                intentVisita.putExtra("et", et);

                                SharedPrefHercules sharedPrefHercules = new SharedPrefHercules(DescricaoActivity.this);
                                String token = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_TOKEN);
                                String userId = sharedPrefHercules.carregarPrefHercules(sharedPrefHercules.USUARIO_ID);
                                buscaOuvidorias(token, userId);

                                startActivity(intentVisita);
                                finish();
                            }, 1000);
                        });
                    } else {
                        Log.e("VisitaDebug", "API retornou erro - Código: " + response.code() + ", Mensagem: " + response.message());
                        runOnUiThread(() -> {
                            resetButtonState();
                            Toast.makeText(DescricaoActivity.this, "Erro API (" + response.code() + "): " + response.message(), Toast.LENGTH_LONG).show();
                        });
                    }
                }

                @Override
                public void onFailure(Call<VisitaApi> callvisita, Throwable t) {
                    Log.e("VisitaDebug", "Falha na conexão com API: " + t.getMessage(), t);
                    runOnUiThread(() -> {
                        resetButtonState();
                        Toast.makeText(DescricaoActivity.this, "Erro na conexão: " + t.getMessage(), Toast.LENGTH_LONG).show();
                    });
                }
            });
        }).start();
    }


    @Override
    protected void onResume() {
        super.onResume();
        // Reverte o botão ao estado inicial quando voltar para a tela
        if (btnFinalizar != null) {
            resetButtonState();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Liberar memória das miniaturas e imagens em tela cheia
        for (int i = 0; i < thumbnails.size(); i++) {
            if (thumbnails.get(i) != null && !thumbnails.get(i).isRecycled()) {
                thumbnails.get(i).recycle();
            }
            if (fullImages.get(i) != null && !fullImages.get(i).isRecycled()) {
                fullImages.get(i).recycle();
            }
        }
        thumbnails.clear();
        fullImages.clear();
    }
}

