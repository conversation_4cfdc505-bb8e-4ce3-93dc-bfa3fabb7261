<?xml version="1.0" encoding="UTF-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    style="@style/groupStyleblue_gray_50"
    android:orientation="vertical">

    <!-- Header <PERSON><PERSON><PERSON> padr<PERSON> da Nova Ordem -->
    <LinearLayout
        android:id="@+id/headerLayout"
        style="@style/groupStyleblue_gray_50"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingTop="16dp"
        android:paddingEnd="16dp"
        android:paddingBottom="16dp">

        <TextView
            android:id="@+id/txtIniciarvisitas"
            style="@style/txtRobotoregular22"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="@string/lbl_iniciar_visitas"
            tools:text="@string/lbl_iniciar_visitas" />
    </LinearLayout>

    <!-- Lista de Itens com padding inferior -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:paddingBottom="80dp"
        android:clipToPadding="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Seção Ouvidorias seguindo padrão -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp">

                <TextView
                    android:id="@+id/txtOuvidorias"
                    style="@style/txtRobotoregular20"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/lbl_ouvidorias" />

                <!-- Badge contador (opcional) -->
                <TextView
                    android:id="@+id/badgeOuvidorias"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/rectangle_bg_blue_gray_800_radius_10"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:visibility="gone"
                    tools:text="3"
                    tools:visibility="visible" />
            </LinearLayout>

            <!-- Lista de Ouvidorias com padding padrão -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerOuvidorias"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingBottom="8dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/row_ouvidorias" />

            <!-- Divisor sutil entre seções -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="8dp"
                android:background="@color/gray_300_01"
                android:alpha="0.5" />

            <!-- Seção Ordens de Serviço seguindo padrão -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp">

                <TextView
                    android:id="@+id/txtOrdensdeservi"
                    style="@style/txtRobotoregular20"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/msg_ordens_de_servi_o" />

                <!-- Badge contador (opcional) -->
                <TextView
                    android:id="@+id/badgeOrdens"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/rectangle_bg_blue_gray_800_radius_10"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:visibility="gone"
                    tools:text="5"
                    tools:visibility="visible" />
            </LinearLayout>

            <!-- Lista de Ordens com padding padrão -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvOrdensEt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingBottom="16dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</LinearLayout>

<!-- FAB para Nova Ordem seguindo padrão -->
<com.google.android.material.floatingactionbutton.FloatingActionButton
    android:id="@+id/fabAddNew"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom|end"
    android:layout_margin="16dp"
    android:contentDescription="Nova Ordem"
    android:src="@drawable/plusicon"
    app:backgroundTint="@color/custom_border_color"
    app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>