<?xml version="1.0" encoding="UTF-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/linearColumnordensDeServi"
        style="@style/groupStyleblue_gray_50"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Header seguin<PERSON> padr<PERSON> da Nova Ordem -->
        <LinearLayout
            android:id="@+id/headerLayout"
            style="@style/groupStyleblue_gray_50"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp">

            <TextView
                android:id="@+id/txtIniciarvisitas"
                style="@style/txtRobotoregular22"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="@string/lbl_iniciar_visitas"
                tools:text="@string/lbl_iniciar_visitas" />
        </LinearLayout>

        <!-- Seção de Ordens de Serviço com espaçamento padrão -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp">

            <TextView
                android:id="@+id/txtOrdensdeservi"
                style="@style/txtRobotoregular20"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="@string/msg_ordens_de_servi_o"
                tools:text="@string/msg_ordens_de_servi_o" />
        </LinearLayout>

        <ScrollView
            android:id="@+id/scrollViewScrollview"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:clipToPadding="false"
            android:fillViewport="true"
            android:gravity="center_horizontal"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvOrdensEt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:fadeScrollbars="false"
                    android:orientation="vertical"
                    android:scrollbarStyle="outsideOverlay"
                    android:scrollbars="vertical"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvSkeletonOrdens"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:fadeScrollbars="false"
                    android:orientation="vertical"
                    android:scrollbarStyle="outsideOverlay"
                    android:scrollbars="vertical"
                    android:visibility="visible"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
            </FrameLayout>
        </ScrollView>

        <FrameLayout
            android:id="@+id/frameBottombar"
            style="@style/groupStylewhite_A700"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal">


        </FrameLayout>
    </LinearLayout>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddNew"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/plusicon"
        app:backgroundTint="@color/custom_border_color"
        app:tint="@android:color/white" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>