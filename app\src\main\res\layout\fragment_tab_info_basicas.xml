<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/blue_gray_50">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Seção: Motivo da Visita -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/rectangle_bg_white_a700_radius_12"
            android:elevation="2dp"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                style="@style/txtRobotoregular18"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="Motivo da Visita"
                android:textColor="@color/blue_gray_800" />

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Selecione o motivo"
                app:boxStrokeColor="@color/custom_border_color"
                app:hintTextColor="@color/custom_border_color"
                app:endIconTint="@color/custom_border_color">

                <com.google.android.material.textfield.MaterialAutoCompleteTextView
                    android:id="@+id/spinnerMotivo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textColor="@color/blue_gray_800" />
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>

        <!-- Seção: Localização -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/rectangle_bg_white_a700_radius_12"
            android:elevation="2dp"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                style="@style/txtRobotoregular18"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="Localização"
                android:textColor="@color/blue_gray_800" />

            <!-- Campo Município -->
            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Município"
                app:boxStrokeColor="@color/custom_border_color"
                app:hintTextColor="@color/custom_border_color"
                app:endIconTint="@color/custom_border_color">

                <com.google.android.material.textfield.MaterialAutoCompleteTextView
                    android:id="@+id/spinnerMunicipio"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textColor="@color/blue_gray_800" />
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>

        <!-- Seção: Informações Técnicas -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/rectangle_bg_white_a700_radius_12"
            android:elevation="2dp"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                style="@style/txtRobotoregular18"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="Informações Técnicas"
                android:textColor="@color/blue_gray_800" />

            <!-- Linha 1: CP e CS -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:hint="CP"
                    app:boxStrokeColor="@color/custom_border_color"
                    app:hintTextColor="@color/custom_border_color">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etCp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="4"
                        android:textColor="@color/blue_gray_800" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:hint="CS"
                    app:boxStrokeColor="@color/custom_border_color"
                    app:hintTextColor="@color/custom_border_color">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etCs"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="3"
                        android:textColor="@color/blue_gray_800" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

            <!-- Linha 2: Módulo e Display -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:hint="Módulo *"
                    app:boxStrokeColor="@color/custom_border_color"
                    app:hintTextColor="@color/custom_border_color">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etModulo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="8"
                        android:textColor="@color/blue_gray_800" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:hint="Display *"
                    app:boxStrokeColor="@color/custom_border_color"
                    app:hintTextColor="@color/custom_border_color">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etDisplay"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="7"
                        android:textColor="@color/blue_gray_800" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
            
            <!-- Aviso sobre duplicação -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="⚠️ A combinação Display + CP + CS + Módulo deve ser única"
                android:textColor="@color/orange_600"
                android:textSize="12sp"
                android:gravity="center"
                android:background="@drawable/rectangle_bg_orange_50_radius_8"
                android:padding="8dp" />
        </LinearLayout>

        <!-- Seção: Empreiteira -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:background="@drawable/rectangle_bg_white_a700_radius_12"
            android:elevation="2dp"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                style="@style/txtRobotoregular18"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="Empreiteira"
                android:textColor="@color/blue_gray_800" />

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Selecione a empreiteira"
                app:boxStrokeColor="@color/custom_border_color"
                app:hintTextColor="@color/custom_border_color"
                app:endIconTint="@color/custom_border_color">

                <com.google.android.material.textfield.MaterialAutoCompleteTextView
                    android:id="@+id/spinnerEmpreiteira"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textColor="@color/blue_gray_800" />
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>

        <!-- Botão Próximo -->
        <com.github.rygelouv.androidloadingbuttonlib.LoadingButton
            android:id="@+id/btnProximoTab1"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginTop="16dp"
            android:padding="0dp"
            custom:background="@drawable/button_blue_gray_rounded"
            custom:progressColor="@android:color/white"
            custom:text="Próximo"
            custom:textColor="@android:color/white"
            custom:textSize="16sp" />
    </LinearLayout>
</ScrollView>