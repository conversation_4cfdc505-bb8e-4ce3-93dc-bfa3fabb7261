package com.baramaia.hercules.models;

import com.google.gson.annotations.SerializedName;

public class Nucleo {
    @SerializedName("id")
    private int id;

    @SerializedName("nuc_descricao")
    private String nome;

    @SerializedName("munId")
    private int municipioId;

    public Nucleo() {
    }

    public Nucleo(int id, String nome, int municipioId) {
        this.id = id;
        this.nome = nome;
        this.municipioId = municipioId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public int getMunicipioId() {
        return municipioId;
    }

    public void setMunicipioId(int municipioId) {
        this.municipioId = municipioId;
    }

    @Override
    public String toString() {
        return nome;
    }
}
