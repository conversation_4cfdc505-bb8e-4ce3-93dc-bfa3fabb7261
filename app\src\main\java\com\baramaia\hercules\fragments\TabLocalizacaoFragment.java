package com.baramaia.hercules.fragments;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import com.github.rygelouv.androidloadingbuttonlib.LoadingButton;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.baramaia.hercules.NovaOrdemActivity;
import com.baramaia.hercules.R;
import com.baramaia.hercules.adapters.CustomSpinnerAdapter;
import com.baramaia.hercules.models.Municipio;
import com.baramaia.hercules.models.Nucleo;
import com.baramaia.hercules.network.ApiCalls;
import com.baramaia.hercules.network.ApiDados;
import com.baramaia.hercules.network.LoggingInterceptor;
import com.baramaia.hercules.utilities.SharedPrefHercules;
import com.baramaia.hercules.utilities.ToastUtils;

import java.util.ArrayList;
import java.util.List;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class TabLocalizacaoFragment extends Fragment {

    private static final String TAG = "TabLocalizacaoFragment";

    private EditText etLogradouro, etNumero, etComplemento;
    private Spinner spinnerMunicipio, spinnerNucleo;
    private TextView tvNucleoLabel;
    private Button btnAnteriorTab2;
    private LoadingButton btnProximoTab2;

    // Listas para armazenar os dados dos spinners
    private List<Municipio> listaMunicipios = new ArrayList<>();
    private List<Nucleo> listaNucleos = new ArrayList<>();

    // Adapters para os spinners
    private CustomSpinnerAdapter<Municipio> municipioAdapter;
    private CustomSpinnerAdapter<Nucleo> nucleoAdapter;

    // API
    private ApiCalls apiCalls;
    private SharedPrefHercules sharedPrefHercules;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_tab_localizacao, container, false);

        etLogradouro = view.findViewById(R.id.etLogradouro);
        etNumero = view.findViewById(R.id.etNumero);
        etComplemento = view.findViewById(R.id.etComplemento);
        spinnerMunicipio = view.findViewById(R.id.spinnerMunicipio);
        spinnerNucleo = view.findViewById(R.id.spinnerNucleo);
        tvNucleoLabel = view.findViewById(R.id.tvNucleoLabel);
        btnAnteriorTab2 = view.findViewById(R.id.btnAnteriorTab2);
        btnProximoTab2 = view.findViewById(R.id.btnProximoTab2);

        // Inicializar SharedPreferences e API
        sharedPrefHercules = new SharedPrefHercules(requireContext());
        ApiDados apiDados = new ApiDados();

        // Configurar OkHttpClient com o interceptor de log
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(new LoggingInterceptor())
                .build();

        // Configurar Retrofit com o cliente OkHttp
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(apiDados.BASEURL)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        // Criar a interface ApiCalls
        apiCalls = retrofit.create(ApiCalls.class);

        // Configurar adapters vazios inicialmente
        municipioAdapter = new CustomSpinnerAdapter<>(requireContext(),
                android.R.layout.simple_spinner_item, listaMunicipios);
        municipioAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerMunicipio.setAdapter(municipioAdapter);

        // Configurar listener para o spinner de município
        spinnerMunicipio.setOnItemSelectedListener(new android.widget.AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(android.widget.AdapterView<?> parent, View view, int position, long id) {
                // Salvar a seleção
                if (position > 0 && getActivity() instanceof NovaOrdemActivity) {
                    NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
                    Municipio municipioSelecionado = (Municipio) parent.getItemAtPosition(position);
                    activity.setMunicipio(municipioSelecionado.getNome());
                    activity.setMunicipioId(municipioSelecionado.getId());

                    // Carregar núcleos do município selecionado
                    carregarNucleosPorMunicipio(municipioSelecionado.getId());

                    // Mostrar o spinner de núcleo e seu label
                    tvNucleoLabel.setVisibility(View.VISIBLE);
                    spinnerNucleo.setVisibility(View.VISIBLE);
                } else {
                    // Esconder o spinner de núcleo e seu label se nenhum município for selecionado
                    tvNucleoLabel.setVisibility(View.GONE);
                    spinnerNucleo.setVisibility(View.GONE);

                    // Limpar a lista de núcleos
                    listaNucleos.clear();
                    Nucleo placeholderNucleo = new Nucleo();
                    placeholderNucleo.setId(0);
                    placeholderNucleo.setNome("Selecione o núcleo");
                    listaNucleos.add(placeholderNucleo);
                    nucleoAdapter.notifyDataSetChanged();

                    // Limpar a seleção de núcleo na activity
                    if (getActivity() instanceof NovaOrdemActivity) {
                        NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
                        activity.setNucleo("");
                        activity.setNucleoId(0);
                    }
                }
            }

            @Override
            public void onNothingSelected(android.widget.AdapterView<?> parent) {
                // Esconder o spinner de núcleo e seu label
                tvNucleoLabel.setVisibility(View.GONE);
                spinnerNucleo.setVisibility(View.GONE);
            }
        });

        nucleoAdapter = new CustomSpinnerAdapter<>(requireContext(),
                android.R.layout.simple_spinner_item, listaNucleos);
        nucleoAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerNucleo.setAdapter(nucleoAdapter);

        // Configurar listener para o spinner de núcleo
        spinnerNucleo.setOnItemSelectedListener(new android.widget.AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(android.widget.AdapterView<?> parent, View view, int position, long id) {
                // Salvar a seleção
                if (position > 0 && getActivity() instanceof NovaOrdemActivity) {
                    NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
                    Nucleo nucleoSelecionado = (Nucleo) parent.getItemAtPosition(position);
                    activity.setNucleo(nucleoSelecionado.getNome());
                    activity.setNucleoId(nucleoSelecionado.getId());
                }
            }

            @Override
            public void onNothingSelected(android.widget.AdapterView<?> parent) {
                // Nada a fazer
            }
        });

        // Carregar dados dos spinners da API
        carregarMunicipios();
        // Não carregamos todos os núcleos inicialmente, apenas quando um município for selecionado

        // Carregar dados salvos se existirem
        if (getActivity() instanceof NovaOrdemActivity) {
            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
            if (activity.getLogradouro() != null && !activity.getLogradouro().isEmpty()) {
                etLogradouro.setText(activity.getLogradouro());
            }
            if (activity.getNumero() != null && !activity.getNumero().isEmpty()) {
                etNumero.setText(activity.getNumero());
            }
            if (activity.getComplemento() != null && !activity.getComplemento().isEmpty()) {
                etComplemento.setText(activity.getComplemento());
            }

            // Selecionar município e núcleo se já estiverem definidos
            // Isso será feito depois que os dados forem carregados da API

            // Verificar se já existe um município selecionado
            if (activity.getMunicipioId() > 0) {
                // Mostrar o spinner de núcleo e seu label
                tvNucleoLabel.setVisibility(View.VISIBLE);
                spinnerNucleo.setVisibility(View.VISIBLE);

                // Carregar núcleos do município selecionado
                carregarNucleosPorMunicipio(activity.getMunicipioId());
            } else {
                // Esconder o spinner de núcleo e seu label
                tvNucleoLabel.setVisibility(View.GONE);
                spinnerNucleo.setVisibility(View.GONE);
            }
        }

        btnAnteriorTab2.setOnClickListener(v -> {
            salvarDados();
            if (getActivity() instanceof NovaOrdemActivity) {
                ((NovaOrdemActivity) getActivity()).setCurrentItem(0);
            }
        });

        btnProximoTab2.setOnClickListener(v -> {
            btnProximoTab2.startLoading("Carregando...");
            boolean invalido = false;
            if (etLogradouro.getText().toString().isEmpty()){
                Toast.makeText(requireContext(), "Necessário preencher o logradouro! " , Toast.LENGTH_SHORT).show();
                invalido = true;
            }
            if (etNumero.getText().toString().isEmpty()){
                Toast.makeText(requireContext(), "Necessário preencher o número! " , Toast.LENGTH_SHORT).show();
                invalido = true;
            }
            if (spinnerMunicipio.getSelectedItemPosition() == 0){
                Toast.makeText(requireContext(), "Necessário selecionar um município! " , Toast.LENGTH_SHORT).show();
                invalido = true;
            }
            if (spinnerNucleo.getSelectedItemPosition() == 0){
                Toast.makeText(requireContext(), "Necessário selecionar um núcleo! " , Toast.LENGTH_SHORT).show();
                invalido = true;
            }
            if(!invalido){
                salvarDados();
                if (getActivity() instanceof NovaOrdemActivity) {
                    ((NovaOrdemActivity) getActivity()).setCurrentItem(2);
                }
            }
            btnProximoTab2.stopLoading("Próximo");
        });

        return view;
    }


    private void salvarDados() {
        if (getActivity() instanceof NovaOrdemActivity) {
            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
            activity.setLogradouro(etLogradouro.getText().toString().trim());
            activity.setNumero(etNumero.getText().toString().trim());
            activity.setComplemento(etComplemento.getText().toString().trim());

            // Salvar município selecionado
            if (spinnerMunicipio.getSelectedItem() != null && spinnerMunicipio.getSelectedItemPosition() > 0) {
                Municipio municipioSelecionado = (Municipio) spinnerMunicipio.getSelectedItem();
                activity.setMunicipio(municipioSelecionado.getNome());
                activity.setMunicipioId(municipioSelecionado.getId());
                Log.d(TAG, "Salvando município: ID=" + municipioSelecionado.getId() + ", Nome=" + municipioSelecionado.getNome());
            }

            // Salvar núcleo selecionado
            if (spinnerNucleo.getSelectedItem() != null && spinnerNucleo.getSelectedItemPosition() > 0) {
                Nucleo nucleoSelecionado = (Nucleo) spinnerNucleo.getSelectedItem();
                activity.setNucleo(nucleoSelecionado.getNome());
                activity.setNucleoId(nucleoSelecionado.getId());
                Log.d(TAG, "Salvando núcleo: ID=" + nucleoSelecionado.getId() + ", Nome=" + nucleoSelecionado.getNome());
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        salvarDados();
    }

    /**
     * Carrega a lista de municípios da API
     */
    private void carregarMunicipios() {
        // Obter token de autenticação
        String tokenValue = sharedPrefHercules.getToken();
        Log.d(TAG, "Token original: " + tokenValue);

        // Verificar se o token já tem o prefixo "Bearer "
        String token;
        if (tokenValue != null && tokenValue.startsWith("Bearer ")) {
            token = tokenValue;
        } else {
            token = "Bearer " + tokenValue;
        }

        Log.d(TAG, "Token final: " + token);

        // Fazer a chamada à API
        Call<List<Municipio>> call = apiCalls.getMunicipios(token);
        call.enqueue(new Callback<List<Municipio>>() {
            @Override
            public void onResponse(Call<List<Municipio>> call, Response<List<Municipio>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    // Limpar lista atual
                    listaMunicipios.clear();

                    // Adicionar placeholder
                    Municipio placeholderMunicipio = new Municipio();
                    placeholderMunicipio.setId(0);
                    placeholderMunicipio.setNome("Selecione o município");
                    listaMunicipios.add(placeholderMunicipio);

                    // Adicionar os municípios recebidos
                    List<Municipio> municipiosRecebidos = response.body();
                    Log.d(TAG, "Municípios recebidos: " + municipiosRecebidos.size());

                    // Formatar os nomes dos municípios (primeira letra maiúscula, resto minúscula)
                    for (Municipio municipio : municipiosRecebidos) {
                        if (municipio.getNome() != null && !municipio.getNome().isEmpty()) {
                            String nomeFormatado = formatarNome(municipio.getNome());
                            municipio.setNome(nomeFormatado);
                        }
                        Log.d(TAG, "Município: ID=" + municipio.getId() + ", Nome=" + municipio.getNome());
                    }

                    listaMunicipios.addAll(municipiosRecebidos);

                    // Notificar o adapter sobre as mudanças
                    municipioAdapter.notifyDataSetChanged();

                    // Selecionar município se já estiver definido
                    if (getActivity() instanceof NovaOrdemActivity) {
                        NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
                        if (activity.getMunicipioId() > 0) {
                            // Procurar pelo ID do município
                            for (int i = 0; i < listaMunicipios.size(); i++) {
                                if (listaMunicipios.get(i).getId() == activity.getMunicipioId()) {
                                    spinnerMunicipio.setSelection(i, true);
                                    Log.d(TAG, "Selecionando município na posição " + i + ": " + listaMunicipios.get(i).getNome());
                                    municipioAdapter.notifyDataSetChanged();
                                    break;
                                }
                            }
                        } else if (activity.getMunicipio() != null && !activity.getMunicipio().isEmpty()) {
                            // Procurar pelo nome do município como fallback
                            for (int i = 0; i < listaMunicipios.size(); i++) {
                                if (listaMunicipios.get(i).getNome() != null &&
                                        listaMunicipios.get(i).getNome().equals(activity.getMunicipio())) {
                                    spinnerMunicipio.setSelection(i, true);
                                    Log.d(TAG, "Selecionando município na posição " + i + ": " + listaMunicipios.get(i).getNome());
                                    municipioAdapter.notifyDataSetChanged();
                                    break;
                                }
                            }
                        }
                    }

                    Log.d(TAG, "Municípios carregados: " + listaMunicipios.size());
                } else {
                    // Exibir mensagem de erro
                    String errorMessage = "Erro ao carregar municípios";
                    if (response != null) {
                        errorMessage += " (" + response.code() + ")";
                    }
                    ToastUtils.showShortToast(TabLocalizacaoFragment.this, errorMessage);
                    Log.e(TAG, errorMessage);
                }
            }

            @Override
            public void onFailure(Call<List<Municipio>> call, Throwable t) {
                // Exibir mensagem de erro
                String errorMessage = "Falha ao carregar municípios";
                if (t != null && t.getMessage() != null) {
                    errorMessage += ": " + t.getMessage();
                }
                ToastUtils.showShortToast(TabLocalizacaoFragment.this, errorMessage);
                Log.e(TAG, errorMessage, t);
            }
        });
    }

    /**
     * Carrega a lista de núcleos por município da API
     *
     * @param municipioId ID do município para filtrar os núcleos
     */
    private void carregarNucleosPorMunicipio(int municipioId) {
        Log.d(TAG, "Carregando núcleos para o município ID: " + municipioId);

        // Obter token de autenticação
        String tokenValue = sharedPrefHercules.getToken();

        // Verificar se o token já tem o prefixo "Bearer "
        String token;
        if (tokenValue != null && tokenValue.startsWith("Bearer ")) {
            token = tokenValue;
        } else {
            token = "Bearer " + tokenValue;
        }

        // Fazer a chamada à API
        try {
            Log.d(TAG, "Usando getNucleos e filtrando manualmente");
            Call<List<Nucleo>> call = apiCalls.getNucleos(token);
            call.enqueue(new Callback<List<Nucleo>>() {
                @Override
                public void onResponse(Call<List<Nucleo>> call, Response<List<Nucleo>> response) {
                    if (response.isSuccessful() && response.body() != null) {
                        // Limpar lista atual
                        listaNucleos.clear();

                        // Adicionar placeholder
                        Nucleo placeholderNucleo = new Nucleo();
                        placeholderNucleo.setId(0);
                        placeholderNucleo.setNome("Selecione o núcleo");
                        listaNucleos.add(placeholderNucleo);

                        // Adicionar os núcleos recebidos
                        List<Nucleo> nucleosRecebidos = response.body();
                        Log.d(TAG, "Núcleos recebidos: " + nucleosRecebidos.size());

                        // Filtrar núcleos pelo município
                        List<Nucleo> nucleosFiltrados = new ArrayList<>();
                        for (Nucleo nucleo : nucleosRecebidos) {
                            if (nucleo.getMunicipioId() == municipioId) {
                                nucleosFiltrados.add(nucleo);
                                Log.d(TAG, "Núcleo filtrado: ID=" + nucleo.getId() + ", Nome=" + nucleo.getNome() + ", MunicípioID=" + nucleo.getMunicipioId());
                            }
                        }

                        Log.d(TAG, "Núcleos filtrados para o município " + municipioId + ": " + nucleosFiltrados.size());

                        // Formatar os nomes dos núcleos (primeira letra maiúscula, resto minúscula)
                        for (Nucleo nucleo : nucleosFiltrados) {
                            if (nucleo.getNome() != null && !nucleo.getNome().isEmpty()) {
                                String nomeFormatado = formatarNome(nucleo.getNome());
                                nucleo.setNome(nomeFormatado);
                            }
                            Log.d(TAG, "Núcleo: ID=" + nucleo.getId() + ", Nome=" + nucleo.getNome() + ", MunicípioID=" + nucleo.getMunicipioId());
                        }

                        listaNucleos.addAll(nucleosFiltrados);

                        // Notificar o adapter sobre as mudanças
                        nucleoAdapter.notifyDataSetChanged();

                        // Selecionar núcleo se já estiver definido
                        if (getActivity() instanceof NovaOrdemActivity) {
                            NovaOrdemActivity activity = (NovaOrdemActivity) getActivity();
                            if (activity.getNucleoId() > 0) {
                                // Procurar pelo ID do núcleo
                                for (int i = 0; i < listaNucleos.size(); i++) {
                                    if (listaNucleos.get(i).getId() == activity.getNucleoId()) {
                                        spinnerNucleo.setSelection(i, true);
                                        Log.d(TAG, "Selecionando núcleo na posição " + i + ": " + listaNucleos.get(i).getNome());
                                        break;
                                    }
                                }
                            }
                        }

                        Log.d(TAG, "Núcleos carregados: " + listaNucleos.size());
                    } else {
                        // Exibir mensagem de erro
                        String errorMessage = "Erro ao carregar núcleos";
                        if (response != null) {
                            errorMessage += " (" + response.code() + ")";
                        }
                        ToastUtils.showShortToast(TabLocalizacaoFragment.this, errorMessage);
                        Log.e(TAG, errorMessage);

                        // Esconder o spinner de núcleo e seu label em caso de erro
                        tvNucleoLabel.setVisibility(View.GONE);
                        spinnerNucleo.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onFailure(Call<List<Nucleo>> call, Throwable t) {
                    // Exibir mensagem de erro
                    String errorMessage = "Falha ao carregar núcleos";
                    if (t != null && t.getMessage() != null) {
                        errorMessage += ": " + t.getMessage();
                    }
                    ToastUtils.showShortToast(TabLocalizacaoFragment.this, errorMessage);
                    Log.e(TAG, errorMessage, t);

                    // Esconder o spinner de núcleo e seu label em caso de erro
                    tvNucleoLabel.setVisibility(View.GONE);
                    spinnerNucleo.setVisibility(View.GONE);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Erro ao carregar núcleos: " + e.getMessage(), e);
            Toast.makeText(requireContext(), "Erro ao carregar núcleos: " + e.getMessage(), Toast.LENGTH_SHORT).show();

            // Esconder o spinner de núcleo e seu label em caso de erro
            tvNucleoLabel.setVisibility(View.GONE);
            spinnerNucleo.setVisibility(View.GONE);
        }
    }

    /**
     * Formata um nome para ter a primeira letra de cada palavra maiúscula e as demais minúsculas
     *
     * @param nome Nome a ser formatado
     * @return Nome formatado
     */
    private String formatarNome(String nome) {
        if (nome == null || nome.isEmpty()) {
            return nome;
        }

        // Converter para minúsculas primeiro
        nome = nome.toLowerCase();

        // Dividir em palavras
        String[] palavras = nome.split(" ");
        StringBuilder resultado = new StringBuilder();

        for (String palavra : palavras) {
            if (!palavra.isEmpty()) {
                // Primeira letra maiúscula, resto minúscula
                resultado.append(Character.toUpperCase(palavra.charAt(0)))
                        .append(palavra.substring(1))
                        .append(" ");
            }
        }

        return resultado.toString().trim();
    }
}
