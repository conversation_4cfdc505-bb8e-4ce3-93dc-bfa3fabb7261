package com.baramaia.hercules;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.baramaia.hercules.models.Motivo;

import java.util.List;

public class MotivosAdapter extends RecyclerView.Adapter<MotivosAdapter.ViewHolder> {

    private List<Motivo> motivosList;
    private int selectedPosition = -1; // Posição do item selecionado

    public MotivosAdapter(List<Motivo> motivosList) {
        this.motivosList = motivosList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.row_motivos, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Motivo motivo = motivosList.get(position);
        holder.radioMotivo.setText(motivo.getMot_descricao());

        // Define o estado do RadioButton
        holder.radioMotivo.setChecked(position == selectedPosition);

        // Evento de clique para definir a seleção
        holder.radioMotivo.setOnClickListener(v -> {
            selectedPosition = holder.getAdapterPosition();
            notifyDataSetChanged(); // Atualiza a lista para refletir a seleção única
        });
    }

    @Override
    public int getItemCount() {
        return motivosList.size();
    }

    // Retorna o item selecionado
    public Motivo getSelectedMotivo() {
        if (selectedPosition != -1) {
            return motivosList.get(selectedPosition);
        }
        return null;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public RadioButton radioMotivo;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            radioMotivo = itemView.findViewById(R.id.radioMotivo);
        }
    }
}
