<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/groupStyleblue_gray_50"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <!-- Header <PERSON><PERSON><PERSON> pad<PERSON><PERSON> da Nova Ordem -->
    <LinearLayout
        android:id="@+id/headerLayout"
        style="@style/groupStyleblue_gray_50"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingTop="16dp"
        android:paddingEnd="16dp"
        android:paddingBottom="16dp">

        <ImageView
            android:id="@+id/imageArrowleftOne"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Voltar"
            android:padding="4dp"
            android:src="@drawable/img_arrow_left" />

        <TextView
            android:id="@+id/txtTitle"
            style="@style/txtRobotoregular22"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="@string/msg_visita_n_o_efetivada"
            tools:text="Visita Não Efetivada" />
    </LinearLayout>
    <!-- Conteúdo com ScrollView e padding para botão fixo -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:fillViewport="true"
        android:paddingBottom="80dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">
            <!-- Card para Motivos -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:orientation="vertical">
                <!-- Título da seção melhorado -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/txtRobotoregular20"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/lbl_motivo"
                        android:textColor="@color/blue_gray_800" />
                    <!-- Indicador de obrigatório -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="*"
                        android:textColor="@color/red_500"
                        android:textSize="18sp"
                        android:textStyle="bold" />
                </LinearLayout>
                <!-- Card para o RecyclerView de motivos -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">
                        <!-- RecyclerView para motivos -->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/row_motivos"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:nestedScrollingEnabled="false" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </LinearLayout>
            <!-- Card para Observação -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="vertical">
                <!-- Título da seção -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/txtRobotoregular20"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/lbl_observa_o"
                        android:textColor="@color/blue_gray_800" />
                </LinearLayout>
                <!-- Card para observação -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">
                        <!-- Campo de observação -->
                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Digite observações sobre a não efetivação..."
                            app:boxStrokeColor="@color/custom_border_color"
                            app:boxStrokeWidth="1dp"
                            app:boxStrokeWidthFocused="2dp"
                            app:counterEnabled="true"
                            app:counterMaxLength="500"
                            app:hintTextColor="@color/custom_border_color">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edtObsercacao"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="top"
                                android:inputType="textMultiLine|textCapSentences"
                                android:maxLength="500"
                                android:maxLines="8"
                                android:minLines="4"
                                android:textColor="@color/blue_gray_800" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </LinearLayout>
            <!-- Seção de Foto -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <!-- Título da seção -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/txtRobotoregular20"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Foto:"
                        android:textColor="@color/blue_gray_800" />
                </LinearLayout>
                <!-- Card para foto -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">
                        <!-- Área da foto -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical">
                            <!-- Botão para tirar foto -->
                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnIconizercamera"
                                android:layout_width="match_parent"
                                android:layout_height="56dp"
                                android:text="TIRAR FOTO"
                                android:textAllCaps="false"
                                android:textColor="@color/white"
                                android:textSize="14sp"
                                app:backgroundTint="@color/custom_border_color"
                                app:cornerRadius="8dp"
                                app:icon="@drawable/img_iconizer_camera"
                                app:iconGravity="textStart"
                                app:iconSize="20dp"
                                app:iconTint="@color/white" />
                            <!-- Preview da foto -->
                            <androidx.cardview.widget.CardView
                                android:id="@+id/cardFoto"
                                android:layout_width="120dp"
                                android:layout_height="120dp"
                                android:layout_marginTop="16dp"
                                android:visibility="gone"
                                app:cardCornerRadius="8dp"
                                app:cardElevation="4dp">

                                <ImageView
                                    android:id="@+id/thumbnailFoto"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:contentDescription="Foto comprobatória"
                                    android:scaleType="centerCrop" />
                                <!-- Botão de remover foto -->
                                <ImageView
                                    android:id="@+id/btnRemoverFoto"
                                    android:layout_width="28dp"
                                    android:layout_height="28dp"
                                    android:layout_gravity="top|end"
                                    android:layout_margin="4dp"
                                    android:background="@drawable/ic_close_circle"
                                    android:contentDescription="Remover foto"
                                    android:visibility="gone" />
                            </androidx.cardview.widget.CardView>
                            <!-- Dica para o usuário -->
                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp"
                                android:background="@drawable/rectangle_bg_gray_50_radius_12"
                                android:gravity="center"
                                android:padding="8dp"
                                android:text="💡 Tire uma foto que comprove o motivo da não efetivação (ex: portão fechado, ausência do morador, etc.)"
                                android:textColor="@color/blue_gray_800"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <!-- Botão Finalizar seguindo padrão -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="8dp">

        <com.github.rygelouv.androidloadingbuttonlib.LoadingButton
            android:id="@+id/btnFinalizar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_margin="16dp"

            custom:background="@drawable/button_blue_gray_rounded"
            custom:cornerRadius="8dp"
            custom:progressColor="@android:color/white"
            custom:text="@string/msg_proximo"
            custom:textColor="@android:color/white"
            custom:textSize="16sp" />
    </FrameLayout>
</LinearLayout>