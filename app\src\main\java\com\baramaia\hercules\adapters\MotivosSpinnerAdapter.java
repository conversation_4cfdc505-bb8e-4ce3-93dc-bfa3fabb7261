package com.baramaia.hercules.adapters;

import android.content.Context;
import android.graphics.Color;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.baramaia.hercules.models.MotivoAvulso;

import java.util.List;

/**
 * Adapter específico para o spinner de motivos
 */
public class MotivosSpinnerAdapter extends ArrayAdapter<MotivoAvulso> {

    private static final String TAG = "MotivosSpinnerAdapter";

    public MotivosSpinnerAdapter(@NonNull Context context, int resource, @NonNull List<MotivoAvulso> motivos) {
        super(context, resource, motivos);
        Log.d(TAG, "Adapter criado com " + motivos.size() + " motivos");
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        Log.d(TAG, "getView chamado para posição " + position);

        View view = super.getView(position, convertView, parent);
        TextView textView = (TextView) view;

        MotivoAvulso motivo = getItem(position);
        if (motivo != null) {
            // Verificar se a descrição não é nula
            String descricao = motivo.getDescricao();
            if (descricao == null) {
                descricao = "Motivo " + position;
                Log.w(TAG, "Descrição nula para motivo na posição " + position + ", usando valor padrão");
            }

            textView.setText(descricao);
            Log.d(TAG, "Texto definido: " + descricao);

            // Garantir que o texto seja visível
            if (position == 0) {
                // Placeholder em cinza claro
                textView.setTextColor(Color.parseColor("#BDBDBD")); // Cinza claro
            } else {
                // Outros itens em preto
                textView.setTextColor(Color.BLACK);
            }
            textView.setTextSize(16);
        }

        return view;
    }

    @Override
    public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        Log.d(TAG, "getDropDownView chamado para posição " + position);

        View view = super.getDropDownView(position, convertView, parent);
        TextView textView = (TextView) view;

        MotivoAvulso motivo = getItem(position);
        if (motivo != null) {
            // Verificar se a descrição não é nula
            String descricao = motivo.getDescricao();
            if (descricao == null) {
                descricao = "Motivo " + position;
                Log.w(TAG, "Descrição nula para motivo na posição " + position + ", usando valor padrão");
            }

            textView.setText(descricao);
            Log.d(TAG, "Texto dropdown definido: " + descricao);

            // Garantir que o texto seja visível
            if (position == 0) {
                // Placeholder em cinza claro
                textView.setTextColor(Color.parseColor("#BDBDBD")); // Cinza claro
            } else {
                // Outros itens em preto
                textView.setTextColor(Color.BLACK);
            }
            textView.setTextSize(16);
            textView.setPadding(16, 16, 16, 16);
            view.setBackgroundColor(Color.WHITE);
        }

        return view;
    }
}
