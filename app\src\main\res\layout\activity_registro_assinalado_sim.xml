<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blue_gray_50"
    android:orientation="vertical">

    <!-- Head<PERSON> -->
    <LinearLayout
        android:id="@+id/headerLayout"
        style="@style/groupStyleblue_gray_50"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingTop="16dp"
        android:paddingEnd="16dp"
        android:paddingBottom="16dp">

        <ImageView
            android:id="@+id/imgBack"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:contentDescription="Voltar"
            android:padding="4dp"
            android:src="@drawable/img_arrow_left" />

        <TextView
            android:id="@+id/txtTitle"
            style="@style/txtRobotoregular22"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="@string/msg_visita_efetivada"
            android:textColor="@color/blue_gray_800" />
    </LinearLayout>
    <!-- Conteúdo -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:fillViewport="true"
        android:paddingBottom="80dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Seção: Equipamentos Retirados -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingTop="16dp"
                android:paddingEnd="16dp"
                android:paddingBottom="8dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_close_circle"
                    android:tint="@color/custom_border_color" />

                <TextView
                    style="@style/txtRobotoregular20"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Equipamentos Retirados"
                    android:textColor="@color/blue_gray_800" />
            </LinearLayout>

            <!-- Card Retirada -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/rectangle_bg_white_a700_radius_12"
                android:elevation="2dp"
                android:orientation="vertical"
                android:paddingStart="16dp"
                android:paddingTop="16dp"
                android:paddingEnd="16dp"
                android:paddingBottom="16dp">

                <TextView
                    android:id="@+id/txtFoiretirado"
                    style="@style/txtRobotoregular18"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/msg_foi_retirado_algum"
                    android:textColor="@color/blue_gray_800" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/txtRetiradoStatus"
                        style="@style/txtRobotoregular16"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Não"
                        android:textColor="@color/gray_600" />

                    <com.google.android.material.materialswitch.MaterialSwitch
                        android:id="@+id/switchRetirado"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:thumbTint="@color/switch_thumb_selector"
                        app:trackTint="@color/switch_track_selector" />
                </LinearLayout>

                <!-- Container para itens retirados -->
                <LinearLayout
                    android:id="@+id/layEquipRetirado"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical"
                    android:visibility="gone" />

                <!-- Botão de adicionar equipamento retirado -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnAdicionarEquipamento"
                    style="@style/Widget.Material3.Button.TextButton.Icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="16dp"
                    android:text="Adicionar equipamento"
                    android:textAllCaps="false"
                    android:textColor="@color/custom_border_color"
                    android:visibility="gone"
                    app:icon="@drawable/plusicon"
                    app:iconTint="@color/custom_border_color" />
            </LinearLayout>

            <!-- Divisor -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="12dp"
                android:alpha="0.5"
                android:background="@color/gray_300_01" />

            <!-- Seção: Equipamentos Instalados -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingTop="8dp"
                android:paddingEnd="16dp"
                android:paddingBottom="8dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_check_circle"
                    android:tint="@color/custom_border_color" />

                <TextView
                    style="@style/txtRobotoregular20"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Equipamentos Instalados"
                    android:textColor="@color/blue_gray_800" />
            </LinearLayout>

            <!-- Card Instalação -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/rectangle_bg_white_a700_radius_12"
                android:elevation="2dp"
                android:orientation="vertical"
                android:paddingStart="16dp"
                android:paddingTop="16dp"
                android:paddingEnd="16dp"
                android:paddingBottom="16dp">

                <TextView
                    android:id="@+id/txtFoiinstalado"
                    style="@style/txtRobotoregular18"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/msg_foi_instalado_algum"
                    android:textColor="@color/blue_gray_800" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/txtInstaladoStatus"
                        style="@style/txtRobotoregular16"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Não"
                        android:textColor="@color/gray_600" />

                    <com.google.android.material.materialswitch.MaterialSwitch
                        android:id="@+id/switchInstalado"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:thumbTint="@color/switch_thumb_selector"
                        app:trackTint="@color/switch_track_selector" />
                </LinearLayout>

                <!-- Container para itens instalados -->
                <LinearLayout
                    android:id="@+id/layEquipInstalado"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical"
                    android:visibility="gone" />

                <!-- Botão de adicionar equipamento instalado -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnAdicionarEquipamentoInstalado"
                    style="@style/Widget.Material3.Button.TextButton.Icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="16dp"
                    android:text="Adicionar equipamento"
                    android:textAllCaps="false"
                    android:textColor="@color/custom_border_color"
                    android:visibility="gone"
                    app:icon="@drawable/plusicon"
                    app:iconTint="@color/custom_border_color" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <!-- Botão Fixo -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/blue_gray_50"
        android:elevation="4dp"
        android:paddingStart="16dp"
        android:paddingTop="16dp"
        android:paddingEnd="16dp"
        android:paddingBottom="16dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnProxima"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="@string/msg_finalizar_registro"
            android:textAllCaps="false"
            android:textColor="@color/white"
            app:backgroundTint="@color/custom_border_color"
            app:cornerRadius="8dp" />
    </LinearLayout>
</LinearLayout>