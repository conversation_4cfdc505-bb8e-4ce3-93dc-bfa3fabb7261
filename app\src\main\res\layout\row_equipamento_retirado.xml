<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/linearStackedcard"
    style="@style/groupStylegray_50cornerRadius"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/layEquipRetirado"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10sp"
        android:orientation="vertical"
        android:padding="@dimen/_8pxh">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16pxv"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:weightSum="2">
            <!-- Spinner -->
            <Spinner
                android:id="@+id/spinnerOptions"
                android:layout_width="230dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/spinner_background"
                android:padding="@dimen/_8pxh"
                android:spinnerMode="dropdown"
                tools:entries="@array/options_array" />
            <!-- CardView para a miniatura -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardFotoRetirado"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginStart="@dimen/_8pxh"
                android:visibility="gone"
                app:cardCornerRadius="8dp"
                app:cardBackgroundColor="@android:color/white"
                app:cardElevation="2dp">
                <ImageView
                    android:id="@+id/thumbnailFotoRetirado"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:background="@drawable/rounded_corner" />
            </androidx.cardview.widget.CardView>
            <!-- ImageButton (com ícone) -->
            <ImageButton
                android:id="@+id/btnIconizercamera"
                android:layout_width="@dimen/_110pxh"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/_8pxh"
                android:background="@drawable/rectangle_bg_blue_gray_800_radius_9"
                android:paddingHorizontal="@dimen/_1pxh"
                android:paddingVertical="@dimen/_8pxv"
                android:src="@drawable/img_iconizer_camera"
                tools:ignore="ContentDescription" />
        </LinearLayout>
        <!-- Inputs -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12pxv"
            android:orientation="horizontal">
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:backgroundTint="@android:color/transparent"
                android:hint="@string/lbl_info_1"
                app:boxBackgroundColor="@android:color/transparent"
                app:boxBackgroundMode="outline"
                app:boxStrokeColor="@color/custom_border_color">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etInfoCounter"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:inputType="text"
                    tools:text="@string/lbl_placeholder" />
            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8pxh"
                android:layout_weight="1"
                android:hint="@string/lbl_info_1"
                app:boxBackgroundColor="@android:color/transparent"
                app:boxBackgroundMode="outline"
                app:boxStrokeColor="@color/custom_border_color">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etInfoCounter1"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:inputType="text"
                    tools:text="@string/lbl_placeholder" />
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>