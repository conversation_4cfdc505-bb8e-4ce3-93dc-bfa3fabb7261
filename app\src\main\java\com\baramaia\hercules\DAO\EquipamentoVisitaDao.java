package com.baramaia.hercules.DAO;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.baramaia.hercules.models.EquipamentoVisita;

import java.util.List;

@Dao
public interface EquipamentoVisitaDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(EquipamentoVisita equipamento);

    @Query("SELECT * FROM EquipamentoVisita")
    List<EquipamentoVisita> getAll();

    @Delete
    void delete(EquipamentoVisita equipamento);

    @Query("DELETE FROM EquipamentoVisita")
    void deleteAll();
}
